#!/usr/bin/env python3
"""
Aria2自适应配置优化系统
根据实际运行数据动态调整aria2配置参数
"""

import os
import json
import requests
import time
import logging
import sqlite3
import shutil
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

# 配置
ARIA2_RPC_URL = "http://localhost:6800/jsonrpc"
ARIA2_SECRET = "aria2secret"
CONFIG_FILE = "/config/aria2.conf"
CONFIG_BACKUP_DIR = "/backup/aria2_configs"

# 自适应数据库
ADAPTIVE_DB_PATH = "/var/lib/aria2_adaptive.db"

@dataclass
class ConfigParameter:
    name: str
    current_value: Any
    min_value: Any
    max_value: Any
    step: Any
    metric_impact: str  # 影响的性能指标

@dataclass
class PerformanceSnapshot:
    timestamp: datetime
    download_success_rate: float
    avg_download_speed: float
    storage_efficiency: float
    cpu_usage: float
    memory_usage: float
    config_hash: str

class Aria2AdaptiveConfig:
    def __init__(self):
        self.setup_logging()
        self.setup_database()
        self.session = requests.Session()
        
        # 可调整的配置参数
        self.config_parameters = {
            "max-concurrent-downloads": ConfigParameter(
                "max-concurrent-downloads", 8, 3, 15, 1, "download_success_rate"
            ),
            "max-connection-per-server": ConfigParameter(
                "max-connection-per-server", 8, 4, 16, 2, "avg_download_speed"
            ),
            "split": ConfigParameter(
                "split", 8, 4, 16, 2, "avg_download_speed"
            ),
            "min-split-size": ConfigParameter(
                "min-split-size", "32M", "16M", "64M", "8M", "storage_efficiency"
            ),
            "disk-cache": ConfigParameter(
                "disk-cache", "128M", "64M", "256M", "32M", "avg_download_speed"
            ),
            "timeout": ConfigParameter(
                "timeout", 60, 30, 120, 15, "download_success_rate"
            ),
            "connect-timeout": ConfigParameter(
                "connect-timeout", 20, 10, 60, 10, "download_success_rate"
            ),
            "max-tries": ConfigParameter(
                "max-tries", 5, 3, 10, 1, "download_success_rate"
            )
        }
        
        # 性能基线
        self.performance_baseline = None
        self.optimization_history = []
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('/var/log/aria2_adaptive_config.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def setup_database(self):
        """设置自适应数据库"""
        try:
            os.makedirs(os.path.dirname(ADAPTIVE_DB_PATH), exist_ok=True)
            self.conn = sqlite3.connect(ADAPTIVE_DB_PATH, check_same_thread=False)
            
            # 创建性能快照表
            self.conn.execute('''
                CREATE TABLE IF NOT EXISTS performance_snapshots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    download_success_rate REAL,
                    avg_download_speed REAL,
                    storage_efficiency REAL,
                    cpu_usage REAL,
                    memory_usage REAL,
                    config_hash TEXT,
                    config_json TEXT
                )
            ''')
            
            # 创建优化记录表
            self.conn.execute('''
                CREATE TABLE IF NOT EXISTS optimization_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    parameter_name TEXT,
                    old_value TEXT,
                    new_value TEXT,
                    performance_before REAL,
                    performance_after REAL,
                    improvement REAL,
                    reverted BOOLEAN DEFAULT FALSE
                )
            ''')
            
            self.conn.commit()
            self.logger.info("自适应配置数据库初始化成功")
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")

    def call_aria2(self, method: str, params: List[Any] = None) -> Dict:
        """调用aria2 RPC接口"""
        if params is None:
            params = []
        
        payload = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": method,
            "params": [f"token:{ARIA2_SECRET}"] + params
        }
        
        try:
            response = self.session.post(ARIA2_RPC_URL, json=payload, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"Aria2 RPC调用失败: {e}")
            return {"error": str(e)}

    def get_current_config(self) -> Dict[str, str]:
        """获取当前aria2配置"""
        config = {}
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        config[key.strip()] = value.strip()
        except Exception as e:
            self.logger.error(f"读取配置文件失败: {e}")
        
        return config

    def update_config_file(self, updates: Dict[str, str]) -> bool:
        """更新配置文件"""
        try:
            # 备份当前配置
            self.backup_config()
            
            # 读取当前配置
            lines = []
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 更新配置
            updated_lines = []
            updated_keys = set()
            
            for line in lines:
                stripped = line.strip()
                if stripped and not stripped.startswith('#') and '=' in stripped:
                    key, value = stripped.split('=', 1)
                    key = key.strip()
                    if key in updates:
                        updated_lines.append(f"{key}={updates[key]}\n")
                        updated_keys.add(key)
                    else:
                        updated_lines.append(line)
                else:
                    updated_lines.append(line)
            
            # 添加新的配置项
            for key, value in updates.items():
                if key not in updated_keys:
                    updated_lines.append(f"{key}={value}\n")
            
            # 写入文件
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                f.writelines(updated_lines)
            
            self.logger.info(f"配置文件更新成功: {updates}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新配置文件失败: {e}")
            return False

    def backup_config(self):
        """备份配置文件"""
        try:
            os.makedirs(CONFIG_BACKUP_DIR, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{CONFIG_BACKUP_DIR}/aria2_{timestamp}.conf"
            shutil.copy2(CONFIG_FILE, backup_path)
            self.logger.info(f"配置已备份到: {backup_path}")
        except Exception as e:
            self.logger.error(f"备份配置失败: {e}")

    def restart_aria2(self) -> bool:
        """重启aria2服务"""
        try:
            import subprocess
            
            # 尝试使用docker重启
            result = subprocess.run(["docker", "restart", "aria2"], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.logger.info("Aria2 Docker容器重启成功")
                time.sleep(5)  # 等待服务启动
                return True
            else:
                # 尝试使用systemctl重启
                result = subprocess.run(["systemctl", "restart", "aria2"], 
                                      capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    self.logger.info("Aria2服务重启成功")
                    time.sleep(5)
                    return True
                    
        except Exception as e:
            self.logger.error(f"重启aria2失败: {e}")
        
        return False    def collect_performance_metrics(self) -> PerformanceSnapshot:
        """收集性能指标"""
        try:
            # 获取aria2统计信息
            global_stat = self.call_aria2("aria2.getGlobalStat")
            stopped_tasks = self.call_aria2("aria2.tellStopped", [0, 100])
            
            # 计算下载成功率
            download_success_rate = 0.0
            if "result" in stopped_tasks and stopped_tasks["result"]:
                tasks = stopped_tasks["result"]
                completed_count = sum(1 for task in tasks if task.get("status") == "complete")
                download_success_rate = (completed_count / len(tasks)) * 100
            
            # 计算平均下载速度
            avg_download_speed = 0.0
            if "result" in global_stat:
                avg_download_speed = float(global_stat["result"].get("downloadSpeed", 0)) / 1024  # KB/s
            
            # 计算存储效率
            storage_efficiency = 0.0
            try:
                stat = shutil.disk_usage("/downloads")
                storage_efficiency = (stat.free / stat.total) * 100
            except:
                pass
            
            # 获取系统资源使用率
            import psutil
            cpu_usage = psutil.cpu_percent(interval=1)
            memory_usage = psutil.virtual_memory().percent
            
            # 生成配置哈希
            current_config = self.get_current_config()
            config_hash = hash(json.dumps(current_config, sort_keys=True))
            
            return PerformanceSnapshot(
                timestamp=datetime.now(),
                download_success_rate=download_success_rate,
                avg_download_speed=avg_download_speed,
                storage_efficiency=storage_efficiency,
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                config_hash=str(config_hash)
            )
            
        except Exception as e:
            self.logger.error(f"收集性能指标失败: {e}")
            return None

    def store_performance_snapshot(self, snapshot: PerformanceSnapshot):
        """存储性能快照"""
        try:
            current_config = self.get_current_config()
            config_json = json.dumps(current_config)
            
            self.conn.execute('''
                INSERT INTO performance_snapshots 
                (download_success_rate, avg_download_speed, storage_efficiency, 
                 cpu_usage, memory_usage, config_hash, config_json)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                snapshot.download_success_rate,
                snapshot.avg_download_speed,
                snapshot.storage_efficiency,
                snapshot.cpu_usage,
                snapshot.memory_usage,
                snapshot.config_hash,
                config_json
            ))
            self.conn.commit()
            
        except Exception as e:
            self.logger.error(f"存储性能快照失败: {e}")

    def calculate_performance_score(self, snapshot: PerformanceSnapshot) -> float:
        """计算综合性能得分"""
        # 权重配置
        weights = {
            "download_success_rate": 0.4,  # 成功率最重要
            "avg_download_speed": 0.3,     # 速度次之
            "storage_efficiency": 0.2,     # 存储效率
            "system_efficiency": 0.1       # 系统资源效率
        }
        
        # 归一化指标
        success_score = min(snapshot.download_success_rate / 100, 1.0)
        speed_score = min(snapshot.avg_download_speed / 1000, 1.0)  # 1MB/s为满分
        storage_score = snapshot.storage_efficiency / 100
        system_score = 1.0 - ((snapshot.cpu_usage + snapshot.memory_usage) / 200)
        
        # 计算加权得分
        total_score = (
            success_score * weights["download_success_rate"] +
            speed_score * weights["avg_download_speed"] +
            storage_score * weights["storage_efficiency"] +
            system_score * weights["system_efficiency"]
        )
        
        return total_score

    def get_optimization_candidates(self) -> List[Tuple[str, str, Any]]:
        """获取优化候选参数"""
        candidates = []
        
        # 获取最近的性能数据
        recent_snapshots = self.get_recent_performance_data(hours=24)
        if len(recent_snapshots) < 5:
            self.logger.info("性能数据不足，跳过优化")
            return candidates
        
        current_config = self.get_current_config()
        
        # 分析每个参数的优化潜力
        for param_name, param_config in self.config_parameters.items():
            if param_name not in current_config:
                continue
                
            current_value = current_config[param_name]
            
            # 根据性能指标决定调整方向
            metric_impact = param_config.metric_impact
            recent_performance = sum(getattr(s, metric_impact, 0) for s in recent_snapshots[-5:]) / 5
            
            # 如果性能指标低于期望，尝试调整参数
            if self.should_optimize_parameter(param_name, recent_performance):
                new_value = self.calculate_new_value(param_config, current_value, recent_performance)
                if new_value != current_value:
                    candidates.append((param_name, current_value, new_value))
        
        return candidates

    def should_optimize_parameter(self, param_name: str, recent_performance: float) -> bool:
        """判断是否应该优化参数"""
        thresholds = {
            "download_success_rate": 80.0,  # 成功率低于80%
            "avg_download_speed": 200.0,    # 速度低于200KB/s
            "storage_efficiency": 20.0      # 可用空间低于20%
        }
        
        param_config = self.config_parameters[param_name]
        metric = param_config.metric_impact
        threshold = thresholds.get(metric, 0)
        
        return recent_performance < threshold

    def calculate_new_value(self, param_config: ConfigParameter, current_value: str, performance: float) -> str:
        """计算新的参数值"""
        try:
            if param_config.name in ["min-split-size", "disk-cache"]:
                # 处理带单位的值
                current_num = int(current_value.rstrip('MmGgKk'))
                unit = current_value[-1].upper()
                
                step_num = int(param_config.step.rstrip('MmGgKk'))
                min_num = int(param_config.min_value.rstrip('MmGgKk'))
                max_num = int(param_config.max_value.rstrip('MmGgKk'))
                
                # 根据性能决定增减
                if performance < 50:  # 性能较差，尝试增加
                    new_num = min(current_num + step_num, max_num)
                else:  # 性能尚可，尝试减少以节省资源
                    new_num = max(current_num - step_num, min_num)
                
                return f"{new_num}{unit}"
                
            else:
                # 处理数值型参数
                current_num = int(current_value)
                step_num = param_config.step
                min_num = param_config.min_value
                max_num = param_config.max_value
                
                if performance < 50:
                    new_num = min(current_num + step_num, max_num)
                else:
                    new_num = max(current_num - step_num, min_num)
                
                return str(new_num)
                
        except Exception as e:
            self.logger.error(f"计算新参数值失败: {e}")
            return current_value

    def apply_optimization(self, param_name: str, old_value: str, new_value: str) -> bool:
        """应用优化"""
        try:
            # 记录优化前的性能
            before_snapshot = self.collect_performance_metrics()
            if not before_snapshot:
                return False
                
            performance_before = self.calculate_performance_score(before_snapshot)
            
            # 更新配置
            if not self.update_config_file({param_name: new_value}):
                return False
            
            # 重启aria2
            if not self.restart_aria2():
                self.logger.error("重启aria2失败，回滚配置")
                self.update_config_file({param_name: old_value})
                return False
            
            # 等待系统稳定
            time.sleep(30)
            
            # 记录优化后的性能
            after_snapshot = self.collect_performance_metrics()
            if not after_snapshot:
                return False
                
            performance_after = self.calculate_performance_score(after_snapshot)
            improvement = performance_after - performance_before
            
            # 记录优化历史
            self.conn.execute('''
                INSERT INTO optimization_history 
                (parameter_name, old_value, new_value, performance_before, performance_after, improvement)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (param_name, old_value, new_value, performance_before, performance_after, improvement))
            self.conn.commit()
            
            # 如果性能下降，回滚配置
            if improvement < -0.05:  # 性能下降超过5%
                self.logger.warning(f"优化效果不佳，回滚参数 {param_name}: {new_value} -> {old_value}")
                self.update_config_file({param_name: old_value})
                self.restart_aria2()
                
                # 标记为已回滚
                self.conn.execute('''
                    UPDATE optimization_history 
                    SET reverted = TRUE 
                    WHERE parameter_name = ? AND new_value = ? 
                    ORDER BY timestamp DESC LIMIT 1
                ''', (param_name, new_value))
                self.conn.commit()
                
                return False
            else:
                self.logger.info(f"优化成功: {param_name} {old_value} -> {new_value}, 性能提升: {improvement:.3f}")
                return True
                
        except Exception as e:
            self.logger.error(f"应用优化失败: {e}")
            return False

    def get_recent_performance_data(self, hours: int = 24) -> List[PerformanceSnapshot]:
        """获取最近的性能数据"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            cursor = self.conn.execute('''
                SELECT download_success_rate, avg_download_speed, storage_efficiency, 
                       cpu_usage, memory_usage, config_hash, timestamp
                FROM performance_snapshots 
                WHERE timestamp > ? 
                ORDER BY timestamp ASC
            ''', (cutoff_time,))
            
            snapshots = []
            for row in cursor.fetchall():
                snapshot = PerformanceSnapshot(
                    timestamp=datetime.fromisoformat(row[6]),
                    download_success_rate=row[0],
                    avg_download_speed=row[1],
                    storage_efficiency=row[2],
                    cpu_usage=row[3],
                    memory_usage=row[4],
                    config_hash=row[5]
                )
                snapshots.append(snapshot)
            
            return snapshots
            
        except Exception as e:
            self.logger.error(f"获取性能数据失败: {e}")
            return []

    def run_adaptive_optimization(self):
        """运行自适应优化"""
        self.logger.info("开始自适应配置优化")
        
        try:
            # 收集当前性能指标
            current_snapshot = self.collect_performance_metrics()
            if not current_snapshot:
                self.logger.error("无法收集性能指标")
                return
            
            # 存储性能快照
            self.store_performance_snapshot(current_snapshot)
            
            # 获取优化候选
            candidates = self.get_optimization_candidates()
            if not candidates:
                self.logger.info("暂无需要优化的参数")
                return
            
            # 应用优化（一次只优化一个参数）
            for param_name, old_value, new_value in candidates[:1]:  # 只取第一个候选
                self.logger.info(f"尝试优化参数: {param_name} {old_value} -> {new_value}")
                
                if self.apply_optimization(param_name, old_value, new_value):
                    self.logger.info("优化成功，等待下次周期继续优化")
                    break
                else:
                    self.logger.warning("优化失败，尝试下一个参数")
            
        except Exception as e:
            self.logger.error(f"自适应优化失败: {e}")

    def generate_optimization_report(self) -> str:
        """生成优化报告"""
        try:
            # 获取最近的优化历史
            cursor = self.conn.execute('''
                SELECT parameter_name, old_value, new_value, improvement, reverted, timestamp
                FROM optimization_history 
                WHERE timestamp > datetime('now', '-7 days')
                ORDER BY timestamp DESC
            ''')
            
            report = "📈 **Aria2自适应优化报告**\n\n"
            
            optimizations = cursor.fetchall()
            if not optimizations:
                report += "最近7天内无优化记录\n"
                return report
            
            successful_count = sum(1 for opt in optimizations if not opt[4])  # 未回滚的
            total_improvement = sum(opt[3] for opt in optimizations if not opt[4])
            
            report += f"📊 **优化统计**\n"
            report += f"总优化次数: {len(optimizations)}\n"
            report += f"成功优化: {successful_count}\n"
            report += f"累计性能提升: {total_improvement:.3f}\n\n"
            
            report += f"🔧 **最近优化记录**\n"
            for opt in optimizations[:5]:  # 显示最近5次
                status = "❌ 已回滚" if opt[4] else "✅ 成功"
                report += f"{opt[5][:16]} - {opt[0]}: {opt[1]}→{opt[2]} ({opt[3]:+.3f}) {status}\n"
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成优化报告失败: {e}")
            return "生成报告失败"

if __name__ == "__main__":
    import sys
    
    optimizer = Aria2AdaptiveConfig()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "optimize":
            # 执行一次优化
            optimizer.run_adaptive_optimization()
        elif command == "monitor":
            # 持续监控和优化
            interval_hours = int(sys.argv[2]) if len(sys.argv) > 2 else 6
            optimizer.logger.info(f"启动自适应优化监控，间隔: {interval_hours} 小时")
            
            while True:
                try:
                    optimizer.run_adaptive_optimization()
                    time.sleep(interval_hours * 3600)
                except KeyboardInterrupt:
                    optimizer.logger.info("收到停止信号，退出监控")
                    break
                except Exception as e:
                    optimizer.logger.error(f"监控循环出错: {e}")
                    time.sleep(3600)  # 出错后等待1小时
        elif command == "report":
            # 生成优化报告
            report = optimizer.generate_optimization_report()
            print(report)
        elif command == "snapshot":
            # 收集性能快照
            snapshot = optimizer.collect_performance_metrics()
            if snapshot:
                optimizer.store_performance_snapshot(snapshot)
                score = optimizer.calculate_performance_score(snapshot)
                print(f"性能快照已保存，综合得分: {score:.3f}")
        else:
            print("用法:")
            print("  python3 aria2_adaptive_config.py optimize           # 执行一次优化")
            print("  python3 aria2_adaptive_config.py monitor [小时]     # 持续监控优化")
            print("  python3 aria2_adaptive_config.py report             # 生成优化报告")
            print("  python3 aria2_adaptive_config.py snapshot           # 收集性能快照")
    else:
        # 默认执行一次优化
        optimizer.run_adaptive_optimization()