/* Generated by Cython 0.29.4 */

/* BEGIN: Cython Metadata
{
    "distutils": {
        "name": "grapheme.cython.finder",
        "sources": [
            "grapheme/cython/finder.pyx"
        ]
    },
    "module_name": "grapheme.cython.finder"
}
END: Cython Metadata */

#define PY_SSIZE_T_CLEAN
#include "Python.h"
#ifndef Py_PYTHON_H
    #error Python headers needed to compile C extensions, please install development version of Python.
#elif PY_VERSION_HEX < 0x02060000 || (0x03000000 <= PY_VERSION_HEX && PY_VERSION_HEX < 0x03030000)
    #error Cython requires Python 2.6+ or Python 3.3+.
#else
#define CYTHON_ABI "0_29_4"
#define CYTHON_HEX_VERSION 0x001D04F0
#define CYTHON_FUTURE_DIVISION 1
#include <stddef.h>
#ifndef offsetof
  #define offsetof(type, member) ( (size_t) & ((type*)0) -> member )
#endif
#if !defined(WIN32) && !defined(MS_WINDOWS)
  #ifndef __stdcall
    #define __stdcall
  #endif
  #ifndef __cdecl
    #define __cdecl
  #endif
  #ifndef __fastcall
    #define __fastcall
  #endif
#endif
#ifndef DL_IMPORT
  #define DL_IMPORT(t) t
#endif
#ifndef DL_EXPORT
  #define DL_EXPORT(t) t
#endif
#define __PYX_COMMA ,
#ifndef HAVE_LONG_LONG
  #if PY_VERSION_HEX >= 0x02070000
    #define HAVE_LONG_LONG
  #endif
#endif
#ifndef PY_LONG_LONG
  #define PY_LONG_LONG LONG_LONG
#endif
#ifndef Py_HUGE_VAL
  #define Py_HUGE_VAL HUGE_VAL
#endif
#ifdef PYPY_VERSION
  #define CYTHON_COMPILING_IN_PYPY 1
  #define CYTHON_COMPILING_IN_PYSTON 0
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #undef CYTHON_USE_TYPE_SLOTS
  #define CYTHON_USE_TYPE_SLOTS 0
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #if PY_VERSION_HEX < 0x03050000
    #undef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 0
  #elif !defined(CYTHON_USE_ASYNC_SLOTS)
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #undef CYTHON_USE_UNICODE_INTERNALS
  #define CYTHON_USE_UNICODE_INTERNALS 0
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #undef CYTHON_AVOID_BORROWED_REFS
  #define CYTHON_AVOID_BORROWED_REFS 1
  #undef CYTHON_ASSUME_SAFE_MACROS
  #define CYTHON_ASSUME_SAFE_MACROS 0
  #undef CYTHON_UNPACK_METHODS
  #define CYTHON_UNPACK_METHODS 0
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #undef CYTHON_PEP489_MULTI_PHASE_INIT
  #define CYTHON_PEP489_MULTI_PHASE_INIT 0
  #undef CYTHON_USE_TP_FINALIZE
  #define CYTHON_USE_TP_FINALIZE 0
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
#elif defined(PYSTON_VERSION)
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_PYSTON 1
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #undef CYTHON_USE_ASYNC_SLOTS
  #define CYTHON_USE_ASYNC_SLOTS 0
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #undef CYTHON_PEP489_MULTI_PHASE_INIT
  #define CYTHON_PEP489_MULTI_PHASE_INIT 0
  #undef CYTHON_USE_TP_FINALIZE
  #define CYTHON_USE_TP_FINALIZE 0
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
#else
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_PYSTON 0
  #define CYTHON_COMPILING_IN_CPYTHON 1
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #if PY_VERSION_HEX < 0x02070000
    #undef CYTHON_USE_PYTYPE_LOOKUP
    #define CYTHON_USE_PYTYPE_LOOKUP 0
  #elif !defined(CYTHON_USE_PYTYPE_LOOKUP)
    #define CYTHON_USE_PYTYPE_LOOKUP 1
  #endif
  #if PY_MAJOR_VERSION < 3
    #undef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 0
  #elif !defined(CYTHON_USE_ASYNC_SLOTS)
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #if PY_VERSION_HEX < 0x02070000
    #undef CYTHON_USE_PYLONG_INTERNALS
    #define CYTHON_USE_PYLONG_INTERNALS 0
  #elif !defined(CYTHON_USE_PYLONG_INTERNALS)
    #define CYTHON_USE_PYLONG_INTERNALS 1
  #endif
  #ifndef CYTHON_USE_PYLIST_INTERNALS
    #define CYTHON_USE_PYLIST_INTERNALS 1
  #endif
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #if PY_VERSION_HEX < 0x030300F0
    #undef CYTHON_USE_UNICODE_WRITER
    #define CYTHON_USE_UNICODE_WRITER 0
  #elif !defined(CYTHON_USE_UNICODE_WRITER)
    #define CYTHON_USE_UNICODE_WRITER 1
  #endif
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #ifndef CYTHON_FAST_THREAD_STATE
    #define CYTHON_FAST_THREAD_STATE 1
  #endif
  #ifndef CYTHON_FAST_PYCALL
    #define CYTHON_FAST_PYCALL 1
  #endif
  #ifndef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT (PY_VERSION_HEX >= 0x03050000)
  #endif
  #ifndef CYTHON_USE_TP_FINALIZE
    #define CYTHON_USE_TP_FINALIZE (PY_VERSION_HEX >= 0x030400a1)
  #endif
  #ifndef CYTHON_USE_DICT_VERSIONS
    #define CYTHON_USE_DICT_VERSIONS (PY_VERSION_HEX >= 0x030600B1)
  #endif
  #ifndef CYTHON_USE_EXC_INFO_STACK
    #define CYTHON_USE_EXC_INFO_STACK (PY_VERSION_HEX >= 0x030700A3)
  #endif
#endif
#if !defined(CYTHON_FAST_PYCCALL)
#define CYTHON_FAST_PYCCALL  (CYTHON_FAST_PYCALL && PY_VERSION_HEX >= 0x030600B1)
#endif
#if CYTHON_USE_PYLONG_INTERNALS
  #include "longintrepr.h"
  #undef SHIFT
  #undef BASE
  #undef MASK
  #ifdef SIZEOF_VOID_P
    enum { __pyx_check_sizeof_voidp = 1 / (int)(SIZEOF_VOID_P == sizeof(void*)) };
  #endif
#endif
#ifndef __has_attribute
  #define __has_attribute(x) 0
#endif
#ifndef __has_cpp_attribute
  #define __has_cpp_attribute(x) 0
#endif
#ifndef CYTHON_RESTRICT
  #if defined(__GNUC__)
    #define CYTHON_RESTRICT __restrict__
  #elif defined(_MSC_VER) && _MSC_VER >= 1400
    #define CYTHON_RESTRICT __restrict
  #elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
    #define CYTHON_RESTRICT restrict
  #else
    #define CYTHON_RESTRICT
  #endif
#endif
#ifndef CYTHON_UNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define CYTHON_UNUSED __attribute__ ((__unused__))
#   else
#     define CYTHON_UNUSED
#   endif
# elif defined(__ICC) || (defined(__INTEL_COMPILER) && !defined(_MSC_VER))
#   define CYTHON_UNUSED __attribute__ ((__unused__))
# else
#   define CYTHON_UNUSED
# endif
#endif
#ifndef CYTHON_MAYBE_UNUSED_VAR
#  if defined(__cplusplus)
     template<class T> void CYTHON_MAYBE_UNUSED_VAR( const T& ) { }
#  else
#    define CYTHON_MAYBE_UNUSED_VAR(x) (void)(x)
#  endif
#endif
#ifndef CYTHON_NCP_UNUSED
# if CYTHON_COMPILING_IN_CPYTHON
#  define CYTHON_NCP_UNUSED
# else
#  define CYTHON_NCP_UNUSED CYTHON_UNUSED
# endif
#endif
#define __Pyx_void_to_None(void_result) ((void)(void_result), Py_INCREF(Py_None), Py_None)
#ifdef _MSC_VER
    #ifndef _MSC_STDINT_H_
        #if _MSC_VER < 1300
           typedef unsigned char     uint8_t;
           typedef unsigned int      uint32_t;
        #else
           typedef unsigned __int8   uint8_t;
           typedef unsigned __int32  uint32_t;
        #endif
    #endif
#else
   #include <stdint.h>
#endif
#ifndef CYTHON_FALLTHROUGH
  #if defined(__cplusplus) && __cplusplus >= 201103L
    #if __has_cpp_attribute(fallthrough)
      #define CYTHON_FALLTHROUGH [[fallthrough]]
    #elif __has_cpp_attribute(clang::fallthrough)
      #define CYTHON_FALLTHROUGH [[clang::fallthrough]]
    #elif __has_cpp_attribute(gnu::fallthrough)
      #define CYTHON_FALLTHROUGH [[gnu::fallthrough]]
    #endif
  #endif
  #ifndef CYTHON_FALLTHROUGH
    #if __has_attribute(fallthrough)
      #define CYTHON_FALLTHROUGH __attribute__((fallthrough))
    #else
      #define CYTHON_FALLTHROUGH
    #endif
  #endif
  #if defined(__clang__ ) && defined(__apple_build_version__)
    #if __apple_build_version__ < 7000000
      #undef  CYTHON_FALLTHROUGH
      #define CYTHON_FALLTHROUGH
    #endif
  #endif
#endif

#ifndef CYTHON_INLINE
  #if defined(__clang__)
    #define CYTHON_INLINE __inline__ __attribute__ ((__unused__))
  #elif defined(__GNUC__)
    #define CYTHON_INLINE __inline__
  #elif defined(_MSC_VER)
    #define CYTHON_INLINE __inline
  #elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
    #define CYTHON_INLINE inline
  #else
    #define CYTHON_INLINE
  #endif
#endif

#if CYTHON_COMPILING_IN_PYPY && PY_VERSION_HEX < 0x02070600 && !defined(Py_OptimizeFlag)
  #define Py_OptimizeFlag 0
#endif
#define __PYX_BUILD_PY_SSIZE_T "n"
#define CYTHON_FORMAT_SSIZE_T "z"
#if PY_MAJOR_VERSION < 3
  #define __Pyx_BUILTIN_MODULE_NAME "__builtin__"
  #define __Pyx_PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\
          PyCode_New(a+k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)
  #define __Pyx_DefaultClassType PyClass_Type
#else
  #define __Pyx_BUILTIN_MODULE_NAME "builtins"
  #define __Pyx_PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\
          PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)
  #define __Pyx_DefaultClassType PyType_Type
#endif
#ifndef Py_TPFLAGS_CHECKTYPES
  #define Py_TPFLAGS_CHECKTYPES 0
#endif
#ifndef Py_TPFLAGS_HAVE_INDEX
  #define Py_TPFLAGS_HAVE_INDEX 0
#endif
#ifndef Py_TPFLAGS_HAVE_NEWBUFFER
  #define Py_TPFLAGS_HAVE_NEWBUFFER 0
#endif
#ifndef Py_TPFLAGS_HAVE_FINALIZE
  #define Py_TPFLAGS_HAVE_FINALIZE 0
#endif
#ifndef METH_STACKLESS
  #define METH_STACKLESS 0
#endif
#if PY_VERSION_HEX <= 0x030700A3 || !defined(METH_FASTCALL)
  #ifndef METH_FASTCALL
     #define METH_FASTCALL 0x80
  #endif
  typedef PyObject *(*__Pyx_PyCFunctionFast) (PyObject *self, PyObject *const *args, Py_ssize_t nargs);
  typedef PyObject *(*__Pyx_PyCFunctionFastWithKeywords) (PyObject *self, PyObject *const *args,
                                                          Py_ssize_t nargs, PyObject *kwnames);
#else
  #define __Pyx_PyCFunctionFast _PyCFunctionFast
  #define __Pyx_PyCFunctionFastWithKeywords _PyCFunctionFastWithKeywords
#endif
#if CYTHON_FAST_PYCCALL
#define __Pyx_PyFastCFunction_Check(func)\
    ((PyCFunction_Check(func) && (METH_FASTCALL == (PyCFunction_GET_FLAGS(func) & ~(METH_CLASS | METH_STATIC | METH_COEXIST | METH_KEYWORDS | METH_STACKLESS)))))
#else
#define __Pyx_PyFastCFunction_Check(func) 0
#endif
#if CYTHON_USE_DICT_VERSIONS
#define __PYX_GET_DICT_VERSION(dict)  (((PyDictObject*)(dict))->ma_version_tag)
#define __PYX_UPDATE_DICT_CACHE(dict, value, cache_var, version_var)\
    (version_var) = __PYX_GET_DICT_VERSION(dict);\
    (cache_var) = (value);
#define __PYX_PY_DICT_LOOKUP_IF_MODIFIED(VAR, DICT, LOOKUP) {\
        static PY_UINT64_T __pyx_dict_version = 0;\
        static PyObject *__pyx_dict_cached_value = NULL;\
        if (likely(__PYX_GET_DICT_VERSION(DICT) == __pyx_dict_version)) {\
            (VAR) = __pyx_dict_cached_value;\
        } else {\
            (VAR) = __pyx_dict_cached_value = (LOOKUP);\
            __pyx_dict_version = __PYX_GET_DICT_VERSION(DICT);\
        }\
    }
#else
#define __PYX_GET_DICT_VERSION(dict)  (0)
#define __PYX_UPDATE_DICT_CACHE(dict, value, cache_var, version_var)
#define __PYX_PY_DICT_LOOKUP_IF_MODIFIED(VAR, DICT, LOOKUP)  (VAR) = (LOOKUP);
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyObject_Malloc)
  #define PyObject_Malloc(s)   PyMem_Malloc(s)
  #define PyObject_Free(p)     PyMem_Free(p)
  #define PyObject_Realloc(p)  PyMem_Realloc(p)
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030400A1
  #define PyMem_RawMalloc(n)           PyMem_Malloc(n)
  #define PyMem_RawRealloc(p, n)       PyMem_Realloc(p, n)
  #define PyMem_RawFree(p)             PyMem_Free(p)
#endif
#if CYTHON_COMPILING_IN_PYSTON
  #define __Pyx_PyCode_HasFreeVars(co)  PyCode_HasFreeVars(co)
  #define __Pyx_PyFrame_SetLineNumber(frame, lineno) PyFrame_SetLineNumber(frame, lineno)
#else
  #define __Pyx_PyCode_HasFreeVars(co)  (PyCode_GetNumFree(co) > 0)
  #define __Pyx_PyFrame_SetLineNumber(frame, lineno)  (frame)->f_lineno = (lineno)
#endif
#if !CYTHON_FAST_THREAD_STATE || PY_VERSION_HEX < 0x02070000
  #define __Pyx_PyThreadState_Current PyThreadState_GET()
#elif PY_VERSION_HEX >= 0x03060000
  #define __Pyx_PyThreadState_Current _PyThreadState_UncheckedGet()
#elif PY_VERSION_HEX >= 0x03000000
  #define __Pyx_PyThreadState_Current PyThreadState_GET()
#else
  #define __Pyx_PyThreadState_Current _PyThreadState_Current
#endif
#if PY_VERSION_HEX < 0x030700A2 && !defined(PyThread_tss_create) && !defined(Py_tss_NEEDS_INIT)
#include "pythread.h"
#define Py_tss_NEEDS_INIT 0
typedef int Py_tss_t;
static CYTHON_INLINE int PyThread_tss_create(Py_tss_t *key) {
  *key = PyThread_create_key();
  return 0;
}
static CYTHON_INLINE Py_tss_t * PyThread_tss_alloc(void) {
  Py_tss_t *key = (Py_tss_t *)PyObject_Malloc(sizeof(Py_tss_t));
  *key = Py_tss_NEEDS_INIT;
  return key;
}
static CYTHON_INLINE void PyThread_tss_free(Py_tss_t *key) {
  PyObject_Free(key);
}
static CYTHON_INLINE int PyThread_tss_is_created(Py_tss_t *key) {
  return *key != Py_tss_NEEDS_INIT;
}
static CYTHON_INLINE void PyThread_tss_delete(Py_tss_t *key) {
  PyThread_delete_key(*key);
  *key = Py_tss_NEEDS_INIT;
}
static CYTHON_INLINE int PyThread_tss_set(Py_tss_t *key, void *value) {
  return PyThread_set_key_value(*key, value);
}
static CYTHON_INLINE void * PyThread_tss_get(Py_tss_t *key) {
  return PyThread_get_key_value(*key);
}
#endif
#if CYTHON_COMPILING_IN_CPYTHON || defined(_PyDict_NewPresized)
#define __Pyx_PyDict_NewPresized(n)  ((n <= 8) ? PyDict_New() : _PyDict_NewPresized(n))
#else
#define __Pyx_PyDict_NewPresized(n)  PyDict_New()
#endif
#if PY_MAJOR_VERSION >= 3 || CYTHON_FUTURE_DIVISION
  #define __Pyx_PyNumber_Divide(x,y)         PyNumber_TrueDivide(x,y)
  #define __Pyx_PyNumber_InPlaceDivide(x,y)  PyNumber_InPlaceTrueDivide(x,y)
#else
  #define __Pyx_PyNumber_Divide(x,y)         PyNumber_Divide(x,y)
  #define __Pyx_PyNumber_InPlaceDivide(x,y)  PyNumber_InPlaceDivide(x,y)
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x030500A1 && CYTHON_USE_UNICODE_INTERNALS
#define __Pyx_PyDict_GetItemStr(dict, name)  _PyDict_GetItem_KnownHash(dict, name, ((PyASCIIObject *) name)->hash)
#else
#define __Pyx_PyDict_GetItemStr(dict, name)  PyDict_GetItem(dict, name)
#endif
#if PY_VERSION_HEX > 0x03030000 && defined(PyUnicode_KIND)
  #define CYTHON_PEP393_ENABLED 1
  #define __Pyx_PyUnicode_READY(op)       (likely(PyUnicode_IS_READY(op)) ?\
                                              0 : _PyUnicode_Ready((PyObject *)(op)))
  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GET_LENGTH(u)
  #define __Pyx_PyUnicode_READ_CHAR(u, i) PyUnicode_READ_CHAR(u, i)
  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   PyUnicode_MAX_CHAR_VALUE(u)
  #define __Pyx_PyUnicode_KIND(u)         PyUnicode_KIND(u)
  #define __Pyx_PyUnicode_DATA(u)         PyUnicode_DATA(u)
  #define __Pyx_PyUnicode_READ(k, d, i)   PyUnicode_READ(k, d, i)
  #define __Pyx_PyUnicode_WRITE(k, d, i, ch)  PyUnicode_WRITE(k, d, i, ch)
  #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != (likely(PyUnicode_IS_READY(u)) ? PyUnicode_GET_LENGTH(u) : PyUnicode_GET_SIZE(u)))
#else
  #define CYTHON_PEP393_ENABLED 0
  #define PyUnicode_1BYTE_KIND  1
  #define PyUnicode_2BYTE_KIND  2
  #define PyUnicode_4BYTE_KIND  4
  #define __Pyx_PyUnicode_READY(op)       (0)
  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GET_SIZE(u)
  #define __Pyx_PyUnicode_READ_CHAR(u, i) ((Py_UCS4)(PyUnicode_AS_UNICODE(u)[i]))
  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   ((sizeof(Py_UNICODE) == 2) ? 65535 : 1114111)
  #define __Pyx_PyUnicode_KIND(u)         (sizeof(Py_UNICODE))
  #define __Pyx_PyUnicode_DATA(u)         ((void*)PyUnicode_AS_UNICODE(u))
  #define __Pyx_PyUnicode_READ(k, d, i)   ((void)(k), (Py_UCS4)(((Py_UNICODE*)d)[i]))
  #define __Pyx_PyUnicode_WRITE(k, d, i, ch)  (((void)(k)), ((Py_UNICODE*)d)[i] = ch)
  #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != PyUnicode_GET_SIZE(u))
#endif
#if CYTHON_COMPILING_IN_PYPY
  #define __Pyx_PyUnicode_Concat(a, b)      PyNumber_Add(a, b)
  #define __Pyx_PyUnicode_ConcatSafe(a, b)  PyNumber_Add(a, b)
#else
  #define __Pyx_PyUnicode_Concat(a, b)      PyUnicode_Concat(a, b)
  #define __Pyx_PyUnicode_ConcatSafe(a, b)  ((unlikely((a) == Py_None) || unlikely((b) == Py_None)) ?\
      PyNumber_Add(a, b) : __Pyx_PyUnicode_Concat(a, b))
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyUnicode_Contains)
  #define PyUnicode_Contains(u, s)  PySequence_Contains(u, s)
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyByteArray_Check)
  #define PyByteArray_Check(obj)  PyObject_TypeCheck(obj, &PyByteArray_Type)
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyObject_Format)
  #define PyObject_Format(obj, fmt)  PyObject_CallMethod(obj, "__format__", "O", fmt)
#endif
#define __Pyx_PyString_FormatSafe(a, b)   ((unlikely((a) == Py_None || (PyString_Check(b) && !PyString_CheckExact(b)))) ? PyNumber_Remainder(a, b) : __Pyx_PyString_Format(a, b))
#define __Pyx_PyUnicode_FormatSafe(a, b)  ((unlikely((a) == Py_None || (PyUnicode_Check(b) && !PyUnicode_CheckExact(b)))) ? PyNumber_Remainder(a, b) : PyUnicode_Format(a, b))
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyString_Format(a, b)  PyUnicode_Format(a, b)
#else
  #define __Pyx_PyString_Format(a, b)  PyString_Format(a, b)
#endif
#if PY_MAJOR_VERSION < 3 && !defined(PyObject_ASCII)
  #define PyObject_ASCII(o)            PyObject_Repr(o)
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyBaseString_Type            PyUnicode_Type
  #define PyStringObject               PyUnicodeObject
  #define PyString_Type                PyUnicode_Type
  #define PyString_Check               PyUnicode_Check
  #define PyString_CheckExact          PyUnicode_CheckExact
  #define PyObject_Unicode             PyObject_Str
#endif
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyBaseString_Check(obj) PyUnicode_Check(obj)
  #define __Pyx_PyBaseString_CheckExact(obj) PyUnicode_CheckExact(obj)
#else
  #define __Pyx_PyBaseString_Check(obj) (PyString_Check(obj) || PyUnicode_Check(obj))
  #define __Pyx_PyBaseString_CheckExact(obj) (PyString_CheckExact(obj) || PyUnicode_CheckExact(obj))
#endif
#ifndef PySet_CheckExact
  #define PySet_CheckExact(obj)        (Py_TYPE(obj) == &PySet_Type)
#endif
#if CYTHON_ASSUME_SAFE_MACROS
  #define __Pyx_PySequence_SIZE(seq)  Py_SIZE(seq)
#else
  #define __Pyx_PySequence_SIZE(seq)  PySequence_Size(seq)
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyIntObject                  PyLongObject
  #define PyInt_Type                   PyLong_Type
  #define PyInt_Check(op)              PyLong_Check(op)
  #define PyInt_CheckExact(op)         PyLong_CheckExact(op)
  #define PyInt_FromString             PyLong_FromString
  #define PyInt_FromUnicode            PyLong_FromUnicode
  #define PyInt_FromLong               PyLong_FromLong
  #define PyInt_FromSize_t             PyLong_FromSize_t
  #define PyInt_FromSsize_t            PyLong_FromSsize_t
  #define PyInt_AsLong                 PyLong_AsLong
  #define PyInt_AS_LONG                PyLong_AS_LONG
  #define PyInt_AsSsize_t              PyLong_AsSsize_t
  #define PyInt_AsUnsignedLongMask     PyLong_AsUnsignedLongMask
  #define PyInt_AsUnsignedLongLongMask PyLong_AsUnsignedLongLongMask
  #define PyNumber_Int                 PyNumber_Long
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyBoolObject                 PyLongObject
#endif
#if PY_MAJOR_VERSION >= 3 && CYTHON_COMPILING_IN_PYPY
  #ifndef PyUnicode_InternFromString
    #define PyUnicode_InternFromString(s) PyUnicode_FromString(s)
  #endif
#endif
#if PY_VERSION_HEX < 0x030200A4
  typedef long Py_hash_t;
  #define __Pyx_PyInt_FromHash_t PyInt_FromLong
  #define __Pyx_PyInt_AsHash_t   PyInt_AsLong
#else
  #define __Pyx_PyInt_FromHash_t PyInt_FromSsize_t
  #define __Pyx_PyInt_AsHash_t   PyInt_AsSsize_t
#endif
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyMethod_New(func, self, klass) ((self) ? PyMethod_New(func, self) : (Py_INCREF(func), func))
#else
  #define __Pyx_PyMethod_New(func, self, klass) PyMethod_New(func, self, klass)
#endif
#if CYTHON_USE_ASYNC_SLOTS
  #if PY_VERSION_HEX >= 0x030500B1
    #define __Pyx_PyAsyncMethodsStruct PyAsyncMethods
    #define __Pyx_PyType_AsAsync(obj) (Py_TYPE(obj)->tp_as_async)
  #else
    #define __Pyx_PyType_AsAsync(obj) ((__Pyx_PyAsyncMethodsStruct*) (Py_TYPE(obj)->tp_reserved))
  #endif
#else
  #define __Pyx_PyType_AsAsync(obj) NULL
#endif
#ifndef __Pyx_PyAsyncMethodsStruct
    typedef struct {
        unaryfunc am_await;
        unaryfunc am_aiter;
        unaryfunc am_anext;
    } __Pyx_PyAsyncMethodsStruct;
#endif

#if defined(WIN32) || defined(MS_WINDOWS)
  #define _USE_MATH_DEFINES
#endif
#include <math.h>
#ifdef NAN
#define __PYX_NAN() ((float) NAN)
#else
static CYTHON_INLINE float __PYX_NAN() {
  float value;
  memset(&value, 0xFF, sizeof(value));
  return value;
}
#endif
#if defined(__CYGWIN__) && defined(_LDBL_EQ_DBL)
#define __Pyx_truncl trunc
#else
#define __Pyx_truncl truncl
#endif


#define __PYX_ERR(f_index, lineno, Ln_error) \
{ \
  __pyx_filename = __pyx_f[f_index]; __pyx_lineno = lineno; __pyx_clineno = __LINE__; goto Ln_error; \
}

#ifndef __PYX_EXTERN_C
  #ifdef __cplusplus
    #define __PYX_EXTERN_C extern "C"
  #else
    #define __PYX_EXTERN_C extern
  #endif
#endif

#define __PYX_HAVE__grapheme__cython__finder
#define __PYX_HAVE_API__grapheme__cython__finder
/* Early includes */
#ifdef _OPENMP
#include <omp.h>
#endif /* _OPENMP */

#if defined(PYREX_WITHOUT_ASSERTIONS) && !defined(CYTHON_WITHOUT_ASSERTIONS)
#define CYTHON_WITHOUT_ASSERTIONS
#endif

typedef struct {PyObject **p; const char *s; const Py_ssize_t n; const char* encoding;
                const char is_unicode; const char is_str; const char intern; } __Pyx_StringTabEntry;

#define __PYX_DEFAULT_STRING_ENCODING_IS_ASCII 0
#define __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT 0
#define __PYX_DEFAULT_STRING_ENCODING ""
#define __Pyx_PyObject_FromString __Pyx_PyBytes_FromString
#define __Pyx_PyObject_FromStringAndSize __Pyx_PyBytes_FromStringAndSize
#define __Pyx_uchar_cast(c) ((unsigned char)c)
#define __Pyx_long_cast(x) ((long)x)
#define __Pyx_fits_Py_ssize_t(v, type, is_signed)  (\
    (sizeof(type) < sizeof(Py_ssize_t))  ||\
    (sizeof(type) > sizeof(Py_ssize_t) &&\
          likely(v < (type)PY_SSIZE_T_MAX ||\
                 v == (type)PY_SSIZE_T_MAX)  &&\
          (!is_signed || likely(v > (type)PY_SSIZE_T_MIN ||\
                                v == (type)PY_SSIZE_T_MIN)))  ||\
    (sizeof(type) == sizeof(Py_ssize_t) &&\
          (is_signed || likely(v < (type)PY_SSIZE_T_MAX ||\
                               v == (type)PY_SSIZE_T_MAX)))  )
static CYTHON_INLINE int __Pyx_is_valid_index(Py_ssize_t i, Py_ssize_t limit) {
    return (size_t) i < (size_t) limit;
}
#if defined (__cplusplus) && __cplusplus >= 201103L
    #include <cstdlib>
    #define __Pyx_sst_abs(value) std::abs(value)
#elif SIZEOF_INT >= SIZEOF_SIZE_T
    #define __Pyx_sst_abs(value) abs(value)
#elif SIZEOF_LONG >= SIZEOF_SIZE_T
    #define __Pyx_sst_abs(value) labs(value)
#elif defined (_MSC_VER)
    #define __Pyx_sst_abs(value) ((Py_ssize_t)_abs64(value))
#elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
    #define __Pyx_sst_abs(value) llabs(value)
#elif defined (__GNUC__)
    #define __Pyx_sst_abs(value) __builtin_llabs(value)
#else
    #define __Pyx_sst_abs(value) ((value<0) ? -value : value)
#endif
static CYTHON_INLINE const char* __Pyx_PyObject_AsString(PyObject*);
static CYTHON_INLINE const char* __Pyx_PyObject_AsStringAndSize(PyObject*, Py_ssize_t* length);
#define __Pyx_PyByteArray_FromString(s) PyByteArray_FromStringAndSize((const char*)s, strlen((const char*)s))
#define __Pyx_PyByteArray_FromStringAndSize(s, l) PyByteArray_FromStringAndSize((const char*)s, l)
#define __Pyx_PyBytes_FromString        PyBytes_FromString
#define __Pyx_PyBytes_FromStringAndSize PyBytes_FromStringAndSize
static CYTHON_INLINE PyObject* __Pyx_PyUnicode_FromString(const char*);
#if PY_MAJOR_VERSION < 3
    #define __Pyx_PyStr_FromString        __Pyx_PyBytes_FromString
    #define __Pyx_PyStr_FromStringAndSize __Pyx_PyBytes_FromStringAndSize
#else
    #define __Pyx_PyStr_FromString        __Pyx_PyUnicode_FromString
    #define __Pyx_PyStr_FromStringAndSize __Pyx_PyUnicode_FromStringAndSize
#endif
#define __Pyx_PyBytes_AsWritableString(s)     ((char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsWritableSString(s)    ((signed char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsWritableUString(s)    ((unsigned char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsString(s)     ((const char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsSString(s)    ((const signed char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsUString(s)    ((const unsigned char*) PyBytes_AS_STRING(s))
#define __Pyx_PyObject_AsWritableString(s)    ((char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsWritableSString(s)    ((signed char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsWritableUString(s)    ((unsigned char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsSString(s)    ((const signed char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsUString(s)    ((const unsigned char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_FromCString(s)  __Pyx_PyObject_FromString((const char*)s)
#define __Pyx_PyBytes_FromCString(s)   __Pyx_PyBytes_FromString((const char*)s)
#define __Pyx_PyByteArray_FromCString(s)   __Pyx_PyByteArray_FromString((const char*)s)
#define __Pyx_PyStr_FromCString(s)     __Pyx_PyStr_FromString((const char*)s)
#define __Pyx_PyUnicode_FromCString(s) __Pyx_PyUnicode_FromString((const char*)s)
static CYTHON_INLINE size_t __Pyx_Py_UNICODE_strlen(const Py_UNICODE *u) {
    const Py_UNICODE *u_end = u;
    while (*u_end++) ;
    return (size_t)(u_end - u - 1);
}
#define __Pyx_PyUnicode_FromUnicode(u)       PyUnicode_FromUnicode(u, __Pyx_Py_UNICODE_strlen(u))
#define __Pyx_PyUnicode_FromUnicodeAndLength PyUnicode_FromUnicode
#define __Pyx_PyUnicode_AsUnicode            PyUnicode_AsUnicode
#define __Pyx_NewRef(obj) (Py_INCREF(obj), obj)
#define __Pyx_Owned_Py_None(b) __Pyx_NewRef(Py_None)
static CYTHON_INLINE PyObject * __Pyx_PyBool_FromLong(long b);
static CYTHON_INLINE int __Pyx_PyObject_IsTrue(PyObject*);
static CYTHON_INLINE int __Pyx_PyObject_IsTrueAndDecref(PyObject*);
static CYTHON_INLINE PyObject* __Pyx_PyNumber_IntOrLong(PyObject* x);
#define __Pyx_PySequence_Tuple(obj)\
    (likely(PyTuple_CheckExact(obj)) ? __Pyx_NewRef(obj) : PySequence_Tuple(obj))
static CYTHON_INLINE Py_ssize_t __Pyx_PyIndex_AsSsize_t(PyObject*);
static CYTHON_INLINE PyObject * __Pyx_PyInt_FromSize_t(size_t);
#if CYTHON_ASSUME_SAFE_MACROS
#define __pyx_PyFloat_AsDouble(x) (PyFloat_CheckExact(x) ? PyFloat_AS_DOUBLE(x) : PyFloat_AsDouble(x))
#else
#define __pyx_PyFloat_AsDouble(x) PyFloat_AsDouble(x)
#endif
#define __pyx_PyFloat_AsFloat(x) ((float) __pyx_PyFloat_AsDouble(x))
#if PY_MAJOR_VERSION >= 3
#define __Pyx_PyNumber_Int(x) (PyLong_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Long(x))
#else
#define __Pyx_PyNumber_Int(x) (PyInt_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Int(x))
#endif
#define __Pyx_PyNumber_Float(x) (PyFloat_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Float(x))
#if PY_MAJOR_VERSION < 3 && __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
static int __Pyx_sys_getdefaultencoding_not_ascii;
static int __Pyx_init_sys_getdefaultencoding_params(void) {
    PyObject* sys;
    PyObject* default_encoding = NULL;
    PyObject* ascii_chars_u = NULL;
    PyObject* ascii_chars_b = NULL;
    const char* default_encoding_c;
    sys = PyImport_ImportModule("sys");
    if (!sys) goto bad;
    default_encoding = PyObject_CallMethod(sys, (char*) "getdefaultencoding", NULL);
    Py_DECREF(sys);
    if (!default_encoding) goto bad;
    default_encoding_c = PyBytes_AsString(default_encoding);
    if (!default_encoding_c) goto bad;
    if (strcmp(default_encoding_c, "ascii") == 0) {
        __Pyx_sys_getdefaultencoding_not_ascii = 0;
    } else {
        char ascii_chars[128];
        int c;
        for (c = 0; c < 128; c++) {
            ascii_chars[c] = c;
        }
        __Pyx_sys_getdefaultencoding_not_ascii = 1;
        ascii_chars_u = PyUnicode_DecodeASCII(ascii_chars, 128, NULL);
        if (!ascii_chars_u) goto bad;
        ascii_chars_b = PyUnicode_AsEncodedString(ascii_chars_u, default_encoding_c, NULL);
        if (!ascii_chars_b || !PyBytes_Check(ascii_chars_b) || memcmp(ascii_chars, PyBytes_AS_STRING(ascii_chars_b), 128) != 0) {
            PyErr_Format(
                PyExc_ValueError,
                "This module compiled with c_string_encoding=ascii, but default encoding '%.200s' is not a superset of ascii.",
                default_encoding_c);
            goto bad;
        }
        Py_DECREF(ascii_chars_u);
        Py_DECREF(ascii_chars_b);
    }
    Py_DECREF(default_encoding);
    return 0;
bad:
    Py_XDECREF(default_encoding);
    Py_XDECREF(ascii_chars_u);
    Py_XDECREF(ascii_chars_b);
    return -1;
}
#endif
#if __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT && PY_MAJOR_VERSION >= 3
#define __Pyx_PyUnicode_FromStringAndSize(c_str, size) PyUnicode_DecodeUTF8(c_str, size, NULL)
#else
#define __Pyx_PyUnicode_FromStringAndSize(c_str, size) PyUnicode_Decode(c_str, size, __PYX_DEFAULT_STRING_ENCODING, NULL)
#if __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
static char* __PYX_DEFAULT_STRING_ENCODING;
static int __Pyx_init_sys_getdefaultencoding_params(void) {
    PyObject* sys;
    PyObject* default_encoding = NULL;
    char* default_encoding_c;
    sys = PyImport_ImportModule("sys");
    if (!sys) goto bad;
    default_encoding = PyObject_CallMethod(sys, (char*) (const char*) "getdefaultencoding", NULL);
    Py_DECREF(sys);
    if (!default_encoding) goto bad;
    default_encoding_c = PyBytes_AsString(default_encoding);
    if (!default_encoding_c) goto bad;
    __PYX_DEFAULT_STRING_ENCODING = (char*) malloc(strlen(default_encoding_c) + 1);
    if (!__PYX_DEFAULT_STRING_ENCODING) goto bad;
    strcpy(__PYX_DEFAULT_STRING_ENCODING, default_encoding_c);
    Py_DECREF(default_encoding);
    return 0;
bad:
    Py_XDECREF(default_encoding);
    return -1;
}
#endif
#endif


/* Test for GCC > 2.95 */
#if defined(__GNUC__)     && (__GNUC__ > 2 || (__GNUC__ == 2 && (__GNUC_MINOR__ > 95)))
  #define likely(x)   __builtin_expect(!!(x), 1)
  #define unlikely(x) __builtin_expect(!!(x), 0)
#else /* !__GNUC__ or GCC < 2.95 */
  #define likely(x)   (x)
  #define unlikely(x) (x)
#endif /* __GNUC__ */
static CYTHON_INLINE void __Pyx_pretend_to_initialize(void* ptr) { (void)ptr; }

static PyObject *__pyx_m = NULL;
static PyObject *__pyx_d;
static PyObject *__pyx_b;
static PyObject *__pyx_cython_runtime = NULL;
static PyObject *__pyx_empty_tuple;
static PyObject *__pyx_empty_bytes;
static PyObject *__pyx_empty_unicode;
static int __pyx_lineno;
static int __pyx_clineno = 0;
static const char * __pyx_cfilenm= __FILE__;
static const char *__pyx_filename;


static const char *__pyx_f[] = {
  "grapheme/cython/finder.pyx",
  "stringsource",
};

/*--- Type declarations ---*/
struct __pyx_obj_8grapheme_6cython_6finder_FSM;
struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator;

/* "grapheme/cython/finder.pyx":15
 * DEF SHOULD_BREAK_BIT_INVERSED = ~(1 << 30)
 * 
 * cdef enum FSMState:             # <<<<<<<<<<<<<<
 *     fsm_default = 1
 *     fsm_cr = 2
 */
enum __pyx_t_8grapheme_6cython_6finder_FSMState {
  __pyx_e_8grapheme_6cython_6finder_fsm_default = 1,
  __pyx_e_8grapheme_6cython_6finder_fsm_cr = 2,
  __pyx_e_8grapheme_6cython_6finder_fsm_lf_or_control = 3,
  __pyx_e_8grapheme_6cython_6finder_fsm_prepend = 4,
  __pyx_e_8grapheme_6cython_6finder_fsm_hangul_l = 5,
  __pyx_e_8grapheme_6cython_6finder_fsm_hangul_lv_or_v = 6,
  __pyx_e_8grapheme_6cython_6finder_fsm_hangul_lvt_or_t = 7,
  __pyx_e_8grapheme_6cython_6finder_fsm_emoji = 8,
  __pyx_e_8grapheme_6cython_6finder_fsm_emoji_zjw = 9,
  __pyx_e_8grapheme_6cython_6finder_fsm_ri = 10
};

/* "grapheme/cython/finder.pyx":136
 *         return ri(group)
 * 
 * cdef class FSM:             # <<<<<<<<<<<<<<
 *     @classmethod
 *     def default(cls, n):
 */
struct __pyx_obj_8grapheme_6cython_6finder_FSM {
  PyObject_HEAD
};


/* "grapheme/cython/finder.pyx":327
 * 
 * 
 * cdef class GraphemeIterator:             # <<<<<<<<<<<<<<
 *     cdef str_iter
 *     cdef str buffer
 */
struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator {
  PyObject_HEAD
  struct __pyx_vtabstruct_8grapheme_6cython_6finder_GraphemeIterator *__pyx_vtab;
  PyObject *str_iter;
  PyObject *buffer;
  PyObject *state;
};



struct __pyx_vtabstruct_8grapheme_6cython_6finder_GraphemeIterator {
  PyObject *(*_break)(struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *, PyObject *);
};
static struct __pyx_vtabstruct_8grapheme_6cython_6finder_GraphemeIterator *__pyx_vtabptr_8grapheme_6cython_6finder_GraphemeIterator;

/* --- Runtime support code (head) --- */
/* Refnanny.proto */
#ifndef CYTHON_REFNANNY
  #define CYTHON_REFNANNY 0
#endif
#if CYTHON_REFNANNY
  typedef struct {
    void (*INCREF)(void*, PyObject*, int);
    void (*DECREF)(void*, PyObject*, int);
    void (*GOTREF)(void*, PyObject*, int);
    void (*GIVEREF)(void*, PyObject*, int);
    void* (*SetupContext)(const char*, int, const char*);
    void (*FinishContext)(void**);
  } __Pyx_RefNannyAPIStruct;
  static __Pyx_RefNannyAPIStruct *__Pyx_RefNanny = NULL;
  static __Pyx_RefNannyAPIStruct *__Pyx_RefNannyImportAPI(const char *modname);
  #define __Pyx_RefNannyDeclarations void *__pyx_refnanny = NULL;
#ifdef WITH_THREAD
  #define __Pyx_RefNannySetupContext(name, acquire_gil)\
          if (acquire_gil) {\
              PyGILState_STATE __pyx_gilstate_save = PyGILState_Ensure();\
              __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__);\
              PyGILState_Release(__pyx_gilstate_save);\
          } else {\
              __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__);\
          }
#else
  #define __Pyx_RefNannySetupContext(name, acquire_gil)\
          __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__)
#endif
  #define __Pyx_RefNannyFinishContext()\
          __Pyx_RefNanny->FinishContext(&__pyx_refnanny)
  #define __Pyx_INCREF(r)  __Pyx_RefNanny->INCREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_DECREF(r)  __Pyx_RefNanny->DECREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_GOTREF(r)  __Pyx_RefNanny->GOTREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_GIVEREF(r) __Pyx_RefNanny->GIVEREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_XINCREF(r)  do { if((r) != NULL) {__Pyx_INCREF(r); }} while(0)
  #define __Pyx_XDECREF(r)  do { if((r) != NULL) {__Pyx_DECREF(r); }} while(0)
  #define __Pyx_XGOTREF(r)  do { if((r) != NULL) {__Pyx_GOTREF(r); }} while(0)
  #define __Pyx_XGIVEREF(r) do { if((r) != NULL) {__Pyx_GIVEREF(r);}} while(0)
#else
  #define __Pyx_RefNannyDeclarations
  #define __Pyx_RefNannySetupContext(name, acquire_gil)
  #define __Pyx_RefNannyFinishContext()
  #define __Pyx_INCREF(r) Py_INCREF(r)
  #define __Pyx_DECREF(r) Py_DECREF(r)
  #define __Pyx_GOTREF(r)
  #define __Pyx_GIVEREF(r)
  #define __Pyx_XINCREF(r) Py_XINCREF(r)
  #define __Pyx_XDECREF(r) Py_XDECREF(r)
  #define __Pyx_XGOTREF(r)
  #define __Pyx_XGIVEREF(r)
#endif
#define __Pyx_XDECREF_SET(r, v) do {\
        PyObject *tmp = (PyObject *) r;\
        r = v; __Pyx_XDECREF(tmp);\
    } while (0)
#define __Pyx_DECREF_SET(r, v) do {\
        PyObject *tmp = (PyObject *) r;\
        r = v; __Pyx_DECREF(tmp);\
    } while (0)
#define __Pyx_CLEAR(r)    do { PyObject* tmp = ((PyObject*)(r)); r = NULL; __Pyx_DECREF(tmp);} while(0)
#define __Pyx_XCLEAR(r)   do { if((r) != NULL) {PyObject* tmp = ((PyObject*)(r)); r = NULL; __Pyx_DECREF(tmp);}} while(0)

/* PyObjectGetAttrStr.proto */
#if CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStr(PyObject* obj, PyObject* attr_name);
#else
#define __Pyx_PyObject_GetAttrStr(o,n) PyObject_GetAttr(o,n)
#endif

/* GetBuiltinName.proto */
static PyObject *__Pyx_GetBuiltinName(PyObject *name);

/* PyThreadStateGet.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyThreadState_declare  PyThreadState *__pyx_tstate;
#define __Pyx_PyThreadState_assign  __pyx_tstate = __Pyx_PyThreadState_Current;
#define __Pyx_PyErr_Occurred()  __pyx_tstate->curexc_type
#else
#define __Pyx_PyThreadState_declare
#define __Pyx_PyThreadState_assign
#define __Pyx_PyErr_Occurred()  PyErr_Occurred()
#endif

/* PyErrFetchRestore.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyErr_Clear() __Pyx_ErrRestore(NULL, NULL, NULL)
#define __Pyx_ErrRestoreWithState(type, value, tb)  __Pyx_ErrRestoreInState(PyThreadState_GET(), type, value, tb)
#define __Pyx_ErrFetchWithState(type, value, tb)    __Pyx_ErrFetchInState(PyThreadState_GET(), type, value, tb)
#define __Pyx_ErrRestore(type, value, tb)  __Pyx_ErrRestoreInState(__pyx_tstate, type, value, tb)
#define __Pyx_ErrFetch(type, value, tb)    __Pyx_ErrFetchInState(__pyx_tstate, type, value, tb)
static CYTHON_INLINE void __Pyx_ErrRestoreInState(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb);
static CYTHON_INLINE void __Pyx_ErrFetchInState(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb);
#if CYTHON_COMPILING_IN_CPYTHON
#define __Pyx_PyErr_SetNone(exc) (Py_INCREF(exc), __Pyx_ErrRestore((exc), NULL, NULL))
#else
#define __Pyx_PyErr_SetNone(exc) PyErr_SetNone(exc)
#endif
#else
#define __Pyx_PyErr_Clear() PyErr_Clear()
#define __Pyx_PyErr_SetNone(exc) PyErr_SetNone(exc)
#define __Pyx_ErrRestoreWithState(type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetchWithState(type, value, tb)  PyErr_Fetch(type, value, tb)
#define __Pyx_ErrRestoreInState(tstate, type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetchInState(tstate, type, value, tb)  PyErr_Fetch(type, value, tb)
#define __Pyx_ErrRestore(type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetch(type, value, tb)  PyErr_Fetch(type, value, tb)
#endif

/* WriteUnraisableException.proto */
static void __Pyx_WriteUnraisable(const char *name, int clineno,
                                  int lineno, const char *filename,
                                  int full_traceback, int nogil);

/* GetModuleGlobalName.proto */
#if CYTHON_USE_DICT_VERSIONS
#define __Pyx_GetModuleGlobalName(var, name)  {\
    static PY_UINT64_T __pyx_dict_version = 0;\
    static PyObject *__pyx_dict_cached_value = NULL;\
    (var) = (likely(__pyx_dict_version == __PYX_GET_DICT_VERSION(__pyx_d))) ?\
        (likely(__pyx_dict_cached_value) ? __Pyx_NewRef(__pyx_dict_cached_value) : __Pyx_GetBuiltinName(name)) :\
        __Pyx__GetModuleGlobalName(name, &__pyx_dict_version, &__pyx_dict_cached_value);\
}
#define __Pyx_GetModuleGlobalNameUncached(var, name)  {\
    PY_UINT64_T __pyx_dict_version;\
    PyObject *__pyx_dict_cached_value;\
    (var) = __Pyx__GetModuleGlobalName(name, &__pyx_dict_version, &__pyx_dict_cached_value);\
}
static PyObject *__Pyx__GetModuleGlobalName(PyObject *name, PY_UINT64_T *dict_version, PyObject **dict_cached_value);
#else
#define __Pyx_GetModuleGlobalName(var, name)  (var) = __Pyx__GetModuleGlobalName(name)
#define __Pyx_GetModuleGlobalNameUncached(var, name)  (var) = __Pyx__GetModuleGlobalName(name)
static CYTHON_INLINE PyObject *__Pyx__GetModuleGlobalName(PyObject *name);
#endif

/* RaiseArgTupleInvalid.proto */
static void __Pyx_RaiseArgtupleInvalid(const char* func_name, int exact,
    Py_ssize_t num_min, Py_ssize_t num_max, Py_ssize_t num_found);

/* RaiseDoubleKeywords.proto */
static void __Pyx_RaiseDoubleKeywordsError(const char* func_name, PyObject* kw_name);

/* ParseKeywords.proto */
static int __Pyx_ParseOptionalKeywords(PyObject *kwds, PyObject **argnames[],\
    PyObject *kwds2, PyObject *values[], Py_ssize_t num_pos_args,\
    const char* function_name);

/* PyCFunctionFastCall.proto */
#if CYTHON_FAST_PYCCALL
static CYTHON_INLINE PyObject *__Pyx_PyCFunction_FastCall(PyObject *func, PyObject **args, Py_ssize_t nargs);
#else
#define __Pyx_PyCFunction_FastCall(func, args, nargs)  (assert(0), NULL)
#endif

/* PyFunctionFastCall.proto */
#if CYTHON_FAST_PYCALL
#define __Pyx_PyFunction_FastCall(func, args, nargs)\
    __Pyx_PyFunction_FastCallDict((func), (args), (nargs), NULL)
#if 1 || PY_VERSION_HEX < 0x030600B1
static PyObject *__Pyx_PyFunction_FastCallDict(PyObject *func, PyObject **args, int nargs, PyObject *kwargs);
#else
#define __Pyx_PyFunction_FastCallDict(func, args, nargs, kwargs) _PyFunction_FastCallDict(func, args, nargs, kwargs)
#endif
#define __Pyx_BUILD_ASSERT_EXPR(cond)\
    (sizeof(char [1 - 2*!(cond)]) - 1)
#ifndef Py_MEMBER_SIZE
#define Py_MEMBER_SIZE(type, member) sizeof(((type *)0)->member)
#endif
  static size_t __pyx_pyframe_localsplus_offset = 0;
  #include "frameobject.h"
  #define __Pxy_PyFrame_Initialize_Offsets()\
    ((void)__Pyx_BUILD_ASSERT_EXPR(sizeof(PyFrameObject) == offsetof(PyFrameObject, f_localsplus) + Py_MEMBER_SIZE(PyFrameObject, f_localsplus)),\
     (void)(__pyx_pyframe_localsplus_offset = ((size_t)PyFrame_Type.tp_basicsize) - Py_MEMBER_SIZE(PyFrameObject, f_localsplus)))
  #define __Pyx_PyFrame_GetLocalsplus(frame)\
    (assert(__pyx_pyframe_localsplus_offset), (PyObject **)(((char *)(frame)) + __pyx_pyframe_localsplus_offset))
#endif

/* PyObjectCall.proto */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_Call(PyObject *func, PyObject *arg, PyObject *kw);
#else
#define __Pyx_PyObject_Call(func, arg, kw) PyObject_Call(func, arg, kw)
#endif

/* PyObjectCall2Args.proto */
static CYTHON_UNUSED PyObject* __Pyx_PyObject_Call2Args(PyObject* function, PyObject* arg1, PyObject* arg2);

/* PyObjectCallMethO.proto */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallMethO(PyObject *func, PyObject *arg);
#endif

/* PyObjectCallOneArg.proto */
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallOneArg(PyObject *func, PyObject *arg);

/* RaiseTooManyValuesToUnpack.proto */
static CYTHON_INLINE void __Pyx_RaiseTooManyValuesError(Py_ssize_t expected);

/* RaiseNeedMoreValuesToUnpack.proto */
static CYTHON_INLINE void __Pyx_RaiseNeedMoreValuesError(Py_ssize_t index);

/* IterFinish.proto */
static CYTHON_INLINE int __Pyx_IterFinish(void);

/* UnpackItemEndCheck.proto */
static int __Pyx_IternextUnpackEndCheck(PyObject *retval, Py_ssize_t expected);

/* PyErrExceptionMatches.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyErr_ExceptionMatches(err) __Pyx_PyErr_ExceptionMatchesInState(__pyx_tstate, err)
static CYTHON_INLINE int __Pyx_PyErr_ExceptionMatchesInState(PyThreadState* tstate, PyObject* err);
#else
#define __Pyx_PyErr_ExceptionMatches(err)  PyErr_ExceptionMatches(err)
#endif

/* GetAttr.proto */
static CYTHON_INLINE PyObject *__Pyx_GetAttr(PyObject *, PyObject *);

/* GetAttr3.proto */
static CYTHON_INLINE PyObject *__Pyx_GetAttr3(PyObject *, PyObject *, PyObject *);

/* GetItemInt.proto */
#define __Pyx_GetItemInt(o, i, type, is_signed, to_py_func, is_list, wraparound, boundscheck)\
    (__Pyx_fits_Py_ssize_t(i, type, is_signed) ?\
    __Pyx_GetItemInt_Fast(o, (Py_ssize_t)i, is_list, wraparound, boundscheck) :\
    (is_list ? (PyErr_SetString(PyExc_IndexError, "list index out of range"), (PyObject*)NULL) :\
               __Pyx_GetItemInt_Generic(o, to_py_func(i))))
#define __Pyx_GetItemInt_List(o, i, type, is_signed, to_py_func, is_list, wraparound, boundscheck)\
    (__Pyx_fits_Py_ssize_t(i, type, is_signed) ?\
    __Pyx_GetItemInt_List_Fast(o, (Py_ssize_t)i, wraparound, boundscheck) :\
    (PyErr_SetString(PyExc_IndexError, "list index out of range"), (PyObject*)NULL))
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_List_Fast(PyObject *o, Py_ssize_t i,
                                                              int wraparound, int boundscheck);
#define __Pyx_GetItemInt_Tuple(o, i, type, is_signed, to_py_func, is_list, wraparound, boundscheck)\
    (__Pyx_fits_Py_ssize_t(i, type, is_signed) ?\
    __Pyx_GetItemInt_Tuple_Fast(o, (Py_ssize_t)i, wraparound, boundscheck) :\
    (PyErr_SetString(PyExc_IndexError, "tuple index out of range"), (PyObject*)NULL))
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_Tuple_Fast(PyObject *o, Py_ssize_t i,
                                                              int wraparound, int boundscheck);
static PyObject *__Pyx_GetItemInt_Generic(PyObject *o, PyObject* j);
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_Fast(PyObject *o, Py_ssize_t i,
                                                     int is_list, int wraparound, int boundscheck);

/* ObjectGetItem.proto */
#if CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PyObject *__Pyx_PyObject_GetItem(PyObject *obj, PyObject* key);
#else
#define __Pyx_PyObject_GetItem(obj, key)  PyObject_GetItem(obj, key)
#endif

/* PyIntBinop.proto */
#if !CYTHON_COMPILING_IN_PYPY
static PyObject* __Pyx_PyInt_SubtractObjC(PyObject *op1, PyObject *op2, long intval, int inplace, int zerodivision_check);
#else
#define __Pyx_PyInt_SubtractObjC(op1, op2, intval, inplace, zerodivision_check)\
    (inplace ? PyNumber_InPlaceSubtract(op1, op2) : PyNumber_Subtract(op1, op2))
#endif

/* PyIntBinop.proto */
#if !CYTHON_COMPILING_IN_PYPY
static PyObject* __Pyx_PyInt_AddObjC(PyObject *op1, PyObject *op2, long intval, int inplace, int zerodivision_check);
#else
#define __Pyx_PyInt_AddObjC(op1, op2, intval, inplace, zerodivision_check)\
    (inplace ? PyNumber_InPlaceAdd(op1, op2) : PyNumber_Add(op1, op2))
#endif

/* IncludeStringH.proto */
#include <string.h>

/* BytesEquals.proto */
static CYTHON_INLINE int __Pyx_PyBytes_Equals(PyObject* s1, PyObject* s2, int equals);

/* UnicodeEquals.proto */
static CYTHON_INLINE int __Pyx_PyUnicode_Equals(PyObject* s1, PyObject* s2, int equals);

/* GetItemIntUnicode.proto */
#define __Pyx_GetItemInt_Unicode(o, i, type, is_signed, to_py_func, is_list, wraparound, boundscheck)\
    (__Pyx_fits_Py_ssize_t(i, type, is_signed) ?\
    __Pyx_GetItemInt_Unicode_Fast(o, (Py_ssize_t)i, wraparound, boundscheck) :\
    (PyErr_SetString(PyExc_IndexError, "string index out of range"), (Py_UCS4)-1))
static CYTHON_INLINE Py_UCS4 __Pyx_GetItemInt_Unicode_Fast(PyObject* ustring, Py_ssize_t i,
                                                           int wraparound, int boundscheck);

/* PyUnicode_Substring.proto */
static CYTHON_INLINE PyObject* __Pyx_PyUnicode_Substring(
            PyObject* text, Py_ssize_t start, Py_ssize_t stop);

/* unicode_iter.proto */
static CYTHON_INLINE int __Pyx_init_unicode_iteration(
    PyObject* ustring, Py_ssize_t *length, void** data, int *kind);

/* ArgTypeTest.proto */
#define __Pyx_ArgTypeTest(obj, type, none_allowed, name, exact)\
    ((likely((Py_TYPE(obj) == type) | (none_allowed && (obj == Py_None)))) ? 1 :\
        __Pyx__ArgTypeTest(obj, type, name, exact))
static int __Pyx__ArgTypeTest(PyObject *obj, PyTypeObject *type, const char *name, int exact);

/* IterNext.proto */
#define __Pyx_PyIter_Next(obj) __Pyx_PyIter_Next2(obj, NULL)
static CYTHON_INLINE PyObject *__Pyx_PyIter_Next2(PyObject *, PyObject *);

/* GetTopmostException.proto */
#if CYTHON_USE_EXC_INFO_STACK
static _PyErr_StackItem * __Pyx_PyErr_GetTopmostException(PyThreadState *tstate);
#endif

/* SaveResetException.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_ExceptionSave(type, value, tb)  __Pyx__ExceptionSave(__pyx_tstate, type, value, tb)
static CYTHON_INLINE void __Pyx__ExceptionSave(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb);
#define __Pyx_ExceptionReset(type, value, tb)  __Pyx__ExceptionReset(__pyx_tstate, type, value, tb)
static CYTHON_INLINE void __Pyx__ExceptionReset(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb);
#else
#define __Pyx_ExceptionSave(type, value, tb)   PyErr_GetExcInfo(type, value, tb)
#define __Pyx_ExceptionReset(type, value, tb)  PyErr_SetExcInfo(type, value, tb)
#endif

/* GetException.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_GetException(type, value, tb)  __Pyx__GetException(__pyx_tstate, type, value, tb)
static int __Pyx__GetException(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb);
#else
static int __Pyx_GetException(PyObject **type, PyObject **value, PyObject **tb);
#endif

/* RaiseException.proto */
static void __Pyx_Raise(PyObject *type, PyObject *value, PyObject *tb, PyObject *cause);

/* Import.proto */
static PyObject *__Pyx_Import(PyObject *name, PyObject *from_list, int level);

/* ImportFrom.proto */
static PyObject* __Pyx_ImportFrom(PyObject* module, PyObject* name);

/* HasAttr.proto */
static CYTHON_INLINE int __Pyx_HasAttr(PyObject *, PyObject *);

/* PyObject_GenericGetAttrNoDict.proto */
#if CYTHON_USE_TYPE_SLOTS && CYTHON_USE_PYTYPE_LOOKUP && PY_VERSION_HEX < 0x03070000
static CYTHON_INLINE PyObject* __Pyx_PyObject_GenericGetAttrNoDict(PyObject* obj, PyObject* attr_name);
#else
#define __Pyx_PyObject_GenericGetAttrNoDict PyObject_GenericGetAttr
#endif

/* PyObject_GenericGetAttr.proto */
#if CYTHON_USE_TYPE_SLOTS && CYTHON_USE_PYTYPE_LOOKUP && PY_VERSION_HEX < 0x03070000
static PyObject* __Pyx_PyObject_GenericGetAttr(PyObject* obj, PyObject* attr_name);
#else
#define __Pyx_PyObject_GenericGetAttr PyObject_GenericGetAttr
#endif

/* SetupReduce.proto */
static int __Pyx_setup_reduce(PyObject* type_obj);

/* SetVTable.proto */
static int __Pyx_SetVtable(PyObject *dict, void *vtable);

/* ClassMethod.proto */
#include "descrobject.h"
static PyObject* __Pyx_Method_ClassMethod(PyObject *method);

/* GetNameInClass.proto */
#define __Pyx_GetNameInClass(var, nmspace, name)  (var) = __Pyx__GetNameInClass(nmspace, name)
static PyObject *__Pyx__GetNameInClass(PyObject *nmspace, PyObject *name);

/* CalculateMetaclass.proto */
static PyObject *__Pyx_CalculateMetaclass(PyTypeObject *metaclass, PyObject *bases);

/* SetNameInClass.proto */
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x030500A1
#define __Pyx_SetNameInClass(ns, name, value)\
    (likely(PyDict_CheckExact(ns)) ? _PyDict_SetItem_KnownHash(ns, name, value, ((PyASCIIObject *) name)->hash) : PyObject_SetItem(ns, name, value))
#elif CYTHON_COMPILING_IN_CPYTHON
#define __Pyx_SetNameInClass(ns, name, value)\
    (likely(PyDict_CheckExact(ns)) ? PyDict_SetItem(ns, name, value) : PyObject_SetItem(ns, name, value))
#else
#define __Pyx_SetNameInClass(ns, name, value)  PyObject_SetItem(ns, name, value)
#endif

/* Py3ClassCreate.proto */
static PyObject *__Pyx_Py3MetaclassPrepare(PyObject *metaclass, PyObject *bases, PyObject *name, PyObject *qualname,
                                           PyObject *mkw, PyObject *modname, PyObject *doc);
static PyObject *__Pyx_Py3ClassCreate(PyObject *metaclass, PyObject *name, PyObject *bases, PyObject *dict,
                                      PyObject *mkw, int calculate_metaclass, int allow_py2_metaclass);

/* CLineInTraceback.proto */
#ifdef CYTHON_CLINE_IN_TRACEBACK
#define __Pyx_CLineForTraceback(tstate, c_line)  (((CYTHON_CLINE_IN_TRACEBACK)) ? c_line : 0)
#else
static int __Pyx_CLineForTraceback(PyThreadState *tstate, int c_line);
#endif

/* CodeObjectCache.proto */
typedef struct {
    PyCodeObject* code_object;
    int code_line;
} __Pyx_CodeObjectCacheEntry;
struct __Pyx_CodeObjectCache {
    int count;
    int max_count;
    __Pyx_CodeObjectCacheEntry* entries;
};
static struct __Pyx_CodeObjectCache __pyx_code_cache = {0,0,NULL};
static int __pyx_bisect_code_objects(__Pyx_CodeObjectCacheEntry* entries, int count, int code_line);
static PyCodeObject *__pyx_find_code_object(int code_line);
static void __pyx_insert_code_object(int code_line, PyCodeObject* code_object);

/* AddTraceback.proto */
static void __Pyx_AddTraceback(const char *funcname, int c_line,
                               int py_line, const char *filename);

/* CIntToPy.proto */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_int(int value);

/* UnicodeAsUCS4.proto */
static CYTHON_INLINE Py_UCS4 __Pyx_PyUnicode_AsPy_UCS4(PyObject*);

/* CIntToPy.proto */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_long(long value);

/* CIntFromPy.proto */
static CYTHON_INLINE long __Pyx_PyInt_As_long(PyObject *);

/* CIntFromPy.proto */
static CYTHON_INLINE int __Pyx_PyInt_As_int(PyObject *);

/* ObjectAsUCS4.proto */
#define __Pyx_PyObject_AsPy_UCS4(x)\
    (likely(PyUnicode_Check(x)) ? __Pyx_PyUnicode_AsPy_UCS4(x) : __Pyx__PyObject_AsPy_UCS4(x))
static Py_UCS4 __Pyx__PyObject_AsPy_UCS4(PyObject*);

/* FastTypeChecks.proto */
#if CYTHON_COMPILING_IN_CPYTHON
#define __Pyx_TypeCheck(obj, type) __Pyx_IsSubtype(Py_TYPE(obj), (PyTypeObject *)type)
static CYTHON_INLINE int __Pyx_IsSubtype(PyTypeObject *a, PyTypeObject *b);
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches(PyObject *err, PyObject *type);
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches2(PyObject *err, PyObject *type1, PyObject *type2);
#else
#define __Pyx_TypeCheck(obj, type) PyObject_TypeCheck(obj, (PyTypeObject *)type)
#define __Pyx_PyErr_GivenExceptionMatches(err, type) PyErr_GivenExceptionMatches(err, type)
#define __Pyx_PyErr_GivenExceptionMatches2(err, type1, type2) (PyErr_GivenExceptionMatches(err, type1) || PyErr_GivenExceptionMatches(err, type2))
#endif
#define __Pyx_PyException_Check(obj) __Pyx_TypeCheck(obj, PyExc_Exception)

/* CheckBinaryVersion.proto */
static int __Pyx_check_binary_version(void);

/* VoidPtrImport.proto */
static int __Pyx_ImportVoidPtr(PyObject *module, const char *name, void **p, const char *sig);

/* FunctionImport.proto */
static int __Pyx_ImportFunction(PyObject *module, const char *funcname, void (**f)(void), const char *sig);

/* InitStrings.proto */
static int __Pyx_InitStrings(__Pyx_StringTabEntry *t);

static PyObject *__pyx_f_8grapheme_6cython_6finder_16GraphemeIterator__break(struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *__pyx_v_self, PyObject *__pyx_v_new); /* proto*/

/* Module declarations from 'grapheme.cython.grapheme_property_group' */
static int (*__pyx_f_8grapheme_6cython_23grapheme_property_group_c_get_group)(Py_UCS4); /*proto*/

/* Module declarations from 'grapheme.cython.constants' */
static int *__pyx_vp_8grapheme_6cython_9constants_G_PREPEND = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_PREPEND (*__pyx_vp_8grapheme_6cython_9constants_G_PREPEND)
static int *__pyx_vp_8grapheme_6cython_9constants_G_CR = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_CR (*__pyx_vp_8grapheme_6cython_9constants_G_CR)
static int *__pyx_vp_8grapheme_6cython_9constants_G_LF = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_LF (*__pyx_vp_8grapheme_6cython_9constants_G_LF)
static int *__pyx_vp_8grapheme_6cython_9constants_G_CONTROL = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_CONTROL (*__pyx_vp_8grapheme_6cython_9constants_G_CONTROL)
static int *__pyx_vp_8grapheme_6cython_9constants_G_EXTEND = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_EXTEND (*__pyx_vp_8grapheme_6cython_9constants_G_EXTEND)
static int *__pyx_vp_8grapheme_6cython_9constants_G_REGIONAL_INDICATOR = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_REGIONAL_INDICATOR (*__pyx_vp_8grapheme_6cython_9constants_G_REGIONAL_INDICATOR)
static int *__pyx_vp_8grapheme_6cython_9constants_G_SPACING_MARK = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_SPACING_MARK (*__pyx_vp_8grapheme_6cython_9constants_G_SPACING_MARK)
static int *__pyx_vp_8grapheme_6cython_9constants_G_L = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_L (*__pyx_vp_8grapheme_6cython_9constants_G_L)
static int *__pyx_vp_8grapheme_6cython_9constants_G_V = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_V (*__pyx_vp_8grapheme_6cython_9constants_G_V)
static int *__pyx_vp_8grapheme_6cython_9constants_G_T = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_T (*__pyx_vp_8grapheme_6cython_9constants_G_T)
static int *__pyx_vp_8grapheme_6cython_9constants_G_LV = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_LV (*__pyx_vp_8grapheme_6cython_9constants_G_LV)
static int *__pyx_vp_8grapheme_6cython_9constants_G_LVT = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_LVT (*__pyx_vp_8grapheme_6cython_9constants_G_LVT)
static int *__pyx_vp_8grapheme_6cython_9constants_G_ZWJ = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_ZWJ (*__pyx_vp_8grapheme_6cython_9constants_G_ZWJ)
static int *__pyx_vp_8grapheme_6cython_9constants_G_EXTENDED_PICTOGRAPHIC = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_EXTENDED_PICTOGRAPHIC (*__pyx_vp_8grapheme_6cython_9constants_G_EXTENDED_PICTOGRAPHIC)
static int *__pyx_vp_8grapheme_6cython_9constants_G_OTHER = 0;
#define __pyx_v_8grapheme_6cython_9constants_G_OTHER (*__pyx_vp_8grapheme_6cython_9constants_G_OTHER)
static int *__pyx_vp_8grapheme_6cython_9constants_FSM_default = 0;
#define __pyx_v_8grapheme_6cython_9constants_FSM_default (*__pyx_vp_8grapheme_6cython_9constants_FSM_default)
static int *__pyx_vp_8grapheme_6cython_9constants_FSM_cr = 0;
#define __pyx_v_8grapheme_6cython_9constants_FSM_cr (*__pyx_vp_8grapheme_6cython_9constants_FSM_cr)
static int *__pyx_vp_8grapheme_6cython_9constants_FSM_lf_or_control = 0;
#define __pyx_v_8grapheme_6cython_9constants_FSM_lf_or_control (*__pyx_vp_8grapheme_6cython_9constants_FSM_lf_or_control)
static int *__pyx_vp_8grapheme_6cython_9constants_FSM_prepend = 0;
#define __pyx_v_8grapheme_6cython_9constants_FSM_prepend (*__pyx_vp_8grapheme_6cython_9constants_FSM_prepend)
static int *__pyx_vp_8grapheme_6cython_9constants_FSM_hangul_l = 0;
#define __pyx_v_8grapheme_6cython_9constants_FSM_hangul_l (*__pyx_vp_8grapheme_6cython_9constants_FSM_hangul_l)
static int *__pyx_vp_8grapheme_6cython_9constants_FSM_hangul_lv_or_v = 0;
#define __pyx_v_8grapheme_6cython_9constants_FSM_hangul_lv_or_v (*__pyx_vp_8grapheme_6cython_9constants_FSM_hangul_lv_or_v)
static int *__pyx_vp_8grapheme_6cython_9constants_FSM_hangul_lvt_or_t = 0;
#define __pyx_v_8grapheme_6cython_9constants_FSM_hangul_lvt_or_t (*__pyx_vp_8grapheme_6cython_9constants_FSM_hangul_lvt_or_t)
static int *__pyx_vp_8grapheme_6cython_9constants_FSM_emoji = 0;
#define __pyx_v_8grapheme_6cython_9constants_FSM_emoji (*__pyx_vp_8grapheme_6cython_9constants_FSM_emoji)
static int *__pyx_vp_8grapheme_6cython_9constants_FSM_emoji_zjw = 0;
#define __pyx_v_8grapheme_6cython_9constants_FSM_emoji_zjw (*__pyx_vp_8grapheme_6cython_9constants_FSM_emoji_zjw)
static int *__pyx_vp_8grapheme_6cython_9constants_FSM_ri = 0;
#define __pyx_v_8grapheme_6cython_9constants_FSM_ri (*__pyx_vp_8grapheme_6cython_9constants_FSM_ri)

/* Module declarations from 'grapheme.cython.finder' */
static PyTypeObject *__pyx_ptype_8grapheme_6cython_6finder_FSM = 0;
static PyTypeObject *__pyx_ptype_8grapheme_6cython_6finder_GraphemeIterator = 0;
static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_default(int); /*proto*/
static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_cr(int); /*proto*/
static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_lf_or_control(int); /*proto*/
static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_prepend(int); /*proto*/
static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_hangul_l(int); /*proto*/
static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_hangul_lv_or_v(int); /*proto*/
static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_hangul_lvt_or_t(PyObject *); /*proto*/
static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_emoji(int); /*proto*/
static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_emoji_zjw(int); /*proto*/
static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_ri(int); /*proto*/
static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_test_codepoint(int, int); /*proto*/
static PyObject *__pyx_f_8grapheme_6cython_6finder_c_length(PyObject *, int __pyx_skip_dispatch); /*proto*/
static PyObject *__pyx_f_8grapheme_6cython_6finder___pyx_unpickle_FSM__set_state(struct __pyx_obj_8grapheme_6cython_6finder_FSM *, PyObject *); /*proto*/
#define __Pyx_MODULE_NAME "grapheme.cython.finder"
extern int __pyx_module_is_main_grapheme__cython__finder;
int __pyx_module_is_main_grapheme__cython__finder = 0;

/* Implementation of 'grapheme.cython.finder' */
static PyObject *__pyx_builtin_StopIteration;
static PyObject *__pyx_builtin_TypeError;
static const char __pyx_k_[] = "";
static const char __pyx_k_G[] = "G";
static const char __pyx_k_L[] = "L";
static const char __pyx_k_T[] = "T";
static const char __pyx_k_V[] = "V";
static const char __pyx_k_a[] = "a";
static const char __pyx_k_b[] = "b";
static const char __pyx_k_n[] = "n";
static const char __pyx_k_CR[] = "CR";
static const char __pyx_k_LF[] = "LF";
static const char __pyx_k_LV[] = "LV";
static const char __pyx_k_cr[] = "cr";
static const char __pyx_k_ri[] = "ri";
static const char __pyx_k_FSM[] = "FSM";
static const char __pyx_k_LVT[] = "LVT";
static const char __pyx_k_ZWJ[] = "ZWJ";
static const char __pyx_k_cur[] = "cur";
static const char __pyx_k_doc[] = "__doc__";
static const char __pyx_k_new[] = "__new__";
static const char __pyx_k_Enum[] = "Enum";
static const char __pyx_k_dict[] = "__dict__";
static const char __pyx_k_enum[] = "enum";
static const char __pyx_k_main[] = "__main__";
static const char __pyx_k_name[] = "__name__";
static const char __pyx_k_prev[] = "prev";
static const char __pyx_k_test[] = "__test__";
static const char __pyx_k_OTHER[] = "OTHER";
static const char __pyx_k_emoji[] = "emoji";
static const char __pyx_k_index[] = "index";
static const char __pyx_k_EXTEND[] = "EXTEND";
static const char __pyx_k_import[] = "__import__";
static const char __pyx_k_module[] = "__module__";
static const char __pyx_k_pickle[] = "pickle";
static const char __pyx_k_reduce[] = "__reduce__";
static const char __pyx_k_string[] = "string";
static const char __pyx_k_update[] = "update";
static const char __pyx_k_CERTAIN[] = "CERTAIN";
static const char __pyx_k_CONTROL[] = "CONTROL";
static const char __pyx_k_PREPEND[] = "PREPEND";
static const char __pyx_k_certain[] = "certain";
static const char __pyx_k_default[] = "default";
static const char __pyx_k_nobreak[] = "nobreak";
static const char __pyx_k_prepare[] = "__prepare__";
static const char __pyx_k_prepend[] = "prepend";
static const char __pyx_k_NO_BREAK[] = "NO_BREAK";
static const char __pyx_k_POSSIBLE[] = "POSSIBLE";
static const char __pyx_k_getstate[] = "__getstate__";
static const char __pyx_k_hangul_l[] = "hangul_l";
static const char __pyx_k_possible[] = "possible";
static const char __pyx_k_pyx_type[] = "__pyx_type";
static const char __pyx_k_qualname[] = "__qualname__";
static const char __pyx_k_setstate[] = "__setstate__";
static const char __pyx_k_TypeError[] = "TypeError";
static const char __pyx_k_emoji_zjw[] = "emoji_zjw";
static const char __pyx_k_metaclass[] = "__metaclass__";
static const char __pyx_k_pyx_state[] = "__pyx_state";
static const char __pyx_k_reduce_ex[] = "__reduce_ex__";
static const char __pyx_k_pyx_result[] = "__pyx_result";
static const char __pyx_k_pyx_vtable[] = "__pyx_vtable__";
static const char __pyx_k_PickleError[] = "PickleError";
static const char __pyx_k_SPACING_MARK[] = "SPACING_MARK";
static const char __pyx_k_pyx_checksum[] = "__pyx_checksum";
static const char __pyx_k_should_break[] = "should_break";
static const char __pyx_k_stringsource[] = "stringsource";
static const char __pyx_k_StopIteration[] = "StopIteration";
static const char __pyx_k_lf_or_control[] = "lf_or_control";
static const char __pyx_k_reduce_cython[] = "__reduce_cython__";
static const char __pyx_k_hangul_lv_or_v[] = "hangul_lv_or_v";
static const char __pyx_k_hangul_lvt_or_t[] = "hangul_lvt_or_t";
static const char __pyx_k_pyx_PickleError[] = "__pyx_PickleError";
static const char __pyx_k_setstate_cython[] = "__setstate_cython__";
static const char __pyx_k_BreakPossibility[] = "BreakPossibility";
static const char __pyx_k_GraphemeIterator[] = "GraphemeIterator";
static const char __pyx_k_pyx_unpickle_FSM[] = "__pyx_unpickle_FSM";
static const char __pyx_k_REGIONAL_INDICATOR[] = "REGIONAL_INDICATOR";
static const char __pyx_k_cline_in_traceback[] = "cline_in_traceback";
static const char __pyx_k_default_next_state[] = "default_next_state";
static const char __pyx_k_EXTENDED_PICTOGRAPHIC[] = "EXTENDED_PICTOGRAPHIC";
static const char __pyx_k_GraphemePropertyGroup[] = "GraphemePropertyGroup";
static const char __pyx_k_get_break_possibility[] = "get_break_possibility";
static const char __pyx_k_grapheme_cython_finder[] = "grapheme.cython.finder";
static const char __pyx_k_grapheme_cython_finder_pyx[] = "grapheme/cython/finder.pyx";
static const char __pyx_k_get_last_certain_break_index[] = "get_last_certain_break_index";
static const char __pyx_k_Incompatible_checksums_s_vs_0xd4[] = "Incompatible checksums (%s vs 0xd41d8cd = ())";
static const char __pyx_k_grapheme_cython_grapheme_propert[] = "grapheme.cython.grapheme_property_group";
static const char __pyx_k_no_default___reduce___due_to_non[] = "no default __reduce__ due to non-trivial __cinit__";
static PyObject *__pyx_kp_u_;
static PyObject *__pyx_n_s_BreakPossibility;
static PyObject *__pyx_n_s_CERTAIN;
static PyObject *__pyx_n_s_CONTROL;
static PyObject *__pyx_n_s_CR;
static PyObject *__pyx_n_s_EXTEND;
static PyObject *__pyx_n_s_EXTENDED_PICTOGRAPHIC;
static PyObject *__pyx_n_s_Enum;
static PyObject *__pyx_n_s_FSM;
static PyObject *__pyx_n_s_G;
static PyObject *__pyx_n_s_GraphemeIterator;
static PyObject *__pyx_n_s_GraphemePropertyGroup;
static PyObject *__pyx_kp_s_Incompatible_checksums_s_vs_0xd4;
static PyObject *__pyx_n_s_L;
static PyObject *__pyx_n_s_LF;
static PyObject *__pyx_n_s_LV;
static PyObject *__pyx_n_s_LVT;
static PyObject *__pyx_n_s_NO_BREAK;
static PyObject *__pyx_n_s_OTHER;
static PyObject *__pyx_n_s_POSSIBLE;
static PyObject *__pyx_n_s_PREPEND;
static PyObject *__pyx_n_s_PickleError;
static PyObject *__pyx_n_s_REGIONAL_INDICATOR;
static PyObject *__pyx_n_s_SPACING_MARK;
static PyObject *__pyx_n_s_StopIteration;
static PyObject *__pyx_n_s_T;
static PyObject *__pyx_n_s_TypeError;
static PyObject *__pyx_n_s_V;
static PyObject *__pyx_n_s_ZWJ;
static PyObject *__pyx_n_s_a;
static PyObject *__pyx_n_s_b;
static PyObject *__pyx_n_u_certain;
static PyObject *__pyx_n_s_cline_in_traceback;
static PyObject *__pyx_n_s_cr;
static PyObject *__pyx_n_s_cur;
static PyObject *__pyx_n_s_default;
static PyObject *__pyx_n_s_default_next_state;
static PyObject *__pyx_n_s_dict;
static PyObject *__pyx_n_s_doc;
static PyObject *__pyx_n_s_emoji;
static PyObject *__pyx_n_s_emoji_zjw;
static PyObject *__pyx_n_s_enum;
static PyObject *__pyx_n_s_get_break_possibility;
static PyObject *__pyx_n_s_get_last_certain_break_index;
static PyObject *__pyx_n_s_getstate;
static PyObject *__pyx_n_s_grapheme_cython_finder;
static PyObject *__pyx_kp_s_grapheme_cython_finder_pyx;
static PyObject *__pyx_n_s_grapheme_cython_grapheme_propert;
static PyObject *__pyx_n_s_hangul_l;
static PyObject *__pyx_n_s_hangul_lv_or_v;
static PyObject *__pyx_n_s_hangul_lvt_or_t;
static PyObject *__pyx_n_s_import;
static PyObject *__pyx_n_s_index;
static PyObject *__pyx_n_s_lf_or_control;
static PyObject *__pyx_n_s_main;
static PyObject *__pyx_n_s_metaclass;
static PyObject *__pyx_n_s_module;
static PyObject *__pyx_n_s_n;
static PyObject *__pyx_n_s_name;
static PyObject *__pyx_n_s_new;
static PyObject *__pyx_kp_s_no_default___reduce___due_to_non;
static PyObject *__pyx_n_u_nobreak;
static PyObject *__pyx_n_s_pickle;
static PyObject *__pyx_n_u_possible;
static PyObject *__pyx_n_s_prepare;
static PyObject *__pyx_n_s_prepend;
static PyObject *__pyx_n_s_prev;
static PyObject *__pyx_n_s_pyx_PickleError;
static PyObject *__pyx_n_s_pyx_checksum;
static PyObject *__pyx_n_s_pyx_result;
static PyObject *__pyx_n_s_pyx_state;
static PyObject *__pyx_n_s_pyx_type;
static PyObject *__pyx_n_s_pyx_unpickle_FSM;
static PyObject *__pyx_n_s_pyx_vtable;
static PyObject *__pyx_n_s_qualname;
static PyObject *__pyx_n_s_reduce;
static PyObject *__pyx_n_s_reduce_cython;
static PyObject *__pyx_n_s_reduce_ex;
static PyObject *__pyx_n_s_ri;
static PyObject *__pyx_n_s_setstate;
static PyObject *__pyx_n_s_setstate_cython;
static PyObject *__pyx_n_s_should_break;
static PyObject *__pyx_n_s_string;
static PyObject *__pyx_kp_s_stringsource;
static PyObject *__pyx_n_s_test;
static PyObject *__pyx_n_s_update;
static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_default(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_2default_next_state(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n, PyObject *__pyx_v_should_break); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_4cr(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_6lf_or_control(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_8prepend(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_10hangul_l(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_12hangul_lv_or_v(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_14hangul_lvt_or_t(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_16emoji(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_18emoji_zjw(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_20ri(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_22__reduce_cython__(struct __pyx_obj_8grapheme_6cython_6finder_FSM *__pyx_v_self); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_24__setstate_cython__(struct __pyx_obj_8grapheme_6cython_6finder_FSM *__pyx_v_self, PyObject *__pyx_v___pyx_state); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_get_break_possibility(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_a, PyObject *__pyx_v_b); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_2get_last_certain_break_index(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_string, PyObject *__pyx_v_index); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_4c_length(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_string); /* proto */
static int __pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator___cinit__(struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *__pyx_v_self, PyObject *__pyx_v_string); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator_2__iter__(struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *__pyx_v_self); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator_4__next__(struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *__pyx_v_self); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator_6__reduce_cython__(CYTHON_UNUSED struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *__pyx_v_self); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator_8__setstate_cython__(CYTHON_UNUSED struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *__pyx_v_self, CYTHON_UNUSED PyObject *__pyx_v___pyx_state); /* proto */
static PyObject *__pyx_pf_8grapheme_6cython_6finder_6__pyx_unpickle_FSM(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v___pyx_type, long __pyx_v___pyx_checksum, PyObject *__pyx_v___pyx_state); /* proto */
static PyObject *__pyx_tp_new_8grapheme_6cython_6finder_FSM(PyTypeObject *t, PyObject *a, PyObject *k); /*proto*/
static PyObject *__pyx_tp_new_8grapheme_6cython_6finder_GraphemeIterator(PyTypeObject *t, PyObject *a, PyObject *k); /*proto*/
static PyObject *__pyx_int_0;
static PyObject *__pyx_int_1;
static PyObject *__pyx_int_222419149;
static PyObject *__pyx_tuple__2;
static PyObject *__pyx_tuple__3;
static PyObject *__pyx_tuple__4;
static PyObject *__pyx_tuple__6;
static PyObject *__pyx_tuple__8;
static PyObject *__pyx_codeobj__5;
static PyObject *__pyx_codeobj__7;
static PyObject *__pyx_codeobj__9;
/* Late includes */

/* "grapheme/cython/finder.pyx":28
 * 
 * 
 * cdef inline int default(int n):             # <<<<<<<<<<<<<<
 *     if n == G_OTHER:
 *         return SHOULD_BREAK_BIT | FSM_default
 */

static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_default(int __pyx_v_n) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  int __pyx_t_2;
  int __pyx_t_3;
  __Pyx_RefNannySetupContext("default", 0);

  /* "grapheme/cython/finder.pyx":29
 * 
 * cdef inline int default(int n):
 *     if n == G_OTHER:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_default
 *     elif n == G_CR:
 */
  __pyx_t_1 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_OTHER) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":30
 * cdef inline int default(int n):
 *     if n == G_OTHER:
 *         return SHOULD_BREAK_BIT | FSM_default             # <<<<<<<<<<<<<<
 *     elif n == G_CR:
 *         return SHOULD_BREAK_BIT | FSM_cr
 */
    __pyx_r = (0x40000000 | __pyx_v_8grapheme_6cython_9constants_FSM_default);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":29
 * 
 * cdef inline int default(int n):
 *     if n == G_OTHER:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_default
 *     elif n == G_CR:
 */
  }

  /* "grapheme/cython/finder.pyx":31
 *     if n == G_OTHER:
 *         return SHOULD_BREAK_BIT | FSM_default
 *     elif n == G_CR:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_cr
 *     elif n in [G_LF, G_CONTROL]:
 */
  __pyx_t_1 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_CR) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":32
 *         return SHOULD_BREAK_BIT | FSM_default
 *     elif n == G_CR:
 *         return SHOULD_BREAK_BIT | FSM_cr             # <<<<<<<<<<<<<<
 *     elif n in [G_LF, G_CONTROL]:
 *         return SHOULD_BREAK_BIT | FSM_lf_or_control
 */
    __pyx_r = (0x40000000 | __pyx_v_8grapheme_6cython_9constants_FSM_cr);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":31
 *     if n == G_OTHER:
 *         return SHOULD_BREAK_BIT | FSM_default
 *     elif n == G_CR:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_cr
 *     elif n in [G_LF, G_CONTROL]:
 */
  }

  /* "grapheme/cython/finder.pyx":33
 *     elif n == G_CR:
 *         return SHOULD_BREAK_BIT | FSM_cr
 *     elif n in [G_LF, G_CONTROL]:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_lf_or_control
 *     elif n in [G_EXTEND, G_SPACING_MARK, G_ZWJ]:
 */
  __pyx_t_2 = __pyx_v_n;
  __pyx_t_3 = ((__pyx_t_2 == __pyx_v_8grapheme_6cython_9constants_G_LF) != 0);
  if (!__pyx_t_3) {
  } else {
    __pyx_t_1 = __pyx_t_3;
    goto __pyx_L4_bool_binop_done;
  }
  __pyx_t_3 = ((__pyx_t_2 == __pyx_v_8grapheme_6cython_9constants_G_CONTROL) != 0);
  __pyx_t_1 = __pyx_t_3;
  __pyx_L4_bool_binop_done:;
  __pyx_t_3 = (__pyx_t_1 != 0);
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":34
 *         return SHOULD_BREAK_BIT | FSM_cr
 *     elif n in [G_LF, G_CONTROL]:
 *         return SHOULD_BREAK_BIT | FSM_lf_or_control             # <<<<<<<<<<<<<<
 *     elif n in [G_EXTEND, G_SPACING_MARK, G_ZWJ]:
 *         return FSM_default
 */
    __pyx_r = (0x40000000 | __pyx_v_8grapheme_6cython_9constants_FSM_lf_or_control);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":33
 *     elif n == G_CR:
 *         return SHOULD_BREAK_BIT | FSM_cr
 *     elif n in [G_LF, G_CONTROL]:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_lf_or_control
 *     elif n in [G_EXTEND, G_SPACING_MARK, G_ZWJ]:
 */
  }

  /* "grapheme/cython/finder.pyx":35
 *     elif n in [G_LF, G_CONTROL]:
 *         return SHOULD_BREAK_BIT | FSM_lf_or_control
 *     elif n in [G_EXTEND, G_SPACING_MARK, G_ZWJ]:             # <<<<<<<<<<<<<<
 *         return FSM_default
 *     elif n == G_EXTENDED_PICTOGRAPHIC:
 */
  __pyx_t_2 = __pyx_v_n;
  __pyx_t_1 = ((__pyx_t_2 == __pyx_v_8grapheme_6cython_9constants_G_EXTEND) != 0);
  if (!__pyx_t_1) {
  } else {
    __pyx_t_3 = __pyx_t_1;
    goto __pyx_L6_bool_binop_done;
  }
  __pyx_t_1 = ((__pyx_t_2 == __pyx_v_8grapheme_6cython_9constants_G_SPACING_MARK) != 0);
  if (!__pyx_t_1) {
  } else {
    __pyx_t_3 = __pyx_t_1;
    goto __pyx_L6_bool_binop_done;
  }
  __pyx_t_1 = ((__pyx_t_2 == __pyx_v_8grapheme_6cython_9constants_G_ZWJ) != 0);
  __pyx_t_3 = __pyx_t_1;
  __pyx_L6_bool_binop_done:;
  __pyx_t_1 = (__pyx_t_3 != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":36
 *         return SHOULD_BREAK_BIT | FSM_lf_or_control
 *     elif n in [G_EXTEND, G_SPACING_MARK, G_ZWJ]:
 *         return FSM_default             # <<<<<<<<<<<<<<
 *     elif n == G_EXTENDED_PICTOGRAPHIC:
 *         return SHOULD_BREAK_BIT | FSM_emoji
 */
    __pyx_r = __pyx_v_8grapheme_6cython_9constants_FSM_default;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":35
 *     elif n in [G_LF, G_CONTROL]:
 *         return SHOULD_BREAK_BIT | FSM_lf_or_control
 *     elif n in [G_EXTEND, G_SPACING_MARK, G_ZWJ]:             # <<<<<<<<<<<<<<
 *         return FSM_default
 *     elif n == G_EXTENDED_PICTOGRAPHIC:
 */
  }

  /* "grapheme/cython/finder.pyx":37
 *     elif n in [G_EXTEND, G_SPACING_MARK, G_ZWJ]:
 *         return FSM_default
 *     elif n == G_EXTENDED_PICTOGRAPHIC:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_emoji
 *     elif n == G_REGIONAL_INDICATOR:
 */
  __pyx_t_1 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_EXTENDED_PICTOGRAPHIC) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":38
 *         return FSM_default
 *     elif n == G_EXTENDED_PICTOGRAPHIC:
 *         return SHOULD_BREAK_BIT | FSM_emoji             # <<<<<<<<<<<<<<
 *     elif n == G_REGIONAL_INDICATOR:
 *         return SHOULD_BREAK_BIT | FSM_ri
 */
    __pyx_r = (0x40000000 | __pyx_v_8grapheme_6cython_9constants_FSM_emoji);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":37
 *     elif n in [G_EXTEND, G_SPACING_MARK, G_ZWJ]:
 *         return FSM_default
 *     elif n == G_EXTENDED_PICTOGRAPHIC:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_emoji
 *     elif n == G_REGIONAL_INDICATOR:
 */
  }

  /* "grapheme/cython/finder.pyx":39
 *     elif n == G_EXTENDED_PICTOGRAPHIC:
 *         return SHOULD_BREAK_BIT | FSM_emoji
 *     elif n == G_REGIONAL_INDICATOR:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_ri
 *     elif n == G_L:
 */
  __pyx_t_1 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_REGIONAL_INDICATOR) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":40
 *         return SHOULD_BREAK_BIT | FSM_emoji
 *     elif n == G_REGIONAL_INDICATOR:
 *         return SHOULD_BREAK_BIT | FSM_ri             # <<<<<<<<<<<<<<
 *     elif n == G_L:
 *         return SHOULD_BREAK_BIT | FSM_hangul_l
 */
    __pyx_r = (0x40000000 | __pyx_v_8grapheme_6cython_9constants_FSM_ri);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":39
 *     elif n == G_EXTENDED_PICTOGRAPHIC:
 *         return SHOULD_BREAK_BIT | FSM_emoji
 *     elif n == G_REGIONAL_INDICATOR:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_ri
 *     elif n == G_L:
 */
  }

  /* "grapheme/cython/finder.pyx":41
 *     elif n == G_REGIONAL_INDICATOR:
 *         return SHOULD_BREAK_BIT | FSM_ri
 *     elif n == G_L:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_hangul_l
 *     elif n in [G_LV, G_V]:
 */
  __pyx_t_1 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_L) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":42
 *         return SHOULD_BREAK_BIT | FSM_ri
 *     elif n == G_L:
 *         return SHOULD_BREAK_BIT | FSM_hangul_l             # <<<<<<<<<<<<<<
 *     elif n in [G_LV, G_V]:
 *         return SHOULD_BREAK_BIT | FSM_hangul_lv_or_v
 */
    __pyx_r = (0x40000000 | __pyx_v_8grapheme_6cython_9constants_FSM_hangul_l);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":41
 *     elif n == G_REGIONAL_INDICATOR:
 *         return SHOULD_BREAK_BIT | FSM_ri
 *     elif n == G_L:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_hangul_l
 *     elif n in [G_LV, G_V]:
 */
  }

  /* "grapheme/cython/finder.pyx":43
 *     elif n == G_L:
 *         return SHOULD_BREAK_BIT | FSM_hangul_l
 *     elif n in [G_LV, G_V]:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_hangul_lv_or_v
 *     elif n in [G_LVT, G_T]:
 */
  __pyx_t_2 = __pyx_v_n;
  __pyx_t_3 = ((__pyx_t_2 == __pyx_v_8grapheme_6cython_9constants_G_LV) != 0);
  if (!__pyx_t_3) {
  } else {
    __pyx_t_1 = __pyx_t_3;
    goto __pyx_L9_bool_binop_done;
  }
  __pyx_t_3 = ((__pyx_t_2 == __pyx_v_8grapheme_6cython_9constants_G_V) != 0);
  __pyx_t_1 = __pyx_t_3;
  __pyx_L9_bool_binop_done:;
  __pyx_t_3 = (__pyx_t_1 != 0);
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":44
 *         return SHOULD_BREAK_BIT | FSM_hangul_l
 *     elif n in [G_LV, G_V]:
 *         return SHOULD_BREAK_BIT | FSM_hangul_lv_or_v             # <<<<<<<<<<<<<<
 *     elif n in [G_LVT, G_T]:
 *         return SHOULD_BREAK_BIT | FSM_hangul_lvt_or_t
 */
    __pyx_r = (0x40000000 | __pyx_v_8grapheme_6cython_9constants_FSM_hangul_lv_or_v);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":43
 *     elif n == G_L:
 *         return SHOULD_BREAK_BIT | FSM_hangul_l
 *     elif n in [G_LV, G_V]:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_hangul_lv_or_v
 *     elif n in [G_LVT, G_T]:
 */
  }

  /* "grapheme/cython/finder.pyx":45
 *     elif n in [G_LV, G_V]:
 *         return SHOULD_BREAK_BIT | FSM_hangul_lv_or_v
 *     elif n in [G_LVT, G_T]:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_hangul_lvt_or_t
 *     elif n == G_PREPEND:
 */
  __pyx_t_2 = __pyx_v_n;
  __pyx_t_1 = ((__pyx_t_2 == __pyx_v_8grapheme_6cython_9constants_G_LVT) != 0);
  if (!__pyx_t_1) {
  } else {
    __pyx_t_3 = __pyx_t_1;
    goto __pyx_L11_bool_binop_done;
  }
  __pyx_t_1 = ((__pyx_t_2 == __pyx_v_8grapheme_6cython_9constants_G_T) != 0);
  __pyx_t_3 = __pyx_t_1;
  __pyx_L11_bool_binop_done:;
  __pyx_t_1 = (__pyx_t_3 != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":46
 *         return SHOULD_BREAK_BIT | FSM_hangul_lv_or_v
 *     elif n in [G_LVT, G_T]:
 *         return SHOULD_BREAK_BIT | FSM_hangul_lvt_or_t             # <<<<<<<<<<<<<<
 *     elif n == G_PREPEND:
 *         return SHOULD_BREAK_BIT | FSM_prepend
 */
    __pyx_r = (0x40000000 | __pyx_v_8grapheme_6cython_9constants_FSM_hangul_lvt_or_t);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":45
 *     elif n in [G_LV, G_V]:
 *         return SHOULD_BREAK_BIT | FSM_hangul_lv_or_v
 *     elif n in [G_LVT, G_T]:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_hangul_lvt_or_t
 *     elif n == G_PREPEND:
 */
  }

  /* "grapheme/cython/finder.pyx":47
 *     elif n in [G_LVT, G_T]:
 *         return SHOULD_BREAK_BIT | FSM_hangul_lvt_or_t
 *     elif n == G_PREPEND:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_prepend
 *     else:
 */
  __pyx_t_1 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_PREPEND) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":48
 *         return SHOULD_BREAK_BIT | FSM_hangul_lvt_or_t
 *     elif n == G_PREPEND:
 *         return SHOULD_BREAK_BIT | FSM_prepend             # <<<<<<<<<<<<<<
 *     else:
 *         return SHOULD_BREAK_BIT | FSM_default
 */
    __pyx_r = (0x40000000 | __pyx_v_8grapheme_6cython_9constants_FSM_prepend);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":47
 *     elif n in [G_LVT, G_T]:
 *         return SHOULD_BREAK_BIT | FSM_hangul_lvt_or_t
 *     elif n == G_PREPEND:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_prepend
 *     else:
 */
  }

  /* "grapheme/cython/finder.pyx":50
 *         return SHOULD_BREAK_BIT | FSM_prepend
 *     else:
 *         return SHOULD_BREAK_BIT | FSM_default             # <<<<<<<<<<<<<<
 * 
 * 
 */
  /*else*/ {
    __pyx_r = (0x40000000 | __pyx_v_8grapheme_6cython_9constants_FSM_default);
    goto __pyx_L0;
  }

  /* "grapheme/cython/finder.pyx":28
 * 
 * 
 * cdef inline int default(int n):             # <<<<<<<<<<<<<<
 *     if n == G_OTHER:
 *         return SHOULD_BREAK_BIT | FSM_default
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":53
 * 
 * 
 * cdef inline int cr(int n):             # <<<<<<<<<<<<<<
 *     if n == G_LF:
 *         return FSM_lf_or_control
 */

static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_cr(int __pyx_v_n) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  __Pyx_RefNannySetupContext("cr", 0);

  /* "grapheme/cython/finder.pyx":54
 * 
 * cdef inline int cr(int n):
 *     if n == G_LF:             # <<<<<<<<<<<<<<
 *         return FSM_lf_or_control
 *     return default(n) | SHOULD_BREAK_BIT
 */
  __pyx_t_1 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_LF) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":55
 * cdef inline int cr(int n):
 *     if n == G_LF:
 *         return FSM_lf_or_control             # <<<<<<<<<<<<<<
 *     return default(n) | SHOULD_BREAK_BIT
 * 
 */
    __pyx_r = __pyx_v_8grapheme_6cython_9constants_FSM_lf_or_control;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":54
 * 
 * cdef inline int cr(int n):
 *     if n == G_LF:             # <<<<<<<<<<<<<<
 *         return FSM_lf_or_control
 *     return default(n) | SHOULD_BREAK_BIT
 */
  }

  /* "grapheme/cython/finder.pyx":56
 *     if n == G_LF:
 *         return FSM_lf_or_control
 *     return default(n) | SHOULD_BREAK_BIT             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __pyx_r = (__pyx_f_8grapheme_6cython_6finder_default(__pyx_v_n) | 0x40000000);
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":53
 * 
 * 
 * cdef inline int cr(int n):             # <<<<<<<<<<<<<<
 *     if n == G_LF:
 *         return FSM_lf_or_control
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":59
 * 
 * 
 * cdef inline int lf_or_control(int n):             # <<<<<<<<<<<<<<
 *     return default(n) | SHOULD_BREAK_BIT
 * 
 */

static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_lf_or_control(int __pyx_v_n) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("lf_or_control", 0);

  /* "grapheme/cython/finder.pyx":60
 * 
 * cdef inline int lf_or_control(int n):
 *     return default(n) | SHOULD_BREAK_BIT             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __pyx_r = (__pyx_f_8grapheme_6cython_6finder_default(__pyx_v_n) | 0x40000000);
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":59
 * 
 * 
 * cdef inline int lf_or_control(int n):             # <<<<<<<<<<<<<<
 *     return default(n) | SHOULD_BREAK_BIT
 * 
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":63
 * 
 * 
 * cdef inline int prepend(int n):             # <<<<<<<<<<<<<<
 *     if n in [G_CONTROL, G_LF]:
 *         return SHOULD_BREAK_BIT | FSM_default
 */

static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_prepend(int __pyx_v_n) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  int __pyx_t_2;
  int __pyx_t_3;
  __Pyx_RefNannySetupContext("prepend", 0);

  /* "grapheme/cython/finder.pyx":64
 * 
 * cdef inline int prepend(int n):
 *     if n in [G_CONTROL, G_LF]:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_default
 *     elif n == G_CR:
 */
  __pyx_t_1 = __pyx_v_n;
  __pyx_t_3 = ((__pyx_t_1 == __pyx_v_8grapheme_6cython_9constants_G_CONTROL) != 0);
  if (!__pyx_t_3) {
  } else {
    __pyx_t_2 = __pyx_t_3;
    goto __pyx_L4_bool_binop_done;
  }
  __pyx_t_3 = ((__pyx_t_1 == __pyx_v_8grapheme_6cython_9constants_G_LF) != 0);
  __pyx_t_2 = __pyx_t_3;
  __pyx_L4_bool_binop_done:;
  __pyx_t_3 = (__pyx_t_2 != 0);
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":65
 * cdef inline int prepend(int n):
 *     if n in [G_CONTROL, G_LF]:
 *         return SHOULD_BREAK_BIT | FSM_default             # <<<<<<<<<<<<<<
 *     elif n == G_CR:
 *         return SHOULD_BREAK_BIT | FSM_cr
 */
    __pyx_r = (0x40000000 | __pyx_v_8grapheme_6cython_9constants_FSM_default);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":64
 * 
 * cdef inline int prepend(int n):
 *     if n in [G_CONTROL, G_LF]:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_default
 *     elif n == G_CR:
 */
  }

  /* "grapheme/cython/finder.pyx":66
 *     if n in [G_CONTROL, G_LF]:
 *         return SHOULD_BREAK_BIT | FSM_default
 *     elif n == G_CR:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_cr
 *     else:
 */
  __pyx_t_3 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_CR) != 0);
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":67
 *         return SHOULD_BREAK_BIT | FSM_default
 *     elif n == G_CR:
 *         return SHOULD_BREAK_BIT | FSM_cr             # <<<<<<<<<<<<<<
 *     else:
 *         return default(n) & SHOULD_BREAK_BIT_INVERSED
 */
    __pyx_r = (0x40000000 | __pyx_v_8grapheme_6cython_9constants_FSM_cr);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":66
 *     if n in [G_CONTROL, G_LF]:
 *         return SHOULD_BREAK_BIT | FSM_default
 *     elif n == G_CR:             # <<<<<<<<<<<<<<
 *         return SHOULD_BREAK_BIT | FSM_cr
 *     else:
 */
  }

  /* "grapheme/cython/finder.pyx":69
 *         return SHOULD_BREAK_BIT | FSM_cr
 *     else:
 *         return default(n) & SHOULD_BREAK_BIT_INVERSED             # <<<<<<<<<<<<<<
 * 
 * 
 */
  /*else*/ {
    __pyx_r = (__pyx_f_8grapheme_6cython_6finder_default(__pyx_v_n) & -1073741825L);
    goto __pyx_L0;
  }

  /* "grapheme/cython/finder.pyx":63
 * 
 * 
 * cdef inline int prepend(int n):             # <<<<<<<<<<<<<<
 *     if n in [G_CONTROL, G_LF]:
 *         return SHOULD_BREAK_BIT | FSM_default
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":72
 * 
 * 
 * cdef inline int hangul_l(int n):             # <<<<<<<<<<<<<<
 *     if n in [G_V, G_LV]:
 *         return FSM_hangul_lv_or_v
 */

static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_hangul_l(int __pyx_v_n) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  int __pyx_t_2;
  int __pyx_t_3;
  __Pyx_RefNannySetupContext("hangul_l", 0);

  /* "grapheme/cython/finder.pyx":73
 * 
 * cdef inline int hangul_l(int n):
 *     if n in [G_V, G_LV]:             # <<<<<<<<<<<<<<
 *         return FSM_hangul_lv_or_v
 *     elif n == G_LVT:
 */
  __pyx_t_1 = __pyx_v_n;
  __pyx_t_3 = ((__pyx_t_1 == __pyx_v_8grapheme_6cython_9constants_G_V) != 0);
  if (!__pyx_t_3) {
  } else {
    __pyx_t_2 = __pyx_t_3;
    goto __pyx_L4_bool_binop_done;
  }
  __pyx_t_3 = ((__pyx_t_1 == __pyx_v_8grapheme_6cython_9constants_G_LV) != 0);
  __pyx_t_2 = __pyx_t_3;
  __pyx_L4_bool_binop_done:;
  __pyx_t_3 = (__pyx_t_2 != 0);
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":74
 * cdef inline int hangul_l(int n):
 *     if n in [G_V, G_LV]:
 *         return FSM_hangul_lv_or_v             # <<<<<<<<<<<<<<
 *     elif n == G_LVT:
 *         return FSM_hangul_lvt_or_t
 */
    __pyx_r = __pyx_v_8grapheme_6cython_9constants_FSM_hangul_lv_or_v;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":73
 * 
 * cdef inline int hangul_l(int n):
 *     if n in [G_V, G_LV]:             # <<<<<<<<<<<<<<
 *         return FSM_hangul_lv_or_v
 *     elif n == G_LVT:
 */
  }

  /* "grapheme/cython/finder.pyx":75
 *     if n in [G_V, G_LV]:
 *         return FSM_hangul_lv_or_v
 *     elif n == G_LVT:             # <<<<<<<<<<<<<<
 *         return FSM_hangul_lvt_or_t
 *     elif n == G_L:
 */
  __pyx_t_3 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_LVT) != 0);
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":76
 *         return FSM_hangul_lv_or_v
 *     elif n == G_LVT:
 *         return FSM_hangul_lvt_or_t             # <<<<<<<<<<<<<<
 *     elif n == G_L:
 *         return FSM_hangul_l
 */
    __pyx_r = __pyx_v_8grapheme_6cython_9constants_FSM_hangul_lvt_or_t;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":75
 *     if n in [G_V, G_LV]:
 *         return FSM_hangul_lv_or_v
 *     elif n == G_LVT:             # <<<<<<<<<<<<<<
 *         return FSM_hangul_lvt_or_t
 *     elif n == G_L:
 */
  }

  /* "grapheme/cython/finder.pyx":77
 *     elif n == G_LVT:
 *         return FSM_hangul_lvt_or_t
 *     elif n == G_L:             # <<<<<<<<<<<<<<
 *         return FSM_hangul_l
 *     else:
 */
  __pyx_t_3 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_L) != 0);
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":78
 *         return FSM_hangul_lvt_or_t
 *     elif n == G_L:
 *         return FSM_hangul_l             # <<<<<<<<<<<<<<
 *     else:
 *         return default(n)
 */
    __pyx_r = __pyx_v_8grapheme_6cython_9constants_FSM_hangul_l;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":77
 *     elif n == G_LVT:
 *         return FSM_hangul_lvt_or_t
 *     elif n == G_L:             # <<<<<<<<<<<<<<
 *         return FSM_hangul_l
 *     else:
 */
  }

  /* "grapheme/cython/finder.pyx":80
 *         return FSM_hangul_l
 *     else:
 *         return default(n)             # <<<<<<<<<<<<<<
 * 
 * 
 */
  /*else*/ {
    __pyx_r = __pyx_f_8grapheme_6cython_6finder_default(__pyx_v_n);
    goto __pyx_L0;
  }

  /* "grapheme/cython/finder.pyx":72
 * 
 * 
 * cdef inline int hangul_l(int n):             # <<<<<<<<<<<<<<
 *     if n in [G_V, G_LV]:
 *         return FSM_hangul_lv_or_v
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":83
 * 
 * 
 * cdef inline int hangul_lv_or_v(int n):             # <<<<<<<<<<<<<<
 *     if n == G_V:
 *         return FSM_hangul_lv_or_v
 */

static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_hangul_lv_or_v(int __pyx_v_n) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  __Pyx_RefNannySetupContext("hangul_lv_or_v", 0);

  /* "grapheme/cython/finder.pyx":84
 * 
 * cdef inline int hangul_lv_or_v(int n):
 *     if n == G_V:             # <<<<<<<<<<<<<<
 *         return FSM_hangul_lv_or_v
 *     elif n == G_T:
 */
  __pyx_t_1 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_V) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":85
 * cdef inline int hangul_lv_or_v(int n):
 *     if n == G_V:
 *         return FSM_hangul_lv_or_v             # <<<<<<<<<<<<<<
 *     elif n == G_T:
 *         return FSM_hangul_lvt_or_t
 */
    __pyx_r = __pyx_v_8grapheme_6cython_9constants_FSM_hangul_lv_or_v;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":84
 * 
 * cdef inline int hangul_lv_or_v(int n):
 *     if n == G_V:             # <<<<<<<<<<<<<<
 *         return FSM_hangul_lv_or_v
 *     elif n == G_T:
 */
  }

  /* "grapheme/cython/finder.pyx":86
 *     if n == G_V:
 *         return FSM_hangul_lv_or_v
 *     elif n == G_T:             # <<<<<<<<<<<<<<
 *         return FSM_hangul_lvt_or_t
 *     else:
 */
  __pyx_t_1 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_T) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":87
 *         return FSM_hangul_lv_or_v
 *     elif n == G_T:
 *         return FSM_hangul_lvt_or_t             # <<<<<<<<<<<<<<
 *     else:
 *         return default(n)
 */
    __pyx_r = __pyx_v_8grapheme_6cython_9constants_FSM_hangul_lvt_or_t;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":86
 *     if n == G_V:
 *         return FSM_hangul_lv_or_v
 *     elif n == G_T:             # <<<<<<<<<<<<<<
 *         return FSM_hangul_lvt_or_t
 *     else:
 */
  }

  /* "grapheme/cython/finder.pyx":89
 *         return FSM_hangul_lvt_or_t
 *     else:
 *         return default(n)             # <<<<<<<<<<<<<<
 * 
 * cdef inline int hangul_lvt_or_t(n):
 */
  /*else*/ {
    __pyx_r = __pyx_f_8grapheme_6cython_6finder_default(__pyx_v_n);
    goto __pyx_L0;
  }

  /* "grapheme/cython/finder.pyx":83
 * 
 * 
 * cdef inline int hangul_lv_or_v(int n):             # <<<<<<<<<<<<<<
 *     if n == G_V:
 *         return FSM_hangul_lv_or_v
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":91
 *         return default(n)
 * 
 * cdef inline int hangul_lvt_or_t(n):             # <<<<<<<<<<<<<<
 *     if n == G_T:
 *         return FSM_hangul_lvt_or_t
 */

static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_hangul_lvt_or_t(PyObject *__pyx_v_n) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  int __pyx_t_3;
  int __pyx_t_4;
  __Pyx_RefNannySetupContext("hangul_lvt_or_t", 0);

  /* "grapheme/cython/finder.pyx":92
 * 
 * cdef inline int hangul_lvt_or_t(n):
 *     if n == G_T:             # <<<<<<<<<<<<<<
 *         return FSM_hangul_lvt_or_t
 *     return default(n)
 */
  __pyx_t_1 = __Pyx_PyInt_From_int(__pyx_v_8grapheme_6cython_9constants_G_T); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 92, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = PyObject_RichCompare(__pyx_v_n, __pyx_t_1, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 92, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 92, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":93
 * cdef inline int hangul_lvt_or_t(n):
 *     if n == G_T:
 *         return FSM_hangul_lvt_or_t             # <<<<<<<<<<<<<<
 *     return default(n)
 * 
 */
    __pyx_r = __pyx_v_8grapheme_6cython_9constants_FSM_hangul_lvt_or_t;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":92
 * 
 * cdef inline int hangul_lvt_or_t(n):
 *     if n == G_T:             # <<<<<<<<<<<<<<
 *         return FSM_hangul_lvt_or_t
 *     return default(n)
 */
  }

  /* "grapheme/cython/finder.pyx":94
 *     if n == G_T:
 *         return FSM_hangul_lvt_or_t
 *     return default(n)             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __pyx_t_4 = __Pyx_PyInt_As_int(__pyx_v_n); if (unlikely((__pyx_t_4 == (int)-1) && PyErr_Occurred())) __PYX_ERR(0, 94, __pyx_L1_error)
  __pyx_r = __pyx_f_8grapheme_6cython_6finder_default(__pyx_t_4);
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":91
 *         return default(n)
 * 
 * cdef inline int hangul_lvt_or_t(n):             # <<<<<<<<<<<<<<
 *     if n == G_T:
 *         return FSM_hangul_lvt_or_t
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_WriteUnraisable("grapheme.cython.finder.hangul_lvt_or_t", __pyx_clineno, __pyx_lineno, __pyx_filename, 1, 0);
  __pyx_r = 0;
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":97
 * 
 * 
 * cdef inline int emoji(int n):             # <<<<<<<<<<<<<<
 *     if n == G_EXTEND:
 *         return FSM_emoji
 */

static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_emoji(int __pyx_v_n) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  __Pyx_RefNannySetupContext("emoji", 0);

  /* "grapheme/cython/finder.pyx":98
 * 
 * cdef inline int emoji(int n):
 *     if n == G_EXTEND:             # <<<<<<<<<<<<<<
 *         return FSM_emoji
 *     elif n == G_ZWJ:
 */
  __pyx_t_1 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_EXTEND) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":99
 * cdef inline int emoji(int n):
 *     if n == G_EXTEND:
 *         return FSM_emoji             # <<<<<<<<<<<<<<
 *     elif n == G_ZWJ:
 *         return FSM_emoji_zjw
 */
    __pyx_r = __pyx_v_8grapheme_6cython_9constants_FSM_emoji;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":98
 * 
 * cdef inline int emoji(int n):
 *     if n == G_EXTEND:             # <<<<<<<<<<<<<<
 *         return FSM_emoji
 *     elif n == G_ZWJ:
 */
  }

  /* "grapheme/cython/finder.pyx":100
 *     if n == G_EXTEND:
 *         return FSM_emoji
 *     elif n == G_ZWJ:             # <<<<<<<<<<<<<<
 *         return FSM_emoji_zjw
 *     return default(n)
 */
  __pyx_t_1 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_ZWJ) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":101
 *         return FSM_emoji
 *     elif n == G_ZWJ:
 *         return FSM_emoji_zjw             # <<<<<<<<<<<<<<
 *     return default(n)
 * 
 */
    __pyx_r = __pyx_v_8grapheme_6cython_9constants_FSM_emoji_zjw;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":100
 *     if n == G_EXTEND:
 *         return FSM_emoji
 *     elif n == G_ZWJ:             # <<<<<<<<<<<<<<
 *         return FSM_emoji_zjw
 *     return default(n)
 */
  }

  /* "grapheme/cython/finder.pyx":102
 *     elif n == G_ZWJ:
 *         return FSM_emoji_zjw
 *     return default(n)             # <<<<<<<<<<<<<<
 * 
 * cdef inline int emoji_zjw(int n):
 */
  __pyx_r = __pyx_f_8grapheme_6cython_6finder_default(__pyx_v_n);
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":97
 * 
 * 
 * cdef inline int emoji(int n):             # <<<<<<<<<<<<<<
 *     if n == G_EXTEND:
 *         return FSM_emoji
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":104
 *     return default(n)
 * 
 * cdef inline int emoji_zjw(int n):             # <<<<<<<<<<<<<<
 *     if n == G_EXTENDED_PICTOGRAPHIC:
 *         return FSM_emoji
 */

static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_emoji_zjw(int __pyx_v_n) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  __Pyx_RefNannySetupContext("emoji_zjw", 0);

  /* "grapheme/cython/finder.pyx":105
 * 
 * cdef inline int emoji_zjw(int n):
 *     if n == G_EXTENDED_PICTOGRAPHIC:             # <<<<<<<<<<<<<<
 *         return FSM_emoji
 *     return default(n)
 */
  __pyx_t_1 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_EXTENDED_PICTOGRAPHIC) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":106
 * cdef inline int emoji_zjw(int n):
 *     if n == G_EXTENDED_PICTOGRAPHIC:
 *         return FSM_emoji             # <<<<<<<<<<<<<<
 *     return default(n)
 * 
 */
    __pyx_r = __pyx_v_8grapheme_6cython_9constants_FSM_emoji;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":105
 * 
 * cdef inline int emoji_zjw(int n):
 *     if n == G_EXTENDED_PICTOGRAPHIC:             # <<<<<<<<<<<<<<
 *         return FSM_emoji
 *     return default(n)
 */
  }

  /* "grapheme/cython/finder.pyx":107
 *     if n == G_EXTENDED_PICTOGRAPHIC:
 *         return FSM_emoji
 *     return default(n)             # <<<<<<<<<<<<<<
 * 
 * cdef inline int ri(int n):
 */
  __pyx_r = __pyx_f_8grapheme_6cython_6finder_default(__pyx_v_n);
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":104
 *     return default(n)
 * 
 * cdef inline int emoji_zjw(int n):             # <<<<<<<<<<<<<<
 *     if n == G_EXTENDED_PICTOGRAPHIC:
 *         return FSM_emoji
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":109
 *     return default(n)
 * 
 * cdef inline int ri(int n):             # <<<<<<<<<<<<<<
 *     if n == G_REGIONAL_INDICATOR:
 *         return FSM_default
 */

static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_ri(int __pyx_v_n) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  __Pyx_RefNannySetupContext("ri", 0);

  /* "grapheme/cython/finder.pyx":110
 * 
 * cdef inline int ri(int n):
 *     if n == G_REGIONAL_INDICATOR:             # <<<<<<<<<<<<<<
 *         return FSM_default
 *     return default(n)
 */
  __pyx_t_1 = ((__pyx_v_n == __pyx_v_8grapheme_6cython_9constants_G_REGIONAL_INDICATOR) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":111
 * cdef inline int ri(int n):
 *     if n == G_REGIONAL_INDICATOR:
 *         return FSM_default             # <<<<<<<<<<<<<<
 *     return default(n)
 * 
 */
    __pyx_r = __pyx_v_8grapheme_6cython_9constants_FSM_default;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":110
 * 
 * cdef inline int ri(int n):
 *     if n == G_REGIONAL_INDICATOR:             # <<<<<<<<<<<<<<
 *         return FSM_default
 *     return default(n)
 */
  }

  /* "grapheme/cython/finder.pyx":112
 *     if n == G_REGIONAL_INDICATOR:
 *         return FSM_default
 *     return default(n)             # <<<<<<<<<<<<<<
 * 
 * cdef inline int test_codepoint(int state, int group):
 */
  __pyx_r = __pyx_f_8grapheme_6cython_6finder_default(__pyx_v_n);
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":109
 *     return default(n)
 * 
 * cdef inline int ri(int n):             # <<<<<<<<<<<<<<
 *     if n == G_REGIONAL_INDICATOR:
 *         return FSM_default
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":114
 *     return default(n)
 * 
 * cdef inline int test_codepoint(int state, int group):             # <<<<<<<<<<<<<<
 *     if state == FSM_default:
 *         return default(group)
 */

static CYTHON_INLINE int __pyx_f_8grapheme_6cython_6finder_test_codepoint(int __pyx_v_state, int __pyx_v_group) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  PyObject *__pyx_t_2 = NULL;
  __Pyx_RefNannySetupContext("test_codepoint", 0);

  /* "grapheme/cython/finder.pyx":115
 * 
 * cdef inline int test_codepoint(int state, int group):
 *     if state == FSM_default:             # <<<<<<<<<<<<<<
 *         return default(group)
 *     elif state == FSM_cr:
 */
  __pyx_t_1 = ((__pyx_v_state == __pyx_v_8grapheme_6cython_9constants_FSM_default) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":116
 * cdef inline int test_codepoint(int state, int group):
 *     if state == FSM_default:
 *         return default(group)             # <<<<<<<<<<<<<<
 *     elif state == FSM_cr:
 *         return cr(group)
 */
    __pyx_r = __pyx_f_8grapheme_6cython_6finder_default(__pyx_v_group);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":115
 * 
 * cdef inline int test_codepoint(int state, int group):
 *     if state == FSM_default:             # <<<<<<<<<<<<<<
 *         return default(group)
 *     elif state == FSM_cr:
 */
  }

  /* "grapheme/cython/finder.pyx":117
 *     if state == FSM_default:
 *         return default(group)
 *     elif state == FSM_cr:             # <<<<<<<<<<<<<<
 *         return cr(group)
 *     elif state == FSM_lf_or_control:
 */
  __pyx_t_1 = ((__pyx_v_state == __pyx_v_8grapheme_6cython_9constants_FSM_cr) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":118
 *         return default(group)
 *     elif state == FSM_cr:
 *         return cr(group)             # <<<<<<<<<<<<<<
 *     elif state == FSM_lf_or_control:
 *         return lf_or_control(group)
 */
    __pyx_r = __pyx_f_8grapheme_6cython_6finder_cr(__pyx_v_group);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":117
 *     if state == FSM_default:
 *         return default(group)
 *     elif state == FSM_cr:             # <<<<<<<<<<<<<<
 *         return cr(group)
 *     elif state == FSM_lf_or_control:
 */
  }

  /* "grapheme/cython/finder.pyx":119
 *     elif state == FSM_cr:
 *         return cr(group)
 *     elif state == FSM_lf_or_control:             # <<<<<<<<<<<<<<
 *         return lf_or_control(group)
 *     elif state == FSM_prepend:
 */
  __pyx_t_1 = ((__pyx_v_state == __pyx_v_8grapheme_6cython_9constants_FSM_lf_or_control) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":120
 *         return cr(group)
 *     elif state == FSM_lf_or_control:
 *         return lf_or_control(group)             # <<<<<<<<<<<<<<
 *     elif state == FSM_prepend:
 *         return prepend(group)
 */
    __pyx_r = __pyx_f_8grapheme_6cython_6finder_lf_or_control(__pyx_v_group);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":119
 *     elif state == FSM_cr:
 *         return cr(group)
 *     elif state == FSM_lf_or_control:             # <<<<<<<<<<<<<<
 *         return lf_or_control(group)
 *     elif state == FSM_prepend:
 */
  }

  /* "grapheme/cython/finder.pyx":121
 *     elif state == FSM_lf_or_control:
 *         return lf_or_control(group)
 *     elif state == FSM_prepend:             # <<<<<<<<<<<<<<
 *         return prepend(group)
 *     elif state == FSM_hangul_l:
 */
  __pyx_t_1 = ((__pyx_v_state == __pyx_v_8grapheme_6cython_9constants_FSM_prepend) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":122
 *         return lf_or_control(group)
 *     elif state == FSM_prepend:
 *         return prepend(group)             # <<<<<<<<<<<<<<
 *     elif state == FSM_hangul_l:
 *         return hangul_l(group)
 */
    __pyx_r = __pyx_f_8grapheme_6cython_6finder_prepend(__pyx_v_group);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":121
 *     elif state == FSM_lf_or_control:
 *         return lf_or_control(group)
 *     elif state == FSM_prepend:             # <<<<<<<<<<<<<<
 *         return prepend(group)
 *     elif state == FSM_hangul_l:
 */
  }

  /* "grapheme/cython/finder.pyx":123
 *     elif state == FSM_prepend:
 *         return prepend(group)
 *     elif state == FSM_hangul_l:             # <<<<<<<<<<<<<<
 *         return hangul_l(group)
 *     elif state == FSM_hangul_lv_or_v:
 */
  __pyx_t_1 = ((__pyx_v_state == __pyx_v_8grapheme_6cython_9constants_FSM_hangul_l) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":124
 *         return prepend(group)
 *     elif state == FSM_hangul_l:
 *         return hangul_l(group)             # <<<<<<<<<<<<<<
 *     elif state == FSM_hangul_lv_or_v:
 *         return hangul_lv_or_v(group)
 */
    __pyx_r = __pyx_f_8grapheme_6cython_6finder_hangul_l(__pyx_v_group);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":123
 *     elif state == FSM_prepend:
 *         return prepend(group)
 *     elif state == FSM_hangul_l:             # <<<<<<<<<<<<<<
 *         return hangul_l(group)
 *     elif state == FSM_hangul_lv_or_v:
 */
  }

  /* "grapheme/cython/finder.pyx":125
 *     elif state == FSM_hangul_l:
 *         return hangul_l(group)
 *     elif state == FSM_hangul_lv_or_v:             # <<<<<<<<<<<<<<
 *         return hangul_lv_or_v(group)
 *     elif state == FSM_hangul_lvt_or_t:
 */
  __pyx_t_1 = ((__pyx_v_state == __pyx_v_8grapheme_6cython_9constants_FSM_hangul_lv_or_v) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":126
 *         return hangul_l(group)
 *     elif state == FSM_hangul_lv_or_v:
 *         return hangul_lv_or_v(group)             # <<<<<<<<<<<<<<
 *     elif state == FSM_hangul_lvt_or_t:
 *         return hangul_lvt_or_t(group)
 */
    __pyx_r = __pyx_f_8grapheme_6cython_6finder_hangul_lv_or_v(__pyx_v_group);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":125
 *     elif state == FSM_hangul_l:
 *         return hangul_l(group)
 *     elif state == FSM_hangul_lv_or_v:             # <<<<<<<<<<<<<<
 *         return hangul_lv_or_v(group)
 *     elif state == FSM_hangul_lvt_or_t:
 */
  }

  /* "grapheme/cython/finder.pyx":127
 *     elif state == FSM_hangul_lv_or_v:
 *         return hangul_lv_or_v(group)
 *     elif state == FSM_hangul_lvt_or_t:             # <<<<<<<<<<<<<<
 *         return hangul_lvt_or_t(group)
 *     elif state == FSM_emoji:
 */
  __pyx_t_1 = ((__pyx_v_state == __pyx_v_8grapheme_6cython_9constants_FSM_hangul_lvt_or_t) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":128
 *         return hangul_lv_or_v(group)
 *     elif state == FSM_hangul_lvt_or_t:
 *         return hangul_lvt_or_t(group)             # <<<<<<<<<<<<<<
 *     elif state == FSM_emoji:
 *         return emoji(group)
 */
    __pyx_t_2 = __Pyx_PyInt_From_int(__pyx_v_group); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 128, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_r = __pyx_f_8grapheme_6cython_6finder_hangul_lvt_or_t(__pyx_t_2);
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":127
 *     elif state == FSM_hangul_lv_or_v:
 *         return hangul_lv_or_v(group)
 *     elif state == FSM_hangul_lvt_or_t:             # <<<<<<<<<<<<<<
 *         return hangul_lvt_or_t(group)
 *     elif state == FSM_emoji:
 */
  }

  /* "grapheme/cython/finder.pyx":129
 *     elif state == FSM_hangul_lvt_or_t:
 *         return hangul_lvt_or_t(group)
 *     elif state == FSM_emoji:             # <<<<<<<<<<<<<<
 *         return emoji(group)
 *     elif state == FSM_emoji_zjw:
 */
  __pyx_t_1 = ((__pyx_v_state == __pyx_v_8grapheme_6cython_9constants_FSM_emoji) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":130
 *         return hangul_lvt_or_t(group)
 *     elif state == FSM_emoji:
 *         return emoji(group)             # <<<<<<<<<<<<<<
 *     elif state == FSM_emoji_zjw:
 *         return emoji_zjw(group)
 */
    __pyx_r = __pyx_f_8grapheme_6cython_6finder_emoji(__pyx_v_group);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":129
 *     elif state == FSM_hangul_lvt_or_t:
 *         return hangul_lvt_or_t(group)
 *     elif state == FSM_emoji:             # <<<<<<<<<<<<<<
 *         return emoji(group)
 *     elif state == FSM_emoji_zjw:
 */
  }

  /* "grapheme/cython/finder.pyx":131
 *     elif state == FSM_emoji:
 *         return emoji(group)
 *     elif state == FSM_emoji_zjw:             # <<<<<<<<<<<<<<
 *         return emoji_zjw(group)
 *     elif state == FSM_ri:
 */
  __pyx_t_1 = ((__pyx_v_state == __pyx_v_8grapheme_6cython_9constants_FSM_emoji_zjw) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":132
 *         return emoji(group)
 *     elif state == FSM_emoji_zjw:
 *         return emoji_zjw(group)             # <<<<<<<<<<<<<<
 *     elif state == FSM_ri:
 *         return ri(group)
 */
    __pyx_r = __pyx_f_8grapheme_6cython_6finder_emoji_zjw(__pyx_v_group);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":131
 *     elif state == FSM_emoji:
 *         return emoji(group)
 *     elif state == FSM_emoji_zjw:             # <<<<<<<<<<<<<<
 *         return emoji_zjw(group)
 *     elif state == FSM_ri:
 */
  }

  /* "grapheme/cython/finder.pyx":133
 *     elif state == FSM_emoji_zjw:
 *         return emoji_zjw(group)
 *     elif state == FSM_ri:             # <<<<<<<<<<<<<<
 *         return ri(group)
 * 
 */
  __pyx_t_1 = ((__pyx_v_state == __pyx_v_8grapheme_6cython_9constants_FSM_ri) != 0);
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":134
 *         return emoji_zjw(group)
 *     elif state == FSM_ri:
 *         return ri(group)             # <<<<<<<<<<<<<<
 * 
 * cdef class FSM:
 */
    __pyx_r = __pyx_f_8grapheme_6cython_6finder_ri(__pyx_v_group);
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":133
 *     elif state == FSM_emoji_zjw:
 *         return emoji_zjw(group)
 *     elif state == FSM_ri:             # <<<<<<<<<<<<<<
 *         return ri(group)
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":114
 *     return default(n)
 * 
 * cdef inline int test_codepoint(int state, int group):             # <<<<<<<<<<<<<<
 *     if state == FSM_default:
 *         return default(group)
 */

  /* function exit code */
  __pyx_r = 0;
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_WriteUnraisable("grapheme.cython.finder.test_codepoint", __pyx_clineno, __pyx_lineno, __pyx_filename, 1, 0);
  __pyx_r = 0;
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":138
 * cdef class FSM:
 *     @classmethod
 *     def default(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.OTHER:
 *             return True, cls.default
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_1default(PyObject *__pyx_v_cls, PyObject *__pyx_v_n); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_1default(PyObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("default (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_3FSM_default(((PyTypeObject*)__pyx_v_cls), ((PyObject *)__pyx_v_n));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_default(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  int __pyx_t_3;
  PyObject *__pyx_t_4 = NULL;
  int __pyx_t_5;
  __Pyx_RefNannySetupContext("default", 0);

  /* "grapheme/cython/finder.pyx":139
 *     @classmethod
 *     def default(cls, n):
 *         if n == G.OTHER:             # <<<<<<<<<<<<<<
 *             return True, cls.default
 * 
 */
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 139, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_OTHER); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 139, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_v_n, __pyx_t_2, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 139, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 139, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":140
 *     def default(cls, n):
 *         if n == G.OTHER:
 *             return True, cls.default             # <<<<<<<<<<<<<<
 * 
 *         if n == G.CR:
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 140, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_2 = PyTuple_New(2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 140, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_INCREF(Py_True);
    __Pyx_GIVEREF(Py_True);
    PyTuple_SET_ITEM(__pyx_t_2, 0, Py_True);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_2, 1, __pyx_t_1);
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_2;
    __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":139
 *     @classmethod
 *     def default(cls, n):
 *         if n == G.OTHER:             # <<<<<<<<<<<<<<
 *             return True, cls.default
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":142
 *             return True, cls.default
 * 
 *         if n == G.CR:             # <<<<<<<<<<<<<<
 *             return True, cls.cr
 * 
 */
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 142, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_CR); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 142, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_v_n, __pyx_t_1, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 142, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 142, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":143
 * 
 *         if n == G.CR:
 *             return True, cls.cr             # <<<<<<<<<<<<<<
 * 
 *         if n in [G.LF, G.CONTROL]:
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_2 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_cr); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 143, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_1 = PyTuple_New(2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 143, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __Pyx_INCREF(Py_True);
    __Pyx_GIVEREF(Py_True);
    PyTuple_SET_ITEM(__pyx_t_1, 0, Py_True);
    __Pyx_GIVEREF(__pyx_t_2);
    PyTuple_SET_ITEM(__pyx_t_1, 1, __pyx_t_2);
    __pyx_t_2 = 0;
    __pyx_r = __pyx_t_1;
    __pyx_t_1 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":142
 *             return True, cls.default
 * 
 *         if n == G.CR:             # <<<<<<<<<<<<<<
 *             return True, cls.cr
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":145
 *             return True, cls.cr
 * 
 *         if n in [G.LF, G.CONTROL]:             # <<<<<<<<<<<<<<
 *             return True, cls.lf_or_control
 * 
 */
  __Pyx_INCREF(__pyx_v_n);
  __pyx_t_1 = __pyx_v_n;
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 145, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_LF); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 145, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_t_1, __pyx_t_4, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 145, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_5 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_5 < 0)) __PYX_ERR(0, 145, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (!__pyx_t_5) {
  } else {
    __pyx_t_3 = __pyx_t_5;
    goto __pyx_L6_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 145, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_CONTROL); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 145, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_t_1, __pyx_t_4, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 145, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_5 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_5 < 0)) __PYX_ERR(0, 145, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_3 = __pyx_t_5;
  __pyx_L6_bool_binop_done:;
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_5 = (__pyx_t_3 != 0);
  if (__pyx_t_5) {

    /* "grapheme/cython/finder.pyx":146
 * 
 *         if n in [G.LF, G.CONTROL]:
 *             return True, cls.lf_or_control             # <<<<<<<<<<<<<<
 * 
 *         if n in [G.EXTEND, G.SPACING_MARK, G.ZWJ]:
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_lf_or_control); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 146, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_2 = PyTuple_New(2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 146, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_INCREF(Py_True);
    __Pyx_GIVEREF(Py_True);
    PyTuple_SET_ITEM(__pyx_t_2, 0, Py_True);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_2, 1, __pyx_t_1);
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_2;
    __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":145
 *             return True, cls.cr
 * 
 *         if n in [G.LF, G.CONTROL]:             # <<<<<<<<<<<<<<
 *             return True, cls.lf_or_control
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":148
 *             return True, cls.lf_or_control
 * 
 *         if n in [G.EXTEND, G.SPACING_MARK, G.ZWJ]:             # <<<<<<<<<<<<<<
 *             return False, cls.default
 * 
 */
  __Pyx_INCREF(__pyx_v_n);
  __pyx_t_2 = __pyx_v_n;
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 148, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_EXTEND); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 148, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_t_2, __pyx_t_4, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 148, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 148, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (!__pyx_t_3) {
  } else {
    __pyx_t_5 = __pyx_t_3;
    goto __pyx_L9_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 148, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_SPACING_MARK); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 148, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_t_2, __pyx_t_4, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 148, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 148, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (!__pyx_t_3) {
  } else {
    __pyx_t_5 = __pyx_t_3;
    goto __pyx_L9_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 148, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_ZWJ); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 148, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_t_2, __pyx_t_4, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 148, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 148, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_5 = __pyx_t_3;
  __pyx_L9_bool_binop_done:;
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_3 = (__pyx_t_5 != 0);
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":149
 * 
 *         if n in [G.EXTEND, G.SPACING_MARK, G.ZWJ]:
 *             return False, cls.default             # <<<<<<<<<<<<<<
 * 
 *         if n == G.EXTENDED_PICTOGRAPHIC:
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_2 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 149, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_1 = PyTuple_New(2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 149, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __Pyx_INCREF(Py_False);
    __Pyx_GIVEREF(Py_False);
    PyTuple_SET_ITEM(__pyx_t_1, 0, Py_False);
    __Pyx_GIVEREF(__pyx_t_2);
    PyTuple_SET_ITEM(__pyx_t_1, 1, __pyx_t_2);
    __pyx_t_2 = 0;
    __pyx_r = __pyx_t_1;
    __pyx_t_1 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":148
 *             return True, cls.lf_or_control
 * 
 *         if n in [G.EXTEND, G.SPACING_MARK, G.ZWJ]:             # <<<<<<<<<<<<<<
 *             return False, cls.default
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":151
 *             return False, cls.default
 * 
 *         if n == G.EXTENDED_PICTOGRAPHIC:             # <<<<<<<<<<<<<<
 *             return True, cls.emoji
 * 
 */
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 151, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_EXTENDED_PICTOGRAPHIC); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 151, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_v_n, __pyx_t_2, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 151, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 151, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":152
 * 
 *         if n == G.EXTENDED_PICTOGRAPHIC:
 *             return True, cls.emoji             # <<<<<<<<<<<<<<
 * 
 *         if n == G.REGIONAL_INDICATOR:
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_emoji); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 152, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_2 = PyTuple_New(2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 152, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_INCREF(Py_True);
    __Pyx_GIVEREF(Py_True);
    PyTuple_SET_ITEM(__pyx_t_2, 0, Py_True);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_2, 1, __pyx_t_1);
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_2;
    __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":151
 *             return False, cls.default
 * 
 *         if n == G.EXTENDED_PICTOGRAPHIC:             # <<<<<<<<<<<<<<
 *             return True, cls.emoji
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":154
 *             return True, cls.emoji
 * 
 *         if n == G.REGIONAL_INDICATOR:             # <<<<<<<<<<<<<<
 *             return True, cls.ri
 * 
 */
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 154, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_REGIONAL_INDICATOR); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 154, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_v_n, __pyx_t_1, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 154, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 154, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":155
 * 
 *         if n == G.REGIONAL_INDICATOR:
 *             return True, cls.ri             # <<<<<<<<<<<<<<
 * 
 *         if n == G.L:
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_2 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_ri); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 155, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_1 = PyTuple_New(2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 155, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __Pyx_INCREF(Py_True);
    __Pyx_GIVEREF(Py_True);
    PyTuple_SET_ITEM(__pyx_t_1, 0, Py_True);
    __Pyx_GIVEREF(__pyx_t_2);
    PyTuple_SET_ITEM(__pyx_t_1, 1, __pyx_t_2);
    __pyx_t_2 = 0;
    __pyx_r = __pyx_t_1;
    __pyx_t_1 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":154
 *             return True, cls.emoji
 * 
 *         if n == G.REGIONAL_INDICATOR:             # <<<<<<<<<<<<<<
 *             return True, cls.ri
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":157
 *             return True, cls.ri
 * 
 *         if n == G.L:             # <<<<<<<<<<<<<<
 *             return True, cls.hangul_l
 * 
 */
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 157, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_L); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 157, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_v_n, __pyx_t_2, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 157, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 157, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":158
 * 
 *         if n == G.L:
 *             return True, cls.hangul_l             # <<<<<<<<<<<<<<
 * 
 *         if n in [G.LV, G.V]:
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_hangul_l); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 158, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_2 = PyTuple_New(2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 158, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_INCREF(Py_True);
    __Pyx_GIVEREF(Py_True);
    PyTuple_SET_ITEM(__pyx_t_2, 0, Py_True);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_2, 1, __pyx_t_1);
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_2;
    __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":157
 *             return True, cls.ri
 * 
 *         if n == G.L:             # <<<<<<<<<<<<<<
 *             return True, cls.hangul_l
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":160
 *             return True, cls.hangul_l
 * 
 *         if n in [G.LV, G.V]:             # <<<<<<<<<<<<<<
 *             return True, cls.hangul_lv_or_v
 * 
 */
  __Pyx_INCREF(__pyx_v_n);
  __pyx_t_2 = __pyx_v_n;
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 160, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_LV); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 160, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_t_2, __pyx_t_4, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 160, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_5 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_5 < 0)) __PYX_ERR(0, 160, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (!__pyx_t_5) {
  } else {
    __pyx_t_3 = __pyx_t_5;
    goto __pyx_L16_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 160, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_V); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 160, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_t_2, __pyx_t_4, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 160, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_5 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_5 < 0)) __PYX_ERR(0, 160, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_3 = __pyx_t_5;
  __pyx_L16_bool_binop_done:;
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_5 = (__pyx_t_3 != 0);
  if (__pyx_t_5) {

    /* "grapheme/cython/finder.pyx":161
 * 
 *         if n in [G.LV, G.V]:
 *             return True, cls.hangul_lv_or_v             # <<<<<<<<<<<<<<
 * 
 *         if n in [G.LVT, G.T]:
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_2 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_hangul_lv_or_v); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 161, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_1 = PyTuple_New(2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 161, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __Pyx_INCREF(Py_True);
    __Pyx_GIVEREF(Py_True);
    PyTuple_SET_ITEM(__pyx_t_1, 0, Py_True);
    __Pyx_GIVEREF(__pyx_t_2);
    PyTuple_SET_ITEM(__pyx_t_1, 1, __pyx_t_2);
    __pyx_t_2 = 0;
    __pyx_r = __pyx_t_1;
    __pyx_t_1 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":160
 *             return True, cls.hangul_l
 * 
 *         if n in [G.LV, G.V]:             # <<<<<<<<<<<<<<
 *             return True, cls.hangul_lv_or_v
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":163
 *             return True, cls.hangul_lv_or_v
 * 
 *         if n in [G.LVT, G.T]:             # <<<<<<<<<<<<<<
 *             return True, cls.hangul_lvt_or_t
 * 
 */
  __Pyx_INCREF(__pyx_v_n);
  __pyx_t_1 = __pyx_v_n;
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 163, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_LVT); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 163, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_t_1, __pyx_t_4, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 163, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 163, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (!__pyx_t_3) {
  } else {
    __pyx_t_5 = __pyx_t_3;
    goto __pyx_L19_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 163, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_T); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 163, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_t_1, __pyx_t_4, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 163, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 163, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_5 = __pyx_t_3;
  __pyx_L19_bool_binop_done:;
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_3 = (__pyx_t_5 != 0);
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":164
 * 
 *         if n in [G.LVT, G.T]:
 *             return True, cls.hangul_lvt_or_t             # <<<<<<<<<<<<<<
 * 
 *         if n == G.PREPEND:
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_hangul_lvt_or_t); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 164, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_2 = PyTuple_New(2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 164, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_INCREF(Py_True);
    __Pyx_GIVEREF(Py_True);
    PyTuple_SET_ITEM(__pyx_t_2, 0, Py_True);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_2, 1, __pyx_t_1);
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_2;
    __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":163
 *             return True, cls.hangul_lv_or_v
 * 
 *         if n in [G.LVT, G.T]:             # <<<<<<<<<<<<<<
 *             return True, cls.hangul_lvt_or_t
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":166
 *             return True, cls.hangul_lvt_or_t
 * 
 *         if n == G.PREPEND:             # <<<<<<<<<<<<<<
 *             return True, cls.prepend
 * 
 */
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 166, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_PREPEND); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 166, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_v_n, __pyx_t_1, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 166, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 166, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":167
 * 
 *         if n == G.PREPEND:
 *             return True, cls.prepend             # <<<<<<<<<<<<<<
 * 
 *         return True, cls.default
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_2 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_prepend); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 167, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_1 = PyTuple_New(2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 167, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __Pyx_INCREF(Py_True);
    __Pyx_GIVEREF(Py_True);
    PyTuple_SET_ITEM(__pyx_t_1, 0, Py_True);
    __Pyx_GIVEREF(__pyx_t_2);
    PyTuple_SET_ITEM(__pyx_t_1, 1, __pyx_t_2);
    __pyx_t_2 = 0;
    __pyx_r = __pyx_t_1;
    __pyx_t_1 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":166
 *             return True, cls.hangul_lvt_or_t
 * 
 *         if n == G.PREPEND:             # <<<<<<<<<<<<<<
 *             return True, cls.prepend
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":169
 *             return True, cls.prepend
 * 
 *         return True, cls.default             # <<<<<<<<<<<<<<
 * 
 *     @classmethod
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 169, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = PyTuple_New(2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 169, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_INCREF(Py_True);
  __Pyx_GIVEREF(Py_True);
  PyTuple_SET_ITEM(__pyx_t_2, 0, Py_True);
  __Pyx_GIVEREF(__pyx_t_1);
  PyTuple_SET_ITEM(__pyx_t_2, 1, __pyx_t_1);
  __pyx_t_1 = 0;
  __pyx_r = __pyx_t_2;
  __pyx_t_2 = 0;
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":138
 * cdef class FSM:
 *     @classmethod
 *     def default(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.OTHER:
 *             return True, cls.default
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_AddTraceback("grapheme.cython.finder.FSM.default", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":172
 * 
 *     @classmethod
 *     def default_next_state(cls, n, should_break):             # <<<<<<<<<<<<<<
 *         _, next_state = cls.default(n)
 *         return should_break, next_state
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_3default_next_state(PyObject *__pyx_v_cls, PyObject *__pyx_args, PyObject *__pyx_kwds); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_3default_next_state(PyObject *__pyx_v_cls, PyObject *__pyx_args, PyObject *__pyx_kwds) {
  PyObject *__pyx_v_n = 0;
  PyObject *__pyx_v_should_break = 0;
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("default_next_state (wrapper)", 0);
  {
    static PyObject **__pyx_pyargnames[] = {&__pyx_n_s_n,&__pyx_n_s_should_break,0};
    PyObject* values[2] = {0,0};
    if (unlikely(__pyx_kwds)) {
      Py_ssize_t kw_args;
      const Py_ssize_t pos_args = PyTuple_GET_SIZE(__pyx_args);
      switch (pos_args) {
        case  2: values[1] = PyTuple_GET_ITEM(__pyx_args, 1);
        CYTHON_FALLTHROUGH;
        case  1: values[0] = PyTuple_GET_ITEM(__pyx_args, 0);
        CYTHON_FALLTHROUGH;
        case  0: break;
        default: goto __pyx_L5_argtuple_error;
      }
      kw_args = PyDict_Size(__pyx_kwds);
      switch (pos_args) {
        case  0:
        if (likely((values[0] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_n)) != 0)) kw_args--;
        else goto __pyx_L5_argtuple_error;
        CYTHON_FALLTHROUGH;
        case  1:
        if (likely((values[1] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_should_break)) != 0)) kw_args--;
        else {
          __Pyx_RaiseArgtupleInvalid("default_next_state", 1, 2, 2, 1); __PYX_ERR(0, 172, __pyx_L3_error)
        }
      }
      if (unlikely(kw_args > 0)) {
        if (unlikely(__Pyx_ParseOptionalKeywords(__pyx_kwds, __pyx_pyargnames, 0, values, pos_args, "default_next_state") < 0)) __PYX_ERR(0, 172, __pyx_L3_error)
      }
    } else if (PyTuple_GET_SIZE(__pyx_args) != 2) {
      goto __pyx_L5_argtuple_error;
    } else {
      values[0] = PyTuple_GET_ITEM(__pyx_args, 0);
      values[1] = PyTuple_GET_ITEM(__pyx_args, 1);
    }
    __pyx_v_n = values[0];
    __pyx_v_should_break = values[1];
  }
  goto __pyx_L4_argument_unpacking_done;
  __pyx_L5_argtuple_error:;
  __Pyx_RaiseArgtupleInvalid("default_next_state", 1, 2, 2, PyTuple_GET_SIZE(__pyx_args)); __PYX_ERR(0, 172, __pyx_L3_error)
  __pyx_L3_error:;
  __Pyx_AddTraceback("grapheme.cython.finder.FSM.default_next_state", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __Pyx_RefNannyFinishContext();
  return NULL;
  __pyx_L4_argument_unpacking_done:;
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_3FSM_2default_next_state(((PyTypeObject*)__pyx_v_cls), __pyx_v_n, __pyx_v_should_break);

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_2default_next_state(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n, PyObject *__pyx_v_should_break) {
  CYTHON_UNUSED PyObject *__pyx_v__ = NULL;
  PyObject *__pyx_v_next_state = NULL;
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  PyObject *__pyx_t_3 = NULL;
  PyObject *__pyx_t_4 = NULL;
  PyObject *(*__pyx_t_5)(PyObject *);
  __Pyx_RefNannySetupContext("default_next_state", 0);

  /* "grapheme/cython/finder.pyx":173
 *     @classmethod
 *     def default_next_state(cls, n, should_break):
 *         _, next_state = cls.default(n)             # <<<<<<<<<<<<<<
 *         return should_break, next_state
 * 
 */
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 173, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_3 = NULL;
  if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_2))) {
    __pyx_t_3 = PyMethod_GET_SELF(__pyx_t_2);
    if (likely(__pyx_t_3)) {
      PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_2);
      __Pyx_INCREF(__pyx_t_3);
      __Pyx_INCREF(function);
      __Pyx_DECREF_SET(__pyx_t_2, function);
    }
  }
  __pyx_t_1 = (__pyx_t_3) ? __Pyx_PyObject_Call2Args(__pyx_t_2, __pyx_t_3, __pyx_v_n) : __Pyx_PyObject_CallOneArg(__pyx_t_2, __pyx_v_n);
  __Pyx_XDECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 173, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if ((likely(PyTuple_CheckExact(__pyx_t_1))) || (PyList_CheckExact(__pyx_t_1))) {
    PyObject* sequence = __pyx_t_1;
    Py_ssize_t size = __Pyx_PySequence_SIZE(sequence);
    if (unlikely(size != 2)) {
      if (size > 2) __Pyx_RaiseTooManyValuesError(2);
      else if (size >= 0) __Pyx_RaiseNeedMoreValuesError(size);
      __PYX_ERR(0, 173, __pyx_L1_error)
    }
    #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
    if (likely(PyTuple_CheckExact(sequence))) {
      __pyx_t_2 = PyTuple_GET_ITEM(sequence, 0); 
      __pyx_t_3 = PyTuple_GET_ITEM(sequence, 1); 
    } else {
      __pyx_t_2 = PyList_GET_ITEM(sequence, 0); 
      __pyx_t_3 = PyList_GET_ITEM(sequence, 1); 
    }
    __Pyx_INCREF(__pyx_t_2);
    __Pyx_INCREF(__pyx_t_3);
    #else
    __pyx_t_2 = PySequence_ITEM(sequence, 0); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 173, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_3 = PySequence_ITEM(sequence, 1); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 173, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    #endif
    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  } else {
    Py_ssize_t index = -1;
    __pyx_t_4 = PyObject_GetIter(__pyx_t_1); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 173, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_4);
    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
    __pyx_t_5 = Py_TYPE(__pyx_t_4)->tp_iternext;
    index = 0; __pyx_t_2 = __pyx_t_5(__pyx_t_4); if (unlikely(!__pyx_t_2)) goto __pyx_L3_unpacking_failed;
    __Pyx_GOTREF(__pyx_t_2);
    index = 1; __pyx_t_3 = __pyx_t_5(__pyx_t_4); if (unlikely(!__pyx_t_3)) goto __pyx_L3_unpacking_failed;
    __Pyx_GOTREF(__pyx_t_3);
    if (__Pyx_IternextUnpackEndCheck(__pyx_t_5(__pyx_t_4), 2) < 0) __PYX_ERR(0, 173, __pyx_L1_error)
    __pyx_t_5 = NULL;
    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
    goto __pyx_L4_unpacking_done;
    __pyx_L3_unpacking_failed:;
    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
    __pyx_t_5 = NULL;
    if (__Pyx_IterFinish() == 0) __Pyx_RaiseNeedMoreValuesError(index);
    __PYX_ERR(0, 173, __pyx_L1_error)
    __pyx_L4_unpacking_done:;
  }
  __pyx_v__ = __pyx_t_2;
  __pyx_t_2 = 0;
  __pyx_v_next_state = __pyx_t_3;
  __pyx_t_3 = 0;

  /* "grapheme/cython/finder.pyx":174
 *     def default_next_state(cls, n, should_break):
 *         _, next_state = cls.default(n)
 *         return should_break, next_state             # <<<<<<<<<<<<<<
 * 
 *     @classmethod
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = PyTuple_New(2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 174, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_INCREF(__pyx_v_should_break);
  __Pyx_GIVEREF(__pyx_v_should_break);
  PyTuple_SET_ITEM(__pyx_t_1, 0, __pyx_v_should_break);
  __Pyx_INCREF(__pyx_v_next_state);
  __Pyx_GIVEREF(__pyx_v_next_state);
  PyTuple_SET_ITEM(__pyx_t_1, 1, __pyx_v_next_state);
  __pyx_r = __pyx_t_1;
  __pyx_t_1 = 0;
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":172
 * 
 *     @classmethod
 *     def default_next_state(cls, n, should_break):             # <<<<<<<<<<<<<<
 *         _, next_state = cls.default(n)
 *         return should_break, next_state
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_3);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_AddTraceback("grapheme.cython.finder.FSM.default_next_state", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XDECREF(__pyx_v__);
  __Pyx_XDECREF(__pyx_v_next_state);
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":177
 * 
 *     @classmethod
 *     def cr(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.LF:
 *             return False, cls.lf_or_control
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_5cr(PyObject *__pyx_v_cls, PyObject *__pyx_v_n); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_5cr(PyObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("cr (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_3FSM_4cr(((PyTypeObject*)__pyx_v_cls), ((PyObject *)__pyx_v_n));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_4cr(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  int __pyx_t_3;
  PyObject *__pyx_t_4 = NULL;
  PyObject *__pyx_t_5 = NULL;
  __Pyx_RefNannySetupContext("cr", 0);

  /* "grapheme/cython/finder.pyx":178
 *     @classmethod
 *     def cr(cls, n):
 *         if n == G.LF:             # <<<<<<<<<<<<<<
 *             return False, cls.lf_or_control
 *         return cls.default_next_state(n, should_break=True)
 */
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 178, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_LF); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 178, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_v_n, __pyx_t_2, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 178, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 178, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":179
 *     def cr(cls, n):
 *         if n == G.LF:
 *             return False, cls.lf_or_control             # <<<<<<<<<<<<<<
 *         return cls.default_next_state(n, should_break=True)
 * 
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_lf_or_control); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 179, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_2 = PyTuple_New(2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 179, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_INCREF(Py_False);
    __Pyx_GIVEREF(Py_False);
    PyTuple_SET_ITEM(__pyx_t_2, 0, Py_False);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_2, 1, __pyx_t_1);
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_2;
    __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":178
 *     @classmethod
 *     def cr(cls, n):
 *         if n == G.LF:             # <<<<<<<<<<<<<<
 *             return False, cls.lf_or_control
 *         return cls.default_next_state(n, should_break=True)
 */
  }

  /* "grapheme/cython/finder.pyx":180
 *         if n == G.LF:
 *             return False, cls.lf_or_control
 *         return cls.default_next_state(n, should_break=True)             # <<<<<<<<<<<<<<
 * 
 *     @classmethod
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default_next_state); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 180, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_1 = PyTuple_New(1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 180, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_INCREF(__pyx_v_n);
  __Pyx_GIVEREF(__pyx_v_n);
  PyTuple_SET_ITEM(__pyx_t_1, 0, __pyx_v_n);
  __pyx_t_4 = __Pyx_PyDict_NewPresized(1); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 180, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  if (PyDict_SetItem(__pyx_t_4, __pyx_n_s_should_break, Py_True) < 0) __PYX_ERR(0, 180, __pyx_L1_error)
  __pyx_t_5 = __Pyx_PyObject_Call(__pyx_t_2, __pyx_t_1, __pyx_t_4); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 180, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_r = __pyx_t_5;
  __pyx_t_5 = 0;
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":177
 * 
 *     @classmethod
 *     def cr(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.LF:
 *             return False, cls.lf_or_control
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_XDECREF(__pyx_t_5);
  __Pyx_AddTraceback("grapheme.cython.finder.FSM.cr", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":183
 * 
 *     @classmethod
 *     def lf_or_control(cls, n):             # <<<<<<<<<<<<<<
 *         return cls.default_next_state(n, should_break=True)
 * 
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_7lf_or_control(PyObject *__pyx_v_cls, PyObject *__pyx_v_n); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_7lf_or_control(PyObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("lf_or_control (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_3FSM_6lf_or_control(((PyTypeObject*)__pyx_v_cls), ((PyObject *)__pyx_v_n));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_6lf_or_control(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  PyObject *__pyx_t_3 = NULL;
  PyObject *__pyx_t_4 = NULL;
  __Pyx_RefNannySetupContext("lf_or_control", 0);

  /* "grapheme/cython/finder.pyx":184
 *     @classmethod
 *     def lf_or_control(cls, n):
 *         return cls.default_next_state(n, should_break=True)             # <<<<<<<<<<<<<<
 * 
 *     @classmethod
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default_next_state); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 184, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = PyTuple_New(1); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 184, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_INCREF(__pyx_v_n);
  __Pyx_GIVEREF(__pyx_v_n);
  PyTuple_SET_ITEM(__pyx_t_2, 0, __pyx_v_n);
  __pyx_t_3 = __Pyx_PyDict_NewPresized(1); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 184, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  if (PyDict_SetItem(__pyx_t_3, __pyx_n_s_should_break, Py_True) < 0) __PYX_ERR(0, 184, __pyx_L1_error)
  __pyx_t_4 = __Pyx_PyObject_Call(__pyx_t_1, __pyx_t_2, __pyx_t_3); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 184, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_r = __pyx_t_4;
  __pyx_t_4 = 0;
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":183
 * 
 *     @classmethod
 *     def lf_or_control(cls, n):             # <<<<<<<<<<<<<<
 *         return cls.default_next_state(n, should_break=True)
 * 
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_3);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_AddTraceback("grapheme.cython.finder.FSM.lf_or_control", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":187
 * 
 *     @classmethod
 *     def prepend(cls, n):             # <<<<<<<<<<<<<<
 *         if n in [G.CONTROL, G.LF]:
 *             return True, cls.default
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_9prepend(PyObject *__pyx_v_cls, PyObject *__pyx_v_n); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_9prepend(PyObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("prepend (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_3FSM_8prepend(((PyTypeObject*)__pyx_v_cls), ((PyObject *)__pyx_v_n));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_8prepend(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_t_2;
  PyObject *__pyx_t_3 = NULL;
  PyObject *__pyx_t_4 = NULL;
  int __pyx_t_5;
  PyObject *__pyx_t_6 = NULL;
  __Pyx_RefNannySetupContext("prepend", 0);

  /* "grapheme/cython/finder.pyx":188
 *     @classmethod
 *     def prepend(cls, n):
 *         if n in [G.CONTROL, G.LF]:             # <<<<<<<<<<<<<<
 *             return True, cls.default
 *         if n == G.CR:
 */
  __Pyx_INCREF(__pyx_v_n);
  __pyx_t_1 = __pyx_v_n;
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 188, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_CONTROL); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 188, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_1, __pyx_t_4, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 188, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_5 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_5 < 0)) __PYX_ERR(0, 188, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (!__pyx_t_5) {
  } else {
    __pyx_t_2 = __pyx_t_5;
    goto __pyx_L4_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 188, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_LF); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 188, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_1, __pyx_t_4, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 188, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_5 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_5 < 0)) __PYX_ERR(0, 188, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_2 = __pyx_t_5;
  __pyx_L4_bool_binop_done:;
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_5 = (__pyx_t_2 != 0);
  if (__pyx_t_5) {

    /* "grapheme/cython/finder.pyx":189
 *     def prepend(cls, n):
 *         if n in [G.CONTROL, G.LF]:
 *             return True, cls.default             # <<<<<<<<<<<<<<
 *         if n == G.CR:
 *             return True, cls.cr
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 189, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_3 = PyTuple_New(2); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 189, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __Pyx_INCREF(Py_True);
    __Pyx_GIVEREF(Py_True);
    PyTuple_SET_ITEM(__pyx_t_3, 0, Py_True);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_3, 1, __pyx_t_1);
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_3;
    __pyx_t_3 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":188
 *     @classmethod
 *     def prepend(cls, n):
 *         if n in [G.CONTROL, G.LF]:             # <<<<<<<<<<<<<<
 *             return True, cls.default
 *         if n == G.CR:
 */
  }

  /* "grapheme/cython/finder.pyx":190
 *         if n in [G.CONTROL, G.LF]:
 *             return True, cls.default
 *         if n == G.CR:             # <<<<<<<<<<<<<<
 *             return True, cls.cr
 *         return cls.default_next_state(n, should_break=False)
 */
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 190, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_CR); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 190, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_v_n, __pyx_t_1, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 190, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_5 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_5 < 0)) __PYX_ERR(0, 190, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (__pyx_t_5) {

    /* "grapheme/cython/finder.pyx":191
 *             return True, cls.default
 *         if n == G.CR:
 *             return True, cls.cr             # <<<<<<<<<<<<<<
 *         return cls.default_next_state(n, should_break=False)
 * 
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_3 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_cr); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 191, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __pyx_t_1 = PyTuple_New(2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 191, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __Pyx_INCREF(Py_True);
    __Pyx_GIVEREF(Py_True);
    PyTuple_SET_ITEM(__pyx_t_1, 0, Py_True);
    __Pyx_GIVEREF(__pyx_t_3);
    PyTuple_SET_ITEM(__pyx_t_1, 1, __pyx_t_3);
    __pyx_t_3 = 0;
    __pyx_r = __pyx_t_1;
    __pyx_t_1 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":190
 *         if n in [G.CONTROL, G.LF]:
 *             return True, cls.default
 *         if n == G.CR:             # <<<<<<<<<<<<<<
 *             return True, cls.cr
 *         return cls.default_next_state(n, should_break=False)
 */
  }

  /* "grapheme/cython/finder.pyx":192
 *         if n == G.CR:
 *             return True, cls.cr
 *         return cls.default_next_state(n, should_break=False)             # <<<<<<<<<<<<<<
 * 
 *     # Hanguls
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default_next_state); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 192, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_3 = PyTuple_New(1); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 192, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_INCREF(__pyx_v_n);
  __Pyx_GIVEREF(__pyx_v_n);
  PyTuple_SET_ITEM(__pyx_t_3, 0, __pyx_v_n);
  __pyx_t_4 = __Pyx_PyDict_NewPresized(1); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 192, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  if (PyDict_SetItem(__pyx_t_4, __pyx_n_s_should_break, Py_False) < 0) __PYX_ERR(0, 192, __pyx_L1_error)
  __pyx_t_6 = __Pyx_PyObject_Call(__pyx_t_1, __pyx_t_3, __pyx_t_4); if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 192, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_6);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_r = __pyx_t_6;
  __pyx_t_6 = 0;
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":187
 * 
 *     @classmethod
 *     def prepend(cls, n):             # <<<<<<<<<<<<<<
 *         if n in [G.CONTROL, G.LF]:
 *             return True, cls.default
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_3);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_XDECREF(__pyx_t_6);
  __Pyx_AddTraceback("grapheme.cython.finder.FSM.prepend", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":196
 *     # Hanguls
 *     @classmethod
 *     def hangul_l(cls, n):             # <<<<<<<<<<<<<<
 *         if n in [G.V, G.LV]:
 *             return False, cls.hangul_lv_or_v
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_11hangul_l(PyObject *__pyx_v_cls, PyObject *__pyx_v_n); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_11hangul_l(PyObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("hangul_l (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_3FSM_10hangul_l(((PyTypeObject*)__pyx_v_cls), ((PyObject *)__pyx_v_n));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_10hangul_l(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_t_2;
  PyObject *__pyx_t_3 = NULL;
  PyObject *__pyx_t_4 = NULL;
  int __pyx_t_5;
  __Pyx_RefNannySetupContext("hangul_l", 0);

  /* "grapheme/cython/finder.pyx":197
 *     @classmethod
 *     def hangul_l(cls, n):
 *         if n in [G.V, G.LV]:             # <<<<<<<<<<<<<<
 *             return False, cls.hangul_lv_or_v
 *         if n == G.LVT:
 */
  __Pyx_INCREF(__pyx_v_n);
  __pyx_t_1 = __pyx_v_n;
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 197, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_V); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 197, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_1, __pyx_t_4, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 197, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_5 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_5 < 0)) __PYX_ERR(0, 197, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (!__pyx_t_5) {
  } else {
    __pyx_t_2 = __pyx_t_5;
    goto __pyx_L4_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 197, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_4 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_LV); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 197, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_1, __pyx_t_4, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 197, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __pyx_t_5 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_5 < 0)) __PYX_ERR(0, 197, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_2 = __pyx_t_5;
  __pyx_L4_bool_binop_done:;
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_5 = (__pyx_t_2 != 0);
  if (__pyx_t_5) {

    /* "grapheme/cython/finder.pyx":198
 *     def hangul_l(cls, n):
 *         if n in [G.V, G.LV]:
 *             return False, cls.hangul_lv_or_v             # <<<<<<<<<<<<<<
 *         if n == G.LVT:
 *             return False, cls.hangul_lvt_or_t
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_hangul_lv_or_v); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 198, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_3 = PyTuple_New(2); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 198, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __Pyx_INCREF(Py_False);
    __Pyx_GIVEREF(Py_False);
    PyTuple_SET_ITEM(__pyx_t_3, 0, Py_False);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_3, 1, __pyx_t_1);
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_3;
    __pyx_t_3 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":197
 *     @classmethod
 *     def hangul_l(cls, n):
 *         if n in [G.V, G.LV]:             # <<<<<<<<<<<<<<
 *             return False, cls.hangul_lv_or_v
 *         if n == G.LVT:
 */
  }

  /* "grapheme/cython/finder.pyx":199
 *         if n in [G.V, G.LV]:
 *             return False, cls.hangul_lv_or_v
 *         if n == G.LVT:             # <<<<<<<<<<<<<<
 *             return False, cls.hangul_lvt_or_t
 *         if n == G.L:
 */
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 199, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_LVT); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 199, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_v_n, __pyx_t_1, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 199, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_5 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_5 < 0)) __PYX_ERR(0, 199, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (__pyx_t_5) {

    /* "grapheme/cython/finder.pyx":200
 *             return False, cls.hangul_lv_or_v
 *         if n == G.LVT:
 *             return False, cls.hangul_lvt_or_t             # <<<<<<<<<<<<<<
 *         if n == G.L:
 *             return False, cls.hangul_l
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_3 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_hangul_lvt_or_t); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 200, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __pyx_t_1 = PyTuple_New(2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 200, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __Pyx_INCREF(Py_False);
    __Pyx_GIVEREF(Py_False);
    PyTuple_SET_ITEM(__pyx_t_1, 0, Py_False);
    __Pyx_GIVEREF(__pyx_t_3);
    PyTuple_SET_ITEM(__pyx_t_1, 1, __pyx_t_3);
    __pyx_t_3 = 0;
    __pyx_r = __pyx_t_1;
    __pyx_t_1 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":199
 *         if n in [G.V, G.LV]:
 *             return False, cls.hangul_lv_or_v
 *         if n == G.LVT:             # <<<<<<<<<<<<<<
 *             return False, cls.hangul_lvt_or_t
 *         if n == G.L:
 */
  }

  /* "grapheme/cython/finder.pyx":201
 *         if n == G.LVT:
 *             return False, cls.hangul_lvt_or_t
 *         if n == G.L:             # <<<<<<<<<<<<<<
 *             return False, cls.hangul_l
 *         return cls.default(n)
 */
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 201, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_L); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 201, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_v_n, __pyx_t_3, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 201, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_5 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_5 < 0)) __PYX_ERR(0, 201, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (__pyx_t_5) {

    /* "grapheme/cython/finder.pyx":202
 *             return False, cls.hangul_lvt_or_t
 *         if n == G.L:
 *             return False, cls.hangul_l             # <<<<<<<<<<<<<<
 *         return cls.default(n)
 * 
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_hangul_l); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 202, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_3 = PyTuple_New(2); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 202, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __Pyx_INCREF(Py_False);
    __Pyx_GIVEREF(Py_False);
    PyTuple_SET_ITEM(__pyx_t_3, 0, Py_False);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_3, 1, __pyx_t_1);
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_3;
    __pyx_t_3 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":201
 *         if n == G.LVT:
 *             return False, cls.hangul_lvt_or_t
 *         if n == G.L:             # <<<<<<<<<<<<<<
 *             return False, cls.hangul_l
 *         return cls.default(n)
 */
  }

  /* "grapheme/cython/finder.pyx":203
 *         if n == G.L:
 *             return False, cls.hangul_l
 *         return cls.default(n)             # <<<<<<<<<<<<<<
 * 
 *     @classmethod
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 203, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_4 = NULL;
  if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_1))) {
    __pyx_t_4 = PyMethod_GET_SELF(__pyx_t_1);
    if (likely(__pyx_t_4)) {
      PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_1);
      __Pyx_INCREF(__pyx_t_4);
      __Pyx_INCREF(function);
      __Pyx_DECREF_SET(__pyx_t_1, function);
    }
  }
  __pyx_t_3 = (__pyx_t_4) ? __Pyx_PyObject_Call2Args(__pyx_t_1, __pyx_t_4, __pyx_v_n) : __Pyx_PyObject_CallOneArg(__pyx_t_1, __pyx_v_n);
  __Pyx_XDECREF(__pyx_t_4); __pyx_t_4 = 0;
  if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 203, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_r = __pyx_t_3;
  __pyx_t_3 = 0;
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":196
 *     # Hanguls
 *     @classmethod
 *     def hangul_l(cls, n):             # <<<<<<<<<<<<<<
 *         if n in [G.V, G.LV]:
 *             return False, cls.hangul_lv_or_v
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_3);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_AddTraceback("grapheme.cython.finder.FSM.hangul_l", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":206
 * 
 *     @classmethod
 *     def hangul_lv_or_v(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.V:
 *             return False, cls.hangul_lv_or_v
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_13hangul_lv_or_v(PyObject *__pyx_v_cls, PyObject *__pyx_v_n); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_13hangul_lv_or_v(PyObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("hangul_lv_or_v (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_3FSM_12hangul_lv_or_v(((PyTypeObject*)__pyx_v_cls), ((PyObject *)__pyx_v_n));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_12hangul_lv_or_v(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  int __pyx_t_3;
  PyObject *__pyx_t_4 = NULL;
  __Pyx_RefNannySetupContext("hangul_lv_or_v", 0);

  /* "grapheme/cython/finder.pyx":207
 *     @classmethod
 *     def hangul_lv_or_v(cls, n):
 *         if n == G.V:             # <<<<<<<<<<<<<<
 *             return False, cls.hangul_lv_or_v
 *         if n == G.T:
 */
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 207, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_V); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 207, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_v_n, __pyx_t_2, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 207, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 207, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":208
 *     def hangul_lv_or_v(cls, n):
 *         if n == G.V:
 *             return False, cls.hangul_lv_or_v             # <<<<<<<<<<<<<<
 *         if n == G.T:
 *             return False, cls.hangul_lvt_or_t
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_hangul_lv_or_v); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 208, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_2 = PyTuple_New(2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 208, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_INCREF(Py_False);
    __Pyx_GIVEREF(Py_False);
    PyTuple_SET_ITEM(__pyx_t_2, 0, Py_False);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_2, 1, __pyx_t_1);
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_2;
    __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":207
 *     @classmethod
 *     def hangul_lv_or_v(cls, n):
 *         if n == G.V:             # <<<<<<<<<<<<<<
 *             return False, cls.hangul_lv_or_v
 *         if n == G.T:
 */
  }

  /* "grapheme/cython/finder.pyx":209
 *         if n == G.V:
 *             return False, cls.hangul_lv_or_v
 *         if n == G.T:             # <<<<<<<<<<<<<<
 *             return False, cls.hangul_lvt_or_t
 *         return cls.default(n)
 */
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 209, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_T); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 209, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_v_n, __pyx_t_1, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 209, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 209, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":210
 *             return False, cls.hangul_lv_or_v
 *         if n == G.T:
 *             return False, cls.hangul_lvt_or_t             # <<<<<<<<<<<<<<
 *         return cls.default(n)
 * 
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_2 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_hangul_lvt_or_t); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 210, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_1 = PyTuple_New(2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 210, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __Pyx_INCREF(Py_False);
    __Pyx_GIVEREF(Py_False);
    PyTuple_SET_ITEM(__pyx_t_1, 0, Py_False);
    __Pyx_GIVEREF(__pyx_t_2);
    PyTuple_SET_ITEM(__pyx_t_1, 1, __pyx_t_2);
    __pyx_t_2 = 0;
    __pyx_r = __pyx_t_1;
    __pyx_t_1 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":209
 *         if n == G.V:
 *             return False, cls.hangul_lv_or_v
 *         if n == G.T:             # <<<<<<<<<<<<<<
 *             return False, cls.hangul_lvt_or_t
 *         return cls.default(n)
 */
  }

  /* "grapheme/cython/finder.pyx":211
 *         if n == G.T:
 *             return False, cls.hangul_lvt_or_t
 *         return cls.default(n)             # <<<<<<<<<<<<<<
 * 
 *     @classmethod
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 211, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_4 = NULL;
  if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_2))) {
    __pyx_t_4 = PyMethod_GET_SELF(__pyx_t_2);
    if (likely(__pyx_t_4)) {
      PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_2);
      __Pyx_INCREF(__pyx_t_4);
      __Pyx_INCREF(function);
      __Pyx_DECREF_SET(__pyx_t_2, function);
    }
  }
  __pyx_t_1 = (__pyx_t_4) ? __Pyx_PyObject_Call2Args(__pyx_t_2, __pyx_t_4, __pyx_v_n) : __Pyx_PyObject_CallOneArg(__pyx_t_2, __pyx_v_n);
  __Pyx_XDECREF(__pyx_t_4); __pyx_t_4 = 0;
  if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 211, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_r = __pyx_t_1;
  __pyx_t_1 = 0;
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":206
 * 
 *     @classmethod
 *     def hangul_lv_or_v(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.V:
 *             return False, cls.hangul_lv_or_v
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_AddTraceback("grapheme.cython.finder.FSM.hangul_lv_or_v", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":214
 * 
 *     @classmethod
 *     def hangul_lvt_or_t(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.T:
 *             return False, cls.hangul_lvt_or_t
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_15hangul_lvt_or_t(PyObject *__pyx_v_cls, PyObject *__pyx_v_n); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_15hangul_lvt_or_t(PyObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("hangul_lvt_or_t (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_3FSM_14hangul_lvt_or_t(((PyTypeObject*)__pyx_v_cls), ((PyObject *)__pyx_v_n));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_14hangul_lvt_or_t(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  int __pyx_t_3;
  PyObject *__pyx_t_4 = NULL;
  __Pyx_RefNannySetupContext("hangul_lvt_or_t", 0);

  /* "grapheme/cython/finder.pyx":215
 *     @classmethod
 *     def hangul_lvt_or_t(cls, n):
 *         if n == G.T:             # <<<<<<<<<<<<<<
 *             return False, cls.hangul_lvt_or_t
 *         return cls.default(n)
 */
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 215, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_T); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 215, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_v_n, __pyx_t_2, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 215, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 215, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":216
 *     def hangul_lvt_or_t(cls, n):
 *         if n == G.T:
 *             return False, cls.hangul_lvt_or_t             # <<<<<<<<<<<<<<
 *         return cls.default(n)
 * 
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_hangul_lvt_or_t); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 216, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_2 = PyTuple_New(2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 216, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_INCREF(Py_False);
    __Pyx_GIVEREF(Py_False);
    PyTuple_SET_ITEM(__pyx_t_2, 0, Py_False);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_2, 1, __pyx_t_1);
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_2;
    __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":215
 *     @classmethod
 *     def hangul_lvt_or_t(cls, n):
 *         if n == G.T:             # <<<<<<<<<<<<<<
 *             return False, cls.hangul_lvt_or_t
 *         return cls.default(n)
 */
  }

  /* "grapheme/cython/finder.pyx":217
 *         if n == G.T:
 *             return False, cls.hangul_lvt_or_t
 *         return cls.default(n)             # <<<<<<<<<<<<<<
 * 
 *     # Emojis
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 217, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_4 = NULL;
  if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_1))) {
    __pyx_t_4 = PyMethod_GET_SELF(__pyx_t_1);
    if (likely(__pyx_t_4)) {
      PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_1);
      __Pyx_INCREF(__pyx_t_4);
      __Pyx_INCREF(function);
      __Pyx_DECREF_SET(__pyx_t_1, function);
    }
  }
  __pyx_t_2 = (__pyx_t_4) ? __Pyx_PyObject_Call2Args(__pyx_t_1, __pyx_t_4, __pyx_v_n) : __Pyx_PyObject_CallOneArg(__pyx_t_1, __pyx_v_n);
  __Pyx_XDECREF(__pyx_t_4); __pyx_t_4 = 0;
  if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 217, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_r = __pyx_t_2;
  __pyx_t_2 = 0;
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":214
 * 
 *     @classmethod
 *     def hangul_lvt_or_t(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.T:
 *             return False, cls.hangul_lvt_or_t
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_AddTraceback("grapheme.cython.finder.FSM.hangul_lvt_or_t", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":221
 *     # Emojis
 *     @classmethod
 *     def emoji(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.EXTEND:
 *             return False, cls.emoji
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_17emoji(PyObject *__pyx_v_cls, PyObject *__pyx_v_n); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_17emoji(PyObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("emoji (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_3FSM_16emoji(((PyTypeObject*)__pyx_v_cls), ((PyObject *)__pyx_v_n));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_16emoji(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  int __pyx_t_3;
  PyObject *__pyx_t_4 = NULL;
  __Pyx_RefNannySetupContext("emoji", 0);

  /* "grapheme/cython/finder.pyx":222
 *     @classmethod
 *     def emoji(cls, n):
 *         if n == G.EXTEND:             # <<<<<<<<<<<<<<
 *             return False, cls.emoji
 *         if n == G.ZWJ:
 */
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 222, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_EXTEND); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 222, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_v_n, __pyx_t_2, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 222, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 222, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":223
 *     def emoji(cls, n):
 *         if n == G.EXTEND:
 *             return False, cls.emoji             # <<<<<<<<<<<<<<
 *         if n == G.ZWJ:
 *             return False, cls.emoji_zjw
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_emoji); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 223, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_2 = PyTuple_New(2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 223, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_INCREF(Py_False);
    __Pyx_GIVEREF(Py_False);
    PyTuple_SET_ITEM(__pyx_t_2, 0, Py_False);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_2, 1, __pyx_t_1);
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_2;
    __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":222
 *     @classmethod
 *     def emoji(cls, n):
 *         if n == G.EXTEND:             # <<<<<<<<<<<<<<
 *             return False, cls.emoji
 *         if n == G.ZWJ:
 */
  }

  /* "grapheme/cython/finder.pyx":224
 *         if n == G.EXTEND:
 *             return False, cls.emoji
 *         if n == G.ZWJ:             # <<<<<<<<<<<<<<
 *             return False, cls.emoji_zjw
 *         return cls.default(n)
 */
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 224, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_ZWJ); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 224, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_v_n, __pyx_t_1, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 224, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 224, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":225
 *             return False, cls.emoji
 *         if n == G.ZWJ:
 *             return False, cls.emoji_zjw             # <<<<<<<<<<<<<<
 *         return cls.default(n)
 * 
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_2 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_emoji_zjw); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 225, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_1 = PyTuple_New(2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 225, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __Pyx_INCREF(Py_False);
    __Pyx_GIVEREF(Py_False);
    PyTuple_SET_ITEM(__pyx_t_1, 0, Py_False);
    __Pyx_GIVEREF(__pyx_t_2);
    PyTuple_SET_ITEM(__pyx_t_1, 1, __pyx_t_2);
    __pyx_t_2 = 0;
    __pyx_r = __pyx_t_1;
    __pyx_t_1 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":224
 *         if n == G.EXTEND:
 *             return False, cls.emoji
 *         if n == G.ZWJ:             # <<<<<<<<<<<<<<
 *             return False, cls.emoji_zjw
 *         return cls.default(n)
 */
  }

  /* "grapheme/cython/finder.pyx":226
 *         if n == G.ZWJ:
 *             return False, cls.emoji_zjw
 *         return cls.default(n)             # <<<<<<<<<<<<<<
 * 
 *     @classmethod
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 226, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_4 = NULL;
  if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_2))) {
    __pyx_t_4 = PyMethod_GET_SELF(__pyx_t_2);
    if (likely(__pyx_t_4)) {
      PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_2);
      __Pyx_INCREF(__pyx_t_4);
      __Pyx_INCREF(function);
      __Pyx_DECREF_SET(__pyx_t_2, function);
    }
  }
  __pyx_t_1 = (__pyx_t_4) ? __Pyx_PyObject_Call2Args(__pyx_t_2, __pyx_t_4, __pyx_v_n) : __Pyx_PyObject_CallOneArg(__pyx_t_2, __pyx_v_n);
  __Pyx_XDECREF(__pyx_t_4); __pyx_t_4 = 0;
  if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 226, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_r = __pyx_t_1;
  __pyx_t_1 = 0;
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":221
 *     # Emojis
 *     @classmethod
 *     def emoji(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.EXTEND:
 *             return False, cls.emoji
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_AddTraceback("grapheme.cython.finder.FSM.emoji", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":229
 * 
 *     @classmethod
 *     def emoji_zjw(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.EXTENDED_PICTOGRAPHIC:
 *             return False, cls.emoji
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_19emoji_zjw(PyObject *__pyx_v_cls, PyObject *__pyx_v_n); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_19emoji_zjw(PyObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("emoji_zjw (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_3FSM_18emoji_zjw(((PyTypeObject*)__pyx_v_cls), ((PyObject *)__pyx_v_n));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_18emoji_zjw(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  int __pyx_t_3;
  PyObject *__pyx_t_4 = NULL;
  __Pyx_RefNannySetupContext("emoji_zjw", 0);

  /* "grapheme/cython/finder.pyx":230
 *     @classmethod
 *     def emoji_zjw(cls, n):
 *         if n == G.EXTENDED_PICTOGRAPHIC:             # <<<<<<<<<<<<<<
 *             return False, cls.emoji
 *         return cls.default(n)
 */
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 230, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_EXTENDED_PICTOGRAPHIC); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 230, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_v_n, __pyx_t_2, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 230, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 230, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":231
 *     def emoji_zjw(cls, n):
 *         if n == G.EXTENDED_PICTOGRAPHIC:
 *             return False, cls.emoji             # <<<<<<<<<<<<<<
 *         return cls.default(n)
 * 
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_emoji); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 231, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_2 = PyTuple_New(2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 231, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_INCREF(Py_False);
    __Pyx_GIVEREF(Py_False);
    PyTuple_SET_ITEM(__pyx_t_2, 0, Py_False);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_2, 1, __pyx_t_1);
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_2;
    __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":230
 *     @classmethod
 *     def emoji_zjw(cls, n):
 *         if n == G.EXTENDED_PICTOGRAPHIC:             # <<<<<<<<<<<<<<
 *             return False, cls.emoji
 *         return cls.default(n)
 */
  }

  /* "grapheme/cython/finder.pyx":232
 *         if n == G.EXTENDED_PICTOGRAPHIC:
 *             return False, cls.emoji
 *         return cls.default(n)             # <<<<<<<<<<<<<<
 * 
 *     # Regional indication (flag)
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 232, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_4 = NULL;
  if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_1))) {
    __pyx_t_4 = PyMethod_GET_SELF(__pyx_t_1);
    if (likely(__pyx_t_4)) {
      PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_1);
      __Pyx_INCREF(__pyx_t_4);
      __Pyx_INCREF(function);
      __Pyx_DECREF_SET(__pyx_t_1, function);
    }
  }
  __pyx_t_2 = (__pyx_t_4) ? __Pyx_PyObject_Call2Args(__pyx_t_1, __pyx_t_4, __pyx_v_n) : __Pyx_PyObject_CallOneArg(__pyx_t_1, __pyx_v_n);
  __Pyx_XDECREF(__pyx_t_4); __pyx_t_4 = 0;
  if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 232, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_r = __pyx_t_2;
  __pyx_t_2 = 0;
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":229
 * 
 *     @classmethod
 *     def emoji_zjw(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.EXTENDED_PICTOGRAPHIC:
 *             return False, cls.emoji
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_AddTraceback("grapheme.cython.finder.FSM.emoji_zjw", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":236
 *     # Regional indication (flag)
 *     @classmethod
 *     def ri(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.REGIONAL_INDICATOR:
 *             return False, cls.default
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_21ri(PyObject *__pyx_v_cls, PyObject *__pyx_v_n); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_21ri(PyObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("ri (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_3FSM_20ri(((PyTypeObject*)__pyx_v_cls), ((PyObject *)__pyx_v_n));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_20ri(PyTypeObject *__pyx_v_cls, PyObject *__pyx_v_n) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  int __pyx_t_3;
  PyObject *__pyx_t_4 = NULL;
  __Pyx_RefNannySetupContext("ri", 0);

  /* "grapheme/cython/finder.pyx":237
 *     @classmethod
 *     def ri(cls, n):
 *         if n == G.REGIONAL_INDICATOR:             # <<<<<<<<<<<<<<
 *             return False, cls.default
 *         return cls.default(n)
 */
  __Pyx_GetModuleGlobalName(__pyx_t_1, __pyx_n_s_G); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 237, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_1, __pyx_n_s_REGIONAL_INDICATOR); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 237, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyObject_RichCompare(__pyx_v_n, __pyx_t_2, Py_EQ); __Pyx_XGOTREF(__pyx_t_1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 237, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_3 = __Pyx_PyObject_IsTrue(__pyx_t_1); if (unlikely(__pyx_t_3 < 0)) __PYX_ERR(0, 237, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (__pyx_t_3) {

    /* "grapheme/cython/finder.pyx":238
 *     def ri(cls, n):
 *         if n == G.REGIONAL_INDICATOR:
 *             return False, cls.default             # <<<<<<<<<<<<<<
 *         return cls.default(n)
 * 
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 238, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_2 = PyTuple_New(2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 238, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_INCREF(Py_False);
    __Pyx_GIVEREF(Py_False);
    PyTuple_SET_ITEM(__pyx_t_2, 0, Py_False);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_2, 1, __pyx_t_1);
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_2;
    __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":237
 *     @classmethod
 *     def ri(cls, n):
 *         if n == G.REGIONAL_INDICATOR:             # <<<<<<<<<<<<<<
 *             return False, cls.default
 *         return cls.default(n)
 */
  }

  /* "grapheme/cython/finder.pyx":239
 *         if n == G.REGIONAL_INDICATOR:
 *             return False, cls.default
 *         return cls.default(n)             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v_cls), __pyx_n_s_default); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 239, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_4 = NULL;
  if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_1))) {
    __pyx_t_4 = PyMethod_GET_SELF(__pyx_t_1);
    if (likely(__pyx_t_4)) {
      PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_1);
      __Pyx_INCREF(__pyx_t_4);
      __Pyx_INCREF(function);
      __Pyx_DECREF_SET(__pyx_t_1, function);
    }
  }
  __pyx_t_2 = (__pyx_t_4) ? __Pyx_PyObject_Call2Args(__pyx_t_1, __pyx_t_4, __pyx_v_n) : __Pyx_PyObject_CallOneArg(__pyx_t_1, __pyx_v_n);
  __Pyx_XDECREF(__pyx_t_4); __pyx_t_4 = 0;
  if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 239, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_r = __pyx_t_2;
  __pyx_t_2 = 0;
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":236
 *     # Regional indication (flag)
 *     @classmethod
 *     def ri(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.REGIONAL_INDICATOR:
 *             return False, cls.default
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_AddTraceback("grapheme.cython.finder.FSM.ri", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "(tree fragment)":1
 * def __reduce_cython__(self):             # <<<<<<<<<<<<<<
 *     cdef tuple state
 *     cdef object _dict
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_23__reduce_cython__(PyObject *__pyx_v_self, CYTHON_UNUSED PyObject *unused); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_23__reduce_cython__(PyObject *__pyx_v_self, CYTHON_UNUSED PyObject *unused) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__reduce_cython__ (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_3FSM_22__reduce_cython__(((struct __pyx_obj_8grapheme_6cython_6finder_FSM *)__pyx_v_self));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_22__reduce_cython__(struct __pyx_obj_8grapheme_6cython_6finder_FSM *__pyx_v_self) {
  PyObject *__pyx_v_state = 0;
  PyObject *__pyx_v__dict = 0;
  int __pyx_v_use_setstate;
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_t_2;
  int __pyx_t_3;
  PyObject *__pyx_t_4 = NULL;
  PyObject *__pyx_t_5 = NULL;
  __Pyx_RefNannySetupContext("__reduce_cython__", 0);

  /* "(tree fragment)":5
 *     cdef object _dict
 *     cdef bint use_setstate
 *     state = ()             # <<<<<<<<<<<<<<
 *     _dict = getattr(self, '__dict__', None)
 *     if _dict is not None:
 */
  __Pyx_INCREF(__pyx_empty_tuple);
  __pyx_v_state = __pyx_empty_tuple;

  /* "(tree fragment)":6
 *     cdef bint use_setstate
 *     state = ()
 *     _dict = getattr(self, '__dict__', None)             # <<<<<<<<<<<<<<
 *     if _dict is not None:
 *         state += (_dict,)
 */
  __pyx_t_1 = __Pyx_GetAttr3(((PyObject *)__pyx_v_self), __pyx_n_s_dict, Py_None); if (unlikely(!__pyx_t_1)) __PYX_ERR(1, 6, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_v__dict = __pyx_t_1;
  __pyx_t_1 = 0;

  /* "(tree fragment)":7
 *     state = ()
 *     _dict = getattr(self, '__dict__', None)
 *     if _dict is not None:             # <<<<<<<<<<<<<<
 *         state += (_dict,)
 *         use_setstate = True
 */
  __pyx_t_2 = (__pyx_v__dict != Py_None);
  __pyx_t_3 = (__pyx_t_2 != 0);
  if (__pyx_t_3) {

    /* "(tree fragment)":8
 *     _dict = getattr(self, '__dict__', None)
 *     if _dict is not None:
 *         state += (_dict,)             # <<<<<<<<<<<<<<
 *         use_setstate = True
 *     else:
 */
    __pyx_t_1 = PyTuple_New(1); if (unlikely(!__pyx_t_1)) __PYX_ERR(1, 8, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __Pyx_INCREF(__pyx_v__dict);
    __Pyx_GIVEREF(__pyx_v__dict);
    PyTuple_SET_ITEM(__pyx_t_1, 0, __pyx_v__dict);
    __pyx_t_4 = PyNumber_InPlaceAdd(__pyx_v_state, __pyx_t_1); if (unlikely(!__pyx_t_4)) __PYX_ERR(1, 8, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_4);
    __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
    __Pyx_DECREF_SET(__pyx_v_state, ((PyObject*)__pyx_t_4));
    __pyx_t_4 = 0;

    /* "(tree fragment)":9
 *     if _dict is not None:
 *         state += (_dict,)
 *         use_setstate = True             # <<<<<<<<<<<<<<
 *     else:
 *         use_setstate = False
 */
    __pyx_v_use_setstate = 1;

    /* "(tree fragment)":7
 *     state = ()
 *     _dict = getattr(self, '__dict__', None)
 *     if _dict is not None:             # <<<<<<<<<<<<<<
 *         state += (_dict,)
 *         use_setstate = True
 */
    goto __pyx_L3;
  }

  /* "(tree fragment)":11
 *         use_setstate = True
 *     else:
 *         use_setstate = False             # <<<<<<<<<<<<<<
 *     if use_setstate:
 *         return __pyx_unpickle_FSM, (type(self), 0xd41d8cd, None), state
 */
  /*else*/ {
    __pyx_v_use_setstate = 0;
  }
  __pyx_L3:;

  /* "(tree fragment)":12
 *     else:
 *         use_setstate = False
 *     if use_setstate:             # <<<<<<<<<<<<<<
 *         return __pyx_unpickle_FSM, (type(self), 0xd41d8cd, None), state
 *     else:
 */
  __pyx_t_3 = (__pyx_v_use_setstate != 0);
  if (__pyx_t_3) {

    /* "(tree fragment)":13
 *         use_setstate = False
 *     if use_setstate:
 *         return __pyx_unpickle_FSM, (type(self), 0xd41d8cd, None), state             # <<<<<<<<<<<<<<
 *     else:
 *         return __pyx_unpickle_FSM, (type(self), 0xd41d8cd, state)
 */
    __Pyx_XDECREF(__pyx_r);
    __Pyx_GetModuleGlobalName(__pyx_t_4, __pyx_n_s_pyx_unpickle_FSM); if (unlikely(!__pyx_t_4)) __PYX_ERR(1, 13, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_4);
    __pyx_t_1 = PyTuple_New(3); if (unlikely(!__pyx_t_1)) __PYX_ERR(1, 13, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __Pyx_INCREF(((PyObject *)Py_TYPE(((PyObject *)__pyx_v_self))));
    __Pyx_GIVEREF(((PyObject *)Py_TYPE(((PyObject *)__pyx_v_self))));
    PyTuple_SET_ITEM(__pyx_t_1, 0, ((PyObject *)Py_TYPE(((PyObject *)__pyx_v_self))));
    __Pyx_INCREF(__pyx_int_222419149);
    __Pyx_GIVEREF(__pyx_int_222419149);
    PyTuple_SET_ITEM(__pyx_t_1, 1, __pyx_int_222419149);
    __Pyx_INCREF(Py_None);
    __Pyx_GIVEREF(Py_None);
    PyTuple_SET_ITEM(__pyx_t_1, 2, Py_None);
    __pyx_t_5 = PyTuple_New(3); if (unlikely(!__pyx_t_5)) __PYX_ERR(1, 13, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_5);
    __Pyx_GIVEREF(__pyx_t_4);
    PyTuple_SET_ITEM(__pyx_t_5, 0, __pyx_t_4);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_5, 1, __pyx_t_1);
    __Pyx_INCREF(__pyx_v_state);
    __Pyx_GIVEREF(__pyx_v_state);
    PyTuple_SET_ITEM(__pyx_t_5, 2, __pyx_v_state);
    __pyx_t_4 = 0;
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_5;
    __pyx_t_5 = 0;
    goto __pyx_L0;

    /* "(tree fragment)":12
 *     else:
 *         use_setstate = False
 *     if use_setstate:             # <<<<<<<<<<<<<<
 *         return __pyx_unpickle_FSM, (type(self), 0xd41d8cd, None), state
 *     else:
 */
  }

  /* "(tree fragment)":15
 *         return __pyx_unpickle_FSM, (type(self), 0xd41d8cd, None), state
 *     else:
 *         return __pyx_unpickle_FSM, (type(self), 0xd41d8cd, state)             # <<<<<<<<<<<<<<
 * def __setstate_cython__(self, __pyx_state):
 *     __pyx_unpickle_FSM__set_state(self, __pyx_state)
 */
  /*else*/ {
    __Pyx_XDECREF(__pyx_r);
    __Pyx_GetModuleGlobalName(__pyx_t_5, __pyx_n_s_pyx_unpickle_FSM); if (unlikely(!__pyx_t_5)) __PYX_ERR(1, 15, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_5);
    __pyx_t_1 = PyTuple_New(3); if (unlikely(!__pyx_t_1)) __PYX_ERR(1, 15, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __Pyx_INCREF(((PyObject *)Py_TYPE(((PyObject *)__pyx_v_self))));
    __Pyx_GIVEREF(((PyObject *)Py_TYPE(((PyObject *)__pyx_v_self))));
    PyTuple_SET_ITEM(__pyx_t_1, 0, ((PyObject *)Py_TYPE(((PyObject *)__pyx_v_self))));
    __Pyx_INCREF(__pyx_int_222419149);
    __Pyx_GIVEREF(__pyx_int_222419149);
    PyTuple_SET_ITEM(__pyx_t_1, 1, __pyx_int_222419149);
    __Pyx_INCREF(__pyx_v_state);
    __Pyx_GIVEREF(__pyx_v_state);
    PyTuple_SET_ITEM(__pyx_t_1, 2, __pyx_v_state);
    __pyx_t_4 = PyTuple_New(2); if (unlikely(!__pyx_t_4)) __PYX_ERR(1, 15, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_4);
    __Pyx_GIVEREF(__pyx_t_5);
    PyTuple_SET_ITEM(__pyx_t_4, 0, __pyx_t_5);
    __Pyx_GIVEREF(__pyx_t_1);
    PyTuple_SET_ITEM(__pyx_t_4, 1, __pyx_t_1);
    __pyx_t_5 = 0;
    __pyx_t_1 = 0;
    __pyx_r = __pyx_t_4;
    __pyx_t_4 = 0;
    goto __pyx_L0;
  }

  /* "(tree fragment)":1
 * def __reduce_cython__(self):             # <<<<<<<<<<<<<<
 *     cdef tuple state
 *     cdef object _dict
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_XDECREF(__pyx_t_5);
  __Pyx_AddTraceback("grapheme.cython.finder.FSM.__reduce_cython__", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XDECREF(__pyx_v_state);
  __Pyx_XDECREF(__pyx_v__dict);
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "(tree fragment)":16
 *     else:
 *         return __pyx_unpickle_FSM, (type(self), 0xd41d8cd, state)
 * def __setstate_cython__(self, __pyx_state):             # <<<<<<<<<<<<<<
 *     __pyx_unpickle_FSM__set_state(self, __pyx_state)
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_25__setstate_cython__(PyObject *__pyx_v_self, PyObject *__pyx_v___pyx_state); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3FSM_25__setstate_cython__(PyObject *__pyx_v_self, PyObject *__pyx_v___pyx_state) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__setstate_cython__ (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_3FSM_24__setstate_cython__(((struct __pyx_obj_8grapheme_6cython_6finder_FSM *)__pyx_v_self), ((PyObject *)__pyx_v___pyx_state));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_3FSM_24__setstate_cython__(struct __pyx_obj_8grapheme_6cython_6finder_FSM *__pyx_v_self, PyObject *__pyx_v___pyx_state) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  __Pyx_RefNannySetupContext("__setstate_cython__", 0);

  /* "(tree fragment)":17
 *         return __pyx_unpickle_FSM, (type(self), 0xd41d8cd, state)
 * def __setstate_cython__(self, __pyx_state):
 *     __pyx_unpickle_FSM__set_state(self, __pyx_state)             # <<<<<<<<<<<<<<
 */
  if (!(likely(PyTuple_CheckExact(__pyx_v___pyx_state))||((__pyx_v___pyx_state) == Py_None)||(PyErr_Format(PyExc_TypeError, "Expected %.16s, got %.200s", "tuple", Py_TYPE(__pyx_v___pyx_state)->tp_name), 0))) __PYX_ERR(1, 17, __pyx_L1_error)
  __pyx_t_1 = __pyx_f_8grapheme_6cython_6finder___pyx_unpickle_FSM__set_state(__pyx_v_self, ((PyObject*)__pyx_v___pyx_state)); if (unlikely(!__pyx_t_1)) __PYX_ERR(1, 17, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "(tree fragment)":16
 *     else:
 *         return __pyx_unpickle_FSM, (type(self), 0xd41d8cd, state)
 * def __setstate_cython__(self, __pyx_state):             # <<<<<<<<<<<<<<
 *     __pyx_unpickle_FSM__set_state(self, __pyx_state)
 */

  /* function exit code */
  __pyx_r = Py_None; __Pyx_INCREF(Py_None);
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_AddTraceback("grapheme.cython.finder.FSM.__setstate_cython__", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":248
 * 
 * 
 * def get_break_possibility(a, b):             # <<<<<<<<<<<<<<
 *     # Probably most common, included as short circuit before checking all else
 *     if a == G.OTHER and b == G.OTHER:
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_1get_break_possibility(PyObject *__pyx_self, PyObject *__pyx_args, PyObject *__pyx_kwds); /*proto*/
static PyMethodDef __pyx_mdef_8grapheme_6cython_6finder_1get_break_possibility = {"get_break_possibility", (PyCFunction)(void*)(PyCFunctionWithKeywords)__pyx_pw_8grapheme_6cython_6finder_1get_break_possibility, METH_VARARGS|METH_KEYWORDS, 0};
static PyObject *__pyx_pw_8grapheme_6cython_6finder_1get_break_possibility(PyObject *__pyx_self, PyObject *__pyx_args, PyObject *__pyx_kwds) {
  PyObject *__pyx_v_a = 0;
  PyObject *__pyx_v_b = 0;
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("get_break_possibility (wrapper)", 0);
  {
    static PyObject **__pyx_pyargnames[] = {&__pyx_n_s_a,&__pyx_n_s_b,0};
    PyObject* values[2] = {0,0};
    if (unlikely(__pyx_kwds)) {
      Py_ssize_t kw_args;
      const Py_ssize_t pos_args = PyTuple_GET_SIZE(__pyx_args);
      switch (pos_args) {
        case  2: values[1] = PyTuple_GET_ITEM(__pyx_args, 1);
        CYTHON_FALLTHROUGH;
        case  1: values[0] = PyTuple_GET_ITEM(__pyx_args, 0);
        CYTHON_FALLTHROUGH;
        case  0: break;
        default: goto __pyx_L5_argtuple_error;
      }
      kw_args = PyDict_Size(__pyx_kwds);
      switch (pos_args) {
        case  0:
        if (likely((values[0] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_a)) != 0)) kw_args--;
        else goto __pyx_L5_argtuple_error;
        CYTHON_FALLTHROUGH;
        case  1:
        if (likely((values[1] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_b)) != 0)) kw_args--;
        else {
          __Pyx_RaiseArgtupleInvalid("get_break_possibility", 1, 2, 2, 1); __PYX_ERR(0, 248, __pyx_L3_error)
        }
      }
      if (unlikely(kw_args > 0)) {
        if (unlikely(__Pyx_ParseOptionalKeywords(__pyx_kwds, __pyx_pyargnames, 0, values, pos_args, "get_break_possibility") < 0)) __PYX_ERR(0, 248, __pyx_L3_error)
      }
    } else if (PyTuple_GET_SIZE(__pyx_args) != 2) {
      goto __pyx_L5_argtuple_error;
    } else {
      values[0] = PyTuple_GET_ITEM(__pyx_args, 0);
      values[1] = PyTuple_GET_ITEM(__pyx_args, 1);
    }
    __pyx_v_a = values[0];
    __pyx_v_b = values[1];
  }
  goto __pyx_L4_argument_unpacking_done;
  __pyx_L5_argtuple_error:;
  __Pyx_RaiseArgtupleInvalid("get_break_possibility", 1, 2, 2, PyTuple_GET_SIZE(__pyx_args)); __PYX_ERR(0, 248, __pyx_L3_error)
  __pyx_L3_error:;
  __Pyx_AddTraceback("grapheme.cython.finder.get_break_possibility", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __Pyx_RefNannyFinishContext();
  return NULL;
  __pyx_L4_argument_unpacking_done:;
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_get_break_possibility(__pyx_self, __pyx_v_a, __pyx_v_b);

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_get_break_possibility(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_a, PyObject *__pyx_v_b) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  PyObject *__pyx_t_2 = NULL;
  PyObject *__pyx_t_3 = NULL;
  int __pyx_t_4;
  PyObject *__pyx_t_5 = NULL;
  int __pyx_t_6;
  __Pyx_RefNannySetupContext("get_break_possibility", 0);

  /* "grapheme/cython/finder.pyx":250
 * def get_break_possibility(a, b):
 *     # Probably most common, included as short circuit before checking all else
 *     if a == G.OTHER and b == G.OTHER:             # <<<<<<<<<<<<<<
 *         return BreakPossibility.CERTAIN
 * 
 */
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 250, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_OTHER); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 250, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_v_a, __pyx_t_3, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 250, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 250, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (__pyx_t_4) {
  } else {
    __pyx_t_1 = __pyx_t_4;
    goto __pyx_L4_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 250, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_OTHER); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 250, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_v_b, __pyx_t_3, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 250, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 250, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_1 = __pyx_t_4;
  __pyx_L4_bool_binop_done:;
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":251
 *     # Probably most common, included as short circuit before checking all else
 *     if a == G.OTHER and b == G.OTHER:
 *         return BreakPossibility.CERTAIN             # <<<<<<<<<<<<<<
 * 
 *     #assert isinstance(a, G)
 */
    __Pyx_XDECREF(__pyx_r);
    __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_BreakPossibility); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 251, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_CERTAIN); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 251, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    __pyx_r = __pyx_t_3;
    __pyx_t_3 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":250
 * def get_break_possibility(a, b):
 *     # Probably most common, included as short circuit before checking all else
 *     if a == G.OTHER and b == G.OTHER:             # <<<<<<<<<<<<<<
 *         return BreakPossibility.CERTAIN
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":259
 *     # sot (RI RI)* RI  RI
 *     # [ ^ RI] (RI RI) * RI        RI
 *     if a == G.REGIONAL_INDICATOR and b == G.REGIONAL_INDICATOR:             # <<<<<<<<<<<<<<
 *         return BreakPossibility.POSSIBLE
 * 
 */
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 259, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_REGIONAL_INDICATOR); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 259, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_v_a, __pyx_t_2, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 259, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 259, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (__pyx_t_4) {
  } else {
    __pyx_t_1 = __pyx_t_4;
    goto __pyx_L7_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 259, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_REGIONAL_INDICATOR); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 259, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_v_b, __pyx_t_2, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 259, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 259, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_1 = __pyx_t_4;
  __pyx_L7_bool_binop_done:;
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":260
 *     # [ ^ RI] (RI RI) * RI        RI
 *     if a == G.REGIONAL_INDICATOR and b == G.REGIONAL_INDICATOR:
 *         return BreakPossibility.POSSIBLE             # <<<<<<<<<<<<<<
 * 
 *     # (Control | CR | LF)
 */
    __Pyx_XDECREF(__pyx_r);
    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_BreakPossibility); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 260, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_POSSIBLE); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 260, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
    __pyx_r = __pyx_t_2;
    __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":259
 *     # sot (RI RI)* RI  RI
 *     # [ ^ RI] (RI RI) * RI        RI
 *     if a == G.REGIONAL_INDICATOR and b == G.REGIONAL_INDICATOR:             # <<<<<<<<<<<<<<
 *         return BreakPossibility.POSSIBLE
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":264
 *     # (Control | CR | LF)
 *     #   (Control | CR | LF)
 *     if a in [G.CONTROL, G.CR, G.LF] or b in [G.CONTROL, G.CR, G.LF]:             # <<<<<<<<<<<<<<
 *         # CR  LF
 *         if a == G.CR and b == G.LF:
 */
  __Pyx_INCREF(__pyx_v_a);
  __pyx_t_2 = __pyx_v_a;
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_CONTROL); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_2, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (!__pyx_t_6) {
  } else {
    __pyx_t_4 = __pyx_t_6;
    goto __pyx_L12_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_CR); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_2, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (!__pyx_t_6) {
  } else {
    __pyx_t_4 = __pyx_t_6;
    goto __pyx_L12_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_LF); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_2, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_4 = __pyx_t_6;
  __pyx_L12_bool_binop_done:;
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_6 = (__pyx_t_4 != 0);
  if (!__pyx_t_6) {
  } else {
    __pyx_t_1 = __pyx_t_6;
    goto __pyx_L10_bool_binop_done;
  }
  __Pyx_INCREF(__pyx_v_b);
  __pyx_t_2 = __pyx_v_b;
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_CONTROL); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_2, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (!__pyx_t_4) {
  } else {
    __pyx_t_6 = __pyx_t_4;
    goto __pyx_L15_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_CR); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_2, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (!__pyx_t_4) {
  } else {
    __pyx_t_6 = __pyx_t_4;
    goto __pyx_L15_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_LF); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_2, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 264, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_6 = __pyx_t_4;
  __pyx_L15_bool_binop_done:;
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_4 = (__pyx_t_6 != 0);
  __pyx_t_1 = __pyx_t_4;
  __pyx_L10_bool_binop_done:;
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":266
 *     if a in [G.CONTROL, G.CR, G.LF] or b in [G.CONTROL, G.CR, G.LF]:
 *         # CR  LF
 *         if a == G.CR and b == G.LF:             # <<<<<<<<<<<<<<
 *             return BreakPossibility.NO_BREAK
 *         else:
 */
    __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 266, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_CR); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 266, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    __pyx_t_2 = PyObject_RichCompare(__pyx_v_a, __pyx_t_3, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 266, __pyx_L1_error)
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
    __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 266, __pyx_L1_error)
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    if (__pyx_t_4) {
    } else {
      __pyx_t_1 = __pyx_t_4;
      goto __pyx_L19_bool_binop_done;
    }
    __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 266, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_LF); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 266, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    __pyx_t_2 = PyObject_RichCompare(__pyx_v_b, __pyx_t_3, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 266, __pyx_L1_error)
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
    __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 266, __pyx_L1_error)
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    __pyx_t_1 = __pyx_t_4;
    __pyx_L19_bool_binop_done:;
    if (__pyx_t_1) {

      /* "grapheme/cython/finder.pyx":267
 *         # CR  LF
 *         if a == G.CR and b == G.LF:
 *             return BreakPossibility.NO_BREAK             # <<<<<<<<<<<<<<
 *         else:
 *             return BreakPossibility.CERTAIN
 */
      __Pyx_XDECREF(__pyx_r);
      __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_BreakPossibility); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 267, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_2);
      __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_NO_BREAK); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 267, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_3);
      __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
      __pyx_r = __pyx_t_3;
      __pyx_t_3 = 0;
      goto __pyx_L0;

      /* "grapheme/cython/finder.pyx":266
 *     if a in [G.CONTROL, G.CR, G.LF] or b in [G.CONTROL, G.CR, G.LF]:
 *         # CR  LF
 *         if a == G.CR and b == G.LF:             # <<<<<<<<<<<<<<
 *             return BreakPossibility.NO_BREAK
 *         else:
 */
    }

    /* "grapheme/cython/finder.pyx":269
 *             return BreakPossibility.NO_BREAK
 *         else:
 *             return BreakPossibility.CERTAIN             # <<<<<<<<<<<<<<
 * 
 *     # L  (L | V | LV | LVT)
 */
    /*else*/ {
      __Pyx_XDECREF(__pyx_r);
      __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_BreakPossibility); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 269, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_3);
      __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_CERTAIN); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 269, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_2);
      __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
      __pyx_r = __pyx_t_2;
      __pyx_t_2 = 0;
      goto __pyx_L0;
    }

    /* "grapheme/cython/finder.pyx":264
 *     # (Control | CR | LF)
 *     #   (Control | CR | LF)
 *     if a in [G.CONTROL, G.CR, G.LF] or b in [G.CONTROL, G.CR, G.LF]:             # <<<<<<<<<<<<<<
 *         # CR  LF
 *         if a == G.CR and b == G.LF:
 */
  }

  /* "grapheme/cython/finder.pyx":272
 * 
 *     # L  (L | V | LV | LVT)
 *     if a == G.L and b in [G.L, G.V, G.LV, G.LVT]:             # <<<<<<<<<<<<<<
 *         return BreakPossibility.NO_BREAK
 * 
 */
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_L); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_v_a, __pyx_t_3, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (__pyx_t_4) {
  } else {
    __pyx_t_1 = __pyx_t_4;
    goto __pyx_L22_bool_binop_done;
  }
  __Pyx_INCREF(__pyx_v_b);
  __pyx_t_2 = __pyx_v_b;
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_L); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_2, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (!__pyx_t_6) {
  } else {
    __pyx_t_4 = __pyx_t_6;
    goto __pyx_L24_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_V); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_2, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (!__pyx_t_6) {
  } else {
    __pyx_t_4 = __pyx_t_6;
    goto __pyx_L24_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_LV); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_2, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (!__pyx_t_6) {
  } else {
    __pyx_t_4 = __pyx_t_6;
    goto __pyx_L24_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_LVT); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_2, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 272, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_4 = __pyx_t_6;
  __pyx_L24_bool_binop_done:;
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_6 = (__pyx_t_4 != 0);
  __pyx_t_1 = __pyx_t_6;
  __pyx_L22_bool_binop_done:;
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":273
 *     # L  (L | V | LV | LVT)
 *     if a == G.L and b in [G.L, G.V, G.LV, G.LVT]:
 *         return BreakPossibility.NO_BREAK             # <<<<<<<<<<<<<<
 * 
 *     # (LV | V)  (V | T)
 */
    __Pyx_XDECREF(__pyx_r);
    __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_BreakPossibility); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 273, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_NO_BREAK); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 273, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    __pyx_r = __pyx_t_3;
    __pyx_t_3 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":272
 * 
 *     # L  (L | V | LV | LVT)
 *     if a == G.L and b in [G.L, G.V, G.LV, G.LVT]:             # <<<<<<<<<<<<<<
 *         return BreakPossibility.NO_BREAK
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":276
 * 
 *     # (LV | V)  (V | T)
 *     if a in [G.LV, G.V] and b in [G.V, G.T]:             # <<<<<<<<<<<<<<
 *         return BreakPossibility.NO_BREAK
 * 
 */
  __Pyx_INCREF(__pyx_v_a);
  __pyx_t_3 = __pyx_v_a;
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_LV); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_t_3, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (!__pyx_t_4) {
  } else {
    __pyx_t_6 = __pyx_t_4;
    goto __pyx_L31_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_V); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_t_3, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_6 = __pyx_t_4;
  __pyx_L31_bool_binop_done:;
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_4 = (__pyx_t_6 != 0);
  if (__pyx_t_4) {
  } else {
    __pyx_t_1 = __pyx_t_4;
    goto __pyx_L29_bool_binop_done;
  }
  __Pyx_INCREF(__pyx_v_b);
  __pyx_t_3 = __pyx_v_b;
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_V); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_t_3, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (!__pyx_t_6) {
  } else {
    __pyx_t_4 = __pyx_t_6;
    goto __pyx_L33_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_T); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_t_3, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 276, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_4 = __pyx_t_6;
  __pyx_L33_bool_binop_done:;
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_6 = (__pyx_t_4 != 0);
  __pyx_t_1 = __pyx_t_6;
  __pyx_L29_bool_binop_done:;
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":277
 *     # (LV | V)  (V | T)
 *     if a in [G.LV, G.V] and b in [G.V, G.T]:
 *         return BreakPossibility.NO_BREAK             # <<<<<<<<<<<<<<
 * 
 *     # (LVT | T)        T
 */
    __Pyx_XDECREF(__pyx_r);
    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_BreakPossibility); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 277, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_NO_BREAK); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 277, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
    __pyx_r = __pyx_t_2;
    __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":276
 * 
 *     # (LV | V)  (V | T)
 *     if a in [G.LV, G.V] and b in [G.V, G.T]:             # <<<<<<<<<<<<<<
 *         return BreakPossibility.NO_BREAK
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":280
 * 
 *     # (LVT | T)        T
 *     if a in [G.LVT, G.T] and b == G.T:             # <<<<<<<<<<<<<<
 *         return BreakPossibility.NO_BREAK
 * 
 */
  __Pyx_INCREF(__pyx_v_a);
  __pyx_t_2 = __pyx_v_a;
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 280, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_LVT); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 280, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_2, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 280, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 280, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (!__pyx_t_4) {
  } else {
    __pyx_t_6 = __pyx_t_4;
    goto __pyx_L38_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 280, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_T); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 280, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_t_2, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 280, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 280, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_6 = __pyx_t_4;
  __pyx_L38_bool_binop_done:;
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_4 = (__pyx_t_6 != 0);
  if (__pyx_t_4) {
  } else {
    __pyx_t_1 = __pyx_t_4;
    goto __pyx_L36_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 280, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_T); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 280, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_v_b, __pyx_t_3, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 280, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 280, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_1 = __pyx_t_4;
  __pyx_L36_bool_binop_done:;
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":281
 *     # (LVT | T)        T
 *     if a in [G.LVT, G.T] and b == G.T:
 *         return BreakPossibility.NO_BREAK             # <<<<<<<<<<<<<<
 * 
 *     #  (Extend | ZWJ)
 */
    __Pyx_XDECREF(__pyx_r);
    __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_BreakPossibility); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 281, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_NO_BREAK); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 281, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    __pyx_r = __pyx_t_3;
    __pyx_t_3 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":280
 * 
 *     # (LVT | T)        T
 *     if a in [G.LVT, G.T] and b == G.T:             # <<<<<<<<<<<<<<
 *         return BreakPossibility.NO_BREAK
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":286
 *     #  SpacingMark
 *     # Prepend
 *     if b in [G.EXTEND, G.ZWJ, G.SPACING_MARK] or a == G.PREPEND:             # <<<<<<<<<<<<<<
 *         return BreakPossibility.NO_BREAK
 * 
 */
  __Pyx_INCREF(__pyx_v_b);
  __pyx_t_3 = __pyx_v_b;
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_EXTEND); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_t_3, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (!__pyx_t_6) {
  } else {
    __pyx_t_4 = __pyx_t_6;
    goto __pyx_L43_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_ZWJ); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_t_3, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (!__pyx_t_6) {
  } else {
    __pyx_t_4 = __pyx_t_6;
    goto __pyx_L43_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_5 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_SPACING_MARK); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_t_3, __pyx_t_5, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_4 = __pyx_t_6;
  __pyx_L43_bool_binop_done:;
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_6 = (__pyx_t_4 != 0);
  if (!__pyx_t_6) {
  } else {
    __pyx_t_1 = __pyx_t_6;
    goto __pyx_L41_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_G); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_PREPEND); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_3 = PyObject_RichCompare(__pyx_v_a, __pyx_t_2, Py_EQ); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 286, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_1 = __pyx_t_6;
  __pyx_L41_bool_binop_done:;
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":287
 *     # Prepend
 *     if b in [G.EXTEND, G.ZWJ, G.SPACING_MARK] or a == G.PREPEND:
 *         return BreakPossibility.NO_BREAK             # <<<<<<<<<<<<<<
 * 
 *     # \p{Extended_Pictographic} Extend* ZWJ  \p{Extended_Pictographic}
 */
    __Pyx_XDECREF(__pyx_r);
    __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_BreakPossibility); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 287, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_NO_BREAK); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 287, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
    __pyx_r = __pyx_t_2;
    __pyx_t_2 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":286
 *     #  SpacingMark
 *     # Prepend
 *     if b in [G.EXTEND, G.ZWJ, G.SPACING_MARK] or a == G.PREPEND:             # <<<<<<<<<<<<<<
 *         return BreakPossibility.NO_BREAK
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":290
 * 
 *     # \p{Extended_Pictographic} Extend* ZWJ  \p{Extended_Pictographic}
 *     if a == G.ZWJ and b == G.EXTENDED_PICTOGRAPHIC:             # <<<<<<<<<<<<<<
 *         return BreakPossibility.POSSIBLE
 * 
 */
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 290, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_ZWJ); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 290, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_v_a, __pyx_t_3, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 290, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 290, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (__pyx_t_6) {
  } else {
    __pyx_t_1 = __pyx_t_6;
    goto __pyx_L47_bool_binop_done;
  }
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_G); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 290, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_EXTENDED_PICTOGRAPHIC); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 290, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = PyObject_RichCompare(__pyx_v_b, __pyx_t_3, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 290, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_t_6 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_6 < 0)) __PYX_ERR(0, 290, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_1 = __pyx_t_6;
  __pyx_L47_bool_binop_done:;
  if (__pyx_t_1) {

    /* "grapheme/cython/finder.pyx":291
 *     # \p{Extended_Pictographic} Extend* ZWJ  \p{Extended_Pictographic}
 *     if a == G.ZWJ and b == G.EXTENDED_PICTOGRAPHIC:
 *         return BreakPossibility.POSSIBLE             # <<<<<<<<<<<<<<
 * 
 *     # everything else, assumes all other rules are included above
 */
    __Pyx_XDECREF(__pyx_r);
    __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_BreakPossibility); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 291, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_3 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_POSSIBLE); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 291, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    __pyx_r = __pyx_t_3;
    __pyx_t_3 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":290
 * 
 *     # \p{Extended_Pictographic} Extend* ZWJ  \p{Extended_Pictographic}
 *     if a == G.ZWJ and b == G.EXTENDED_PICTOGRAPHIC:             # <<<<<<<<<<<<<<
 *         return BreakPossibility.POSSIBLE
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":294
 * 
 *     # everything else, assumes all other rules are included above
 *     return BreakPossibility.CERTAIN             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __Pyx_XDECREF(__pyx_r);
  __Pyx_GetModuleGlobalName(__pyx_t_3, __pyx_n_s_BreakPossibility); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 294, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(__pyx_t_3, __pyx_n_s_CERTAIN); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 294, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_r = __pyx_t_2;
  __pyx_t_2 = 0;
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":248
 * 
 * 
 * def get_break_possibility(a, b):             # <<<<<<<<<<<<<<
 *     # Probably most common, included as short circuit before checking all else
 *     if a == G.OTHER and b == G.OTHER:
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_3);
  __Pyx_XDECREF(__pyx_t_5);
  __Pyx_AddTraceback("grapheme.cython.finder.get_break_possibility", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":297
 * 
 * 
 * def get_last_certain_break_index(string, index):             # <<<<<<<<<<<<<<
 *     if index >= len(string):
 *         return len(string)
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3get_last_certain_break_index(PyObject *__pyx_self, PyObject *__pyx_args, PyObject *__pyx_kwds); /*proto*/
static PyMethodDef __pyx_mdef_8grapheme_6cython_6finder_3get_last_certain_break_index = {"get_last_certain_break_index", (PyCFunction)(void*)(PyCFunctionWithKeywords)__pyx_pw_8grapheme_6cython_6finder_3get_last_certain_break_index, METH_VARARGS|METH_KEYWORDS, 0};
static PyObject *__pyx_pw_8grapheme_6cython_6finder_3get_last_certain_break_index(PyObject *__pyx_self, PyObject *__pyx_args, PyObject *__pyx_kwds) {
  PyObject *__pyx_v_string = 0;
  PyObject *__pyx_v_index = 0;
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("get_last_certain_break_index (wrapper)", 0);
  {
    static PyObject **__pyx_pyargnames[] = {&__pyx_n_s_string,&__pyx_n_s_index,0};
    PyObject* values[2] = {0,0};
    if (unlikely(__pyx_kwds)) {
      Py_ssize_t kw_args;
      const Py_ssize_t pos_args = PyTuple_GET_SIZE(__pyx_args);
      switch (pos_args) {
        case  2: values[1] = PyTuple_GET_ITEM(__pyx_args, 1);
        CYTHON_FALLTHROUGH;
        case  1: values[0] = PyTuple_GET_ITEM(__pyx_args, 0);
        CYTHON_FALLTHROUGH;
        case  0: break;
        default: goto __pyx_L5_argtuple_error;
      }
      kw_args = PyDict_Size(__pyx_kwds);
      switch (pos_args) {
        case  0:
        if (likely((values[0] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_string)) != 0)) kw_args--;
        else goto __pyx_L5_argtuple_error;
        CYTHON_FALLTHROUGH;
        case  1:
        if (likely((values[1] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_index)) != 0)) kw_args--;
        else {
          __Pyx_RaiseArgtupleInvalid("get_last_certain_break_index", 1, 2, 2, 1); __PYX_ERR(0, 297, __pyx_L3_error)
        }
      }
      if (unlikely(kw_args > 0)) {
        if (unlikely(__Pyx_ParseOptionalKeywords(__pyx_kwds, __pyx_pyargnames, 0, values, pos_args, "get_last_certain_break_index") < 0)) __PYX_ERR(0, 297, __pyx_L3_error)
      }
    } else if (PyTuple_GET_SIZE(__pyx_args) != 2) {
      goto __pyx_L5_argtuple_error;
    } else {
      values[0] = PyTuple_GET_ITEM(__pyx_args, 0);
      values[1] = PyTuple_GET_ITEM(__pyx_args, 1);
    }
    __pyx_v_string = values[0];
    __pyx_v_index = values[1];
  }
  goto __pyx_L4_argument_unpacking_done;
  __pyx_L5_argtuple_error:;
  __Pyx_RaiseArgtupleInvalid("get_last_certain_break_index", 1, 2, 2, PyTuple_GET_SIZE(__pyx_args)); __PYX_ERR(0, 297, __pyx_L3_error)
  __pyx_L3_error:;
  __Pyx_AddTraceback("grapheme.cython.finder.get_last_certain_break_index", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __Pyx_RefNannyFinishContext();
  return NULL;
  __pyx_L4_argument_unpacking_done:;
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_2get_last_certain_break_index(__pyx_self, __pyx_v_string, __pyx_v_index);

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_2get_last_certain_break_index(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_string, PyObject *__pyx_v_index) {
  int __pyx_v_prev;
  int __pyx_v_cur;
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  Py_ssize_t __pyx_t_1;
  PyObject *__pyx_t_2 = NULL;
  PyObject *__pyx_t_3 = NULL;
  int __pyx_t_4;
  Py_UCS4 __pyx_t_5;
  PyObject *__pyx_t_6 = NULL;
  PyObject *__pyx_t_7 = NULL;
  PyObject *__pyx_t_8 = NULL;
  int __pyx_t_9;
  PyObject *__pyx_t_10 = NULL;
  __Pyx_RefNannySetupContext("get_last_certain_break_index", 0);
  __Pyx_INCREF(__pyx_v_index);

  /* "grapheme/cython/finder.pyx":298
 * 
 * def get_last_certain_break_index(string, index):
 *     if index >= len(string):             # <<<<<<<<<<<<<<
 *         return len(string)
 * 
 */
  __pyx_t_1 = PyObject_Length(__pyx_v_string); if (unlikely(__pyx_t_1 == ((Py_ssize_t)-1))) __PYX_ERR(0, 298, __pyx_L1_error)
  __pyx_t_2 = PyInt_FromSsize_t(__pyx_t_1); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 298, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_3 = PyObject_RichCompare(__pyx_v_index, __pyx_t_2, Py_GE); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 298, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 298, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  if (__pyx_t_4) {

    /* "grapheme/cython/finder.pyx":299
 * def get_last_certain_break_index(string, index):
 *     if index >= len(string):
 *         return len(string)             # <<<<<<<<<<<<<<
 * 
 *     prev = get_group(string[index])
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = PyObject_Length(__pyx_v_string); if (unlikely(__pyx_t_1 == ((Py_ssize_t)-1))) __PYX_ERR(0, 299, __pyx_L1_error)
    __pyx_t_3 = PyInt_FromSsize_t(__pyx_t_1); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 299, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __pyx_r = __pyx_t_3;
    __pyx_t_3 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":298
 * 
 * def get_last_certain_break_index(string, index):
 *     if index >= len(string):             # <<<<<<<<<<<<<<
 *         return len(string)
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":301
 *         return len(string)
 * 
 *     prev = get_group(string[index])             # <<<<<<<<<<<<<<
 *     while True:
 *         if index <= 0:
 */
  __pyx_t_3 = __Pyx_PyObject_GetItem(__pyx_v_string, __pyx_v_index); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 301, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __pyx_t_5 = __Pyx_PyObject_AsPy_UCS4(__pyx_t_3); if (unlikely((__pyx_t_5 == (Py_UCS4)-1) && PyErr_Occurred())) __PYX_ERR(0, 301, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __pyx_v_prev = __pyx_f_8grapheme_6cython_23grapheme_property_group_c_get_group(__pyx_t_5);

  /* "grapheme/cython/finder.pyx":302
 * 
 *     prev = get_group(string[index])
 *     while True:             # <<<<<<<<<<<<<<
 *         if index <= 0:
 *             return 0
 */
  while (1) {

    /* "grapheme/cython/finder.pyx":303
 *     prev = get_group(string[index])
 *     while True:
 *         if index <= 0:             # <<<<<<<<<<<<<<
 *             return 0
 *         index -= 1
 */
    __pyx_t_3 = PyObject_RichCompare(__pyx_v_index, __pyx_int_0, Py_LE); __Pyx_XGOTREF(__pyx_t_3); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 303, __pyx_L1_error)
    __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_3); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 303, __pyx_L1_error)
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
    if (__pyx_t_4) {

      /* "grapheme/cython/finder.pyx":304
 *     while True:
 *         if index <= 0:
 *             return 0             # <<<<<<<<<<<<<<
 *         index -= 1
 *         cur = get_group(string[index])
 */
      __Pyx_XDECREF(__pyx_r);
      __Pyx_INCREF(__pyx_int_0);
      __pyx_r = __pyx_int_0;
      goto __pyx_L0;

      /* "grapheme/cython/finder.pyx":303
 *     prev = get_group(string[index])
 *     while True:
 *         if index <= 0:             # <<<<<<<<<<<<<<
 *             return 0
 *         index -= 1
 */
    }

    /* "grapheme/cython/finder.pyx":305
 *         if index <= 0:
 *             return 0
 *         index -= 1             # <<<<<<<<<<<<<<
 *         cur = get_group(string[index])
 *         if get_break_possibility(cur, prev) == BreakPossibility.CERTAIN:
 */
    __pyx_t_3 = __Pyx_PyInt_SubtractObjC(__pyx_v_index, __pyx_int_1, 1, 1, 0); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 305, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __Pyx_DECREF_SET(__pyx_v_index, __pyx_t_3);
    __pyx_t_3 = 0;

    /* "grapheme/cython/finder.pyx":306
 *             return 0
 *         index -= 1
 *         cur = get_group(string[index])             # <<<<<<<<<<<<<<
 *         if get_break_possibility(cur, prev) == BreakPossibility.CERTAIN:
 *             return index + 1
 */
    __pyx_t_3 = __Pyx_PyObject_GetItem(__pyx_v_string, __pyx_v_index); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 306, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __pyx_t_5 = __Pyx_PyObject_AsPy_UCS4(__pyx_t_3); if (unlikely((__pyx_t_5 == (Py_UCS4)-1) && PyErr_Occurred())) __PYX_ERR(0, 306, __pyx_L1_error)
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
    __pyx_v_cur = __pyx_f_8grapheme_6cython_23grapheme_property_group_c_get_group(__pyx_t_5);

    /* "grapheme/cython/finder.pyx":307
 *         index -= 1
 *         cur = get_group(string[index])
 *         if get_break_possibility(cur, prev) == BreakPossibility.CERTAIN:             # <<<<<<<<<<<<<<
 *             return index + 1
 *         prev = cur
 */
    __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_get_break_possibility); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 307, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_6 = __Pyx_PyInt_From_int(__pyx_v_cur); if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 307, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_6);
    __pyx_t_7 = __Pyx_PyInt_From_int(__pyx_v_prev); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 307, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_7);
    __pyx_t_8 = NULL;
    __pyx_t_9 = 0;
    if (CYTHON_UNPACK_METHODS && unlikely(PyMethod_Check(__pyx_t_2))) {
      __pyx_t_8 = PyMethod_GET_SELF(__pyx_t_2);
      if (likely(__pyx_t_8)) {
        PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_2);
        __Pyx_INCREF(__pyx_t_8);
        __Pyx_INCREF(function);
        __Pyx_DECREF_SET(__pyx_t_2, function);
        __pyx_t_9 = 1;
      }
    }
    #if CYTHON_FAST_PYCALL
    if (PyFunction_Check(__pyx_t_2)) {
      PyObject *__pyx_temp[3] = {__pyx_t_8, __pyx_t_6, __pyx_t_7};
      __pyx_t_3 = __Pyx_PyFunction_FastCall(__pyx_t_2, __pyx_temp+1-__pyx_t_9, 2+__pyx_t_9); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 307, __pyx_L1_error)
      __Pyx_XDECREF(__pyx_t_8); __pyx_t_8 = 0;
      __Pyx_GOTREF(__pyx_t_3);
      __Pyx_DECREF(__pyx_t_6); __pyx_t_6 = 0;
      __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;
    } else
    #endif
    #if CYTHON_FAST_PYCCALL
    if (__Pyx_PyFastCFunction_Check(__pyx_t_2)) {
      PyObject *__pyx_temp[3] = {__pyx_t_8, __pyx_t_6, __pyx_t_7};
      __pyx_t_3 = __Pyx_PyCFunction_FastCall(__pyx_t_2, __pyx_temp+1-__pyx_t_9, 2+__pyx_t_9); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 307, __pyx_L1_error)
      __Pyx_XDECREF(__pyx_t_8); __pyx_t_8 = 0;
      __Pyx_GOTREF(__pyx_t_3);
      __Pyx_DECREF(__pyx_t_6); __pyx_t_6 = 0;
      __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;
    } else
    #endif
    {
      __pyx_t_10 = PyTuple_New(2+__pyx_t_9); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 307, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_10);
      if (__pyx_t_8) {
        __Pyx_GIVEREF(__pyx_t_8); PyTuple_SET_ITEM(__pyx_t_10, 0, __pyx_t_8); __pyx_t_8 = NULL;
      }
      __Pyx_GIVEREF(__pyx_t_6);
      PyTuple_SET_ITEM(__pyx_t_10, 0+__pyx_t_9, __pyx_t_6);
      __Pyx_GIVEREF(__pyx_t_7);
      PyTuple_SET_ITEM(__pyx_t_10, 1+__pyx_t_9, __pyx_t_7);
      __pyx_t_6 = 0;
      __pyx_t_7 = 0;
      __pyx_t_3 = __Pyx_PyObject_Call(__pyx_t_2, __pyx_t_10, NULL); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 307, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_3);
      __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;
    }
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_BreakPossibility); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 307, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_10 = __Pyx_PyObject_GetAttrStr(__pyx_t_2, __pyx_n_s_CERTAIN); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 307, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_10);
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    __pyx_t_2 = PyObject_RichCompare(__pyx_t_3, __pyx_t_10, Py_EQ); __Pyx_XGOTREF(__pyx_t_2); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 307, __pyx_L1_error)
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
    __Pyx_DECREF(__pyx_t_10); __pyx_t_10 = 0;
    __pyx_t_4 = __Pyx_PyObject_IsTrue(__pyx_t_2); if (unlikely(__pyx_t_4 < 0)) __PYX_ERR(0, 307, __pyx_L1_error)
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    if (__pyx_t_4) {

      /* "grapheme/cython/finder.pyx":308
 *         cur = get_group(string[index])
 *         if get_break_possibility(cur, prev) == BreakPossibility.CERTAIN:
 *             return index + 1             # <<<<<<<<<<<<<<
 *         prev = cur
 * 
 */
      __Pyx_XDECREF(__pyx_r);
      __pyx_t_2 = __Pyx_PyInt_AddObjC(__pyx_v_index, __pyx_int_1, 1, 0, 0); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 308, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_2);
      __pyx_r = __pyx_t_2;
      __pyx_t_2 = 0;
      goto __pyx_L0;

      /* "grapheme/cython/finder.pyx":307
 *         index -= 1
 *         cur = get_group(string[index])
 *         if get_break_possibility(cur, prev) == BreakPossibility.CERTAIN:             # <<<<<<<<<<<<<<
 *             return index + 1
 *         prev = cur
 */
    }

    /* "grapheme/cython/finder.pyx":309
 *         if get_break_possibility(cur, prev) == BreakPossibility.CERTAIN:
 *             return index + 1
 *         prev = cur             # <<<<<<<<<<<<<<
 * 
 * 
 */
    __pyx_v_prev = __pyx_v_cur;
  }

  /* "grapheme/cython/finder.pyx":297
 * 
 * 
 * def get_last_certain_break_index(string, index):             # <<<<<<<<<<<<<<
 *     if index >= len(string):
 *         return len(string)
 */

  /* function exit code */
  __pyx_r = Py_None; __Pyx_INCREF(Py_None);
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_3);
  __Pyx_XDECREF(__pyx_t_6);
  __Pyx_XDECREF(__pyx_t_7);
  __Pyx_XDECREF(__pyx_t_8);
  __Pyx_XDECREF(__pyx_t_10);
  __Pyx_AddTraceback("grapheme.cython.finder.get_last_certain_break_index", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XDECREF(__pyx_v_index);
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":312
 * 
 * 
 * cpdef c_length(str string):             # <<<<<<<<<<<<<<
 *     cdef int result
 *     if string == "":
 */

static PyObject *__pyx_pw_8grapheme_6cython_6finder_5c_length(PyObject *__pyx_self, PyObject *__pyx_v_string); /*proto*/
static PyObject *__pyx_f_8grapheme_6cython_6finder_c_length(PyObject *__pyx_v_string, CYTHON_UNUSED int __pyx_skip_dispatch) {
  int __pyx_v_result;
  PyObject *__pyx_v_break_count = NULL;
  Py_UCS4 __pyx_v_codepoint;
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  int __pyx_t_2;
  Py_UCS4 __pyx_t_3;
  PyObject *__pyx_t_4 = NULL;
  Py_ssize_t __pyx_t_5;
  Py_ssize_t __pyx_t_6;
  void *__pyx_t_7;
  int __pyx_t_8;
  int __pyx_t_9;
  Py_ssize_t __pyx_t_10;
  PyObject *__pyx_t_11 = NULL;
  __Pyx_RefNannySetupContext("c_length", 0);

  /* "grapheme/cython/finder.pyx":314
 * cpdef c_length(str string):
 *     cdef int result
 *     if string == "":             # <<<<<<<<<<<<<<
 *         return 0
 * 
 */
  __pyx_t_1 = (__Pyx_PyUnicode_Equals(__pyx_v_string, __pyx_kp_u_, Py_EQ)); if (unlikely(__pyx_t_1 < 0)) __PYX_ERR(0, 314, __pyx_L1_error)
  __pyx_t_2 = (__pyx_t_1 != 0);
  if (__pyx_t_2) {

    /* "grapheme/cython/finder.pyx":315
 *     cdef int result
 *     if string == "":
 *         return 0             # <<<<<<<<<<<<<<
 * 
 *     break_count = 0
 */
    __Pyx_XDECREF(__pyx_r);
    __Pyx_INCREF(__pyx_int_0);
    __pyx_r = __pyx_int_0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":314
 * cpdef c_length(str string):
 *     cdef int result
 *     if string == "":             # <<<<<<<<<<<<<<
 *         return 0
 * 
 */
  }

  /* "grapheme/cython/finder.pyx":317
 *         return 0
 * 
 *     break_count = 0             # <<<<<<<<<<<<<<
 *     result = test_codepoint(FSM_default, get_group(string[0]))
 *     for codepoint in string[1:]:
 */
  __Pyx_INCREF(__pyx_int_0);
  __pyx_v_break_count = __pyx_int_0;

  /* "grapheme/cython/finder.pyx":318
 * 
 *     break_count = 0
 *     result = test_codepoint(FSM_default, get_group(string[0]))             # <<<<<<<<<<<<<<
 *     for codepoint in string[1:]:
 *         result = test_codepoint(result & SHOULD_BREAK_BIT_INVERSED, get_group(codepoint))
 */
  __pyx_t_3 = __Pyx_GetItemInt_Unicode(__pyx_v_string, 0, long, 1, __Pyx_PyInt_From_long, 0, 0, 1); if (unlikely(__pyx_t_3 == (Py_UCS4)-1)) __PYX_ERR(0, 318, __pyx_L1_error)
  __pyx_v_result = __pyx_f_8grapheme_6cython_6finder_test_codepoint(__pyx_v_8grapheme_6cython_9constants_FSM_default, __pyx_f_8grapheme_6cython_23grapheme_property_group_c_get_group(__pyx_t_3));

  /* "grapheme/cython/finder.pyx":319
 *     break_count = 0
 *     result = test_codepoint(FSM_default, get_group(string[0]))
 *     for codepoint in string[1:]:             # <<<<<<<<<<<<<<
 *         result = test_codepoint(result & SHOULD_BREAK_BIT_INVERSED, get_group(codepoint))
 *         if result & SHOULD_BREAK_BIT:
 */
  if (unlikely(__pyx_v_string == Py_None)) {
    PyErr_SetString(PyExc_TypeError, "'NoneType' object is not subscriptable");
    __PYX_ERR(0, 319, __pyx_L1_error)
  }
  __pyx_t_4 = __Pyx_PyUnicode_Substring(__pyx_v_string, 1, PY_SSIZE_T_MAX); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 319, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __pyx_t_9 = __Pyx_init_unicode_iteration(__pyx_t_4, (&__pyx_t_6), (&__pyx_t_7), (&__pyx_t_8)); if (unlikely(__pyx_t_9 == ((int)-1))) __PYX_ERR(0, 319, __pyx_L1_error)
  for (__pyx_t_10 = 0; __pyx_t_10 < __pyx_t_6; __pyx_t_10++) {
    __pyx_t_5 = __pyx_t_10;
    __pyx_v_codepoint = __Pyx_PyUnicode_READ(__pyx_t_8, __pyx_t_7, __pyx_t_5);

    /* "grapheme/cython/finder.pyx":320
 *     result = test_codepoint(FSM_default, get_group(string[0]))
 *     for codepoint in string[1:]:
 *         result = test_codepoint(result & SHOULD_BREAK_BIT_INVERSED, get_group(codepoint))             # <<<<<<<<<<<<<<
 *         if result & SHOULD_BREAK_BIT:
 *             break_count += 1
 */
    __pyx_v_result = __pyx_f_8grapheme_6cython_6finder_test_codepoint((__pyx_v_result & -1073741825L), __pyx_f_8grapheme_6cython_23grapheme_property_group_c_get_group(__pyx_v_codepoint));

    /* "grapheme/cython/finder.pyx":321
 *     for codepoint in string[1:]:
 *         result = test_codepoint(result & SHOULD_BREAK_BIT_INVERSED, get_group(codepoint))
 *         if result & SHOULD_BREAK_BIT:             # <<<<<<<<<<<<<<
 *             break_count += 1
 * 
 */
    __pyx_t_2 = ((__pyx_v_result & 0x40000000) != 0);
    if (__pyx_t_2) {

      /* "grapheme/cython/finder.pyx":322
 *         result = test_codepoint(result & SHOULD_BREAK_BIT_INVERSED, get_group(codepoint))
 *         if result & SHOULD_BREAK_BIT:
 *             break_count += 1             # <<<<<<<<<<<<<<
 * 
 *     return break_count + 1
 */
      __pyx_t_11 = __Pyx_PyInt_AddObjC(__pyx_v_break_count, __pyx_int_1, 1, 1, 0); if (unlikely(!__pyx_t_11)) __PYX_ERR(0, 322, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_11);
      __Pyx_DECREF_SET(__pyx_v_break_count, __pyx_t_11);
      __pyx_t_11 = 0;

      /* "grapheme/cython/finder.pyx":321
 *     for codepoint in string[1:]:
 *         result = test_codepoint(result & SHOULD_BREAK_BIT_INVERSED, get_group(codepoint))
 *         if result & SHOULD_BREAK_BIT:             # <<<<<<<<<<<<<<
 *             break_count += 1
 * 
 */
    }
  }
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;

  /* "grapheme/cython/finder.pyx":324
 *             break_count += 1
 * 
 *     return break_count + 1             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_4 = __Pyx_PyInt_AddObjC(__pyx_v_break_count, __pyx_int_1, 1, 0, 0); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 324, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  __pyx_r = __pyx_t_4;
  __pyx_t_4 = 0;
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":312
 * 
 * 
 * cpdef c_length(str string):             # <<<<<<<<<<<<<<
 *     cdef int result
 *     if string == "":
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_XDECREF(__pyx_t_11);
  __Pyx_AddTraceback("grapheme.cython.finder.c_length", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = 0;
  __pyx_L0:;
  __Pyx_XDECREF(__pyx_v_break_count);
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_5c_length(PyObject *__pyx_self, PyObject *__pyx_v_string); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_5c_length(PyObject *__pyx_self, PyObject *__pyx_v_string) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("c_length (wrapper)", 0);
  if (unlikely(!__Pyx_ArgTypeTest(((PyObject *)__pyx_v_string), (&PyUnicode_Type), 1, "string", 1))) __PYX_ERR(0, 312, __pyx_L1_error)
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_4c_length(__pyx_self, ((PyObject*)__pyx_v_string));

  /* function exit code */
  goto __pyx_L0;
  __pyx_L1_error:;
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_4c_length(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v_string) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  __Pyx_RefNannySetupContext("c_length", 0);
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = __pyx_f_8grapheme_6cython_6finder_c_length(__pyx_v_string, 0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 312, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_r = __pyx_t_1;
  __pyx_t_1 = 0;
  goto __pyx_L0;

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_AddTraceback("grapheme.cython.finder.c_length", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":332
 *     cdef state
 * 
 *     def __cinit__(self, str string):             # <<<<<<<<<<<<<<
 *         self.str_iter = iter(string)
 *         try:
 */

/* Python wrapper */
static int __pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_1__cinit__(PyObject *__pyx_v_self, PyObject *__pyx_args, PyObject *__pyx_kwds); /*proto*/
static int __pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_1__cinit__(PyObject *__pyx_v_self, PyObject *__pyx_args, PyObject *__pyx_kwds) {
  PyObject *__pyx_v_string = 0;
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__cinit__ (wrapper)", 0);
  {
    static PyObject **__pyx_pyargnames[] = {&__pyx_n_s_string,0};
    PyObject* values[1] = {0};
    if (unlikely(__pyx_kwds)) {
      Py_ssize_t kw_args;
      const Py_ssize_t pos_args = PyTuple_GET_SIZE(__pyx_args);
      switch (pos_args) {
        case  1: values[0] = PyTuple_GET_ITEM(__pyx_args, 0);
        CYTHON_FALLTHROUGH;
        case  0: break;
        default: goto __pyx_L5_argtuple_error;
      }
      kw_args = PyDict_Size(__pyx_kwds);
      switch (pos_args) {
        case  0:
        if (likely((values[0] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_string)) != 0)) kw_args--;
        else goto __pyx_L5_argtuple_error;
      }
      if (unlikely(kw_args > 0)) {
        if (unlikely(__Pyx_ParseOptionalKeywords(__pyx_kwds, __pyx_pyargnames, 0, values, pos_args, "__cinit__") < 0)) __PYX_ERR(0, 332, __pyx_L3_error)
      }
    } else if (PyTuple_GET_SIZE(__pyx_args) != 1) {
      goto __pyx_L5_argtuple_error;
    } else {
      values[0] = PyTuple_GET_ITEM(__pyx_args, 0);
    }
    __pyx_v_string = ((PyObject*)values[0]);
  }
  goto __pyx_L4_argument_unpacking_done;
  __pyx_L5_argtuple_error:;
  __Pyx_RaiseArgtupleInvalid("__cinit__", 1, 1, 1, PyTuple_GET_SIZE(__pyx_args)); __PYX_ERR(0, 332, __pyx_L3_error)
  __pyx_L3_error:;
  __Pyx_AddTraceback("grapheme.cython.finder.GraphemeIterator.__cinit__", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __Pyx_RefNannyFinishContext();
  return -1;
  __pyx_L4_argument_unpacking_done:;
  if (unlikely(!__Pyx_ArgTypeTest(((PyObject *)__pyx_v_string), (&PyUnicode_Type), 1, "string", 1))) __PYX_ERR(0, 332, __pyx_L1_error)
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator___cinit__(((struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *)__pyx_v_self), __pyx_v_string);

  /* function exit code */
  goto __pyx_L0;
  __pyx_L1_error:;
  __pyx_r = -1;
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static int __pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator___cinit__(struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *__pyx_v_self, PyObject *__pyx_v_string) {
  CYTHON_UNUSED PyObject *__pyx_v__ = NULL;
  PyObject *__pyx_v_state = NULL;
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  PyObject *__pyx_t_3 = NULL;
  PyObject *__pyx_t_4 = NULL;
  PyObject *__pyx_t_5 = NULL;
  Py_UCS4 __pyx_t_6;
  PyObject *__pyx_t_7 = NULL;
  PyObject *__pyx_t_8 = NULL;
  PyObject *(*__pyx_t_9)(PyObject *);
  int __pyx_t_10;
  __Pyx_RefNannySetupContext("__cinit__", 0);

  /* "grapheme/cython/finder.pyx":333
 * 
 *     def __cinit__(self, str string):
 *         self.str_iter = iter(string)             # <<<<<<<<<<<<<<
 *         try:
 *             self.buffer = next(self.str_iter)
 */
  __pyx_t_1 = PyObject_GetIter(__pyx_v_string); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 333, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_GIVEREF(__pyx_t_1);
  __Pyx_GOTREF(__pyx_v_self->str_iter);
  __Pyx_DECREF(__pyx_v_self->str_iter);
  __pyx_v_self->str_iter = __pyx_t_1;
  __pyx_t_1 = 0;

  /* "grapheme/cython/finder.pyx":334
 *     def __cinit__(self, str string):
 *         self.str_iter = iter(string)
 *         try:             # <<<<<<<<<<<<<<
 *             self.buffer = next(self.str_iter)
 *         except StopIteration:
 */
  {
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    __Pyx_ExceptionSave(&__pyx_t_2, &__pyx_t_3, &__pyx_t_4);
    __Pyx_XGOTREF(__pyx_t_2);
    __Pyx_XGOTREF(__pyx_t_3);
    __Pyx_XGOTREF(__pyx_t_4);
    /*try:*/ {

      /* "grapheme/cython/finder.pyx":335
 *         self.str_iter = iter(string)
 *         try:
 *             self.buffer = next(self.str_iter)             # <<<<<<<<<<<<<<
 *         except StopIteration:
 *             self.buffer = ""
 */
      __pyx_t_1 = __pyx_v_self->str_iter;
      __Pyx_INCREF(__pyx_t_1);
      __pyx_t_5 = __Pyx_PyIter_Next(__pyx_t_1); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 335, __pyx_L3_error)
      __Pyx_GOTREF(__pyx_t_5);
      __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
      if (!(likely(PyUnicode_CheckExact(__pyx_t_5))||((__pyx_t_5) == Py_None)||(PyErr_Format(PyExc_TypeError, "Expected %.16s, got %.200s", "unicode", Py_TYPE(__pyx_t_5)->tp_name), 0))) __PYX_ERR(0, 335, __pyx_L3_error)
      __Pyx_GIVEREF(__pyx_t_5);
      __Pyx_GOTREF(__pyx_v_self->buffer);
      __Pyx_DECREF(__pyx_v_self->buffer);
      __pyx_v_self->buffer = ((PyObject*)__pyx_t_5);
      __pyx_t_5 = 0;

      /* "grapheme/cython/finder.pyx":334
 *     def __cinit__(self, str string):
 *         self.str_iter = iter(string)
 *         try:             # <<<<<<<<<<<<<<
 *             self.buffer = next(self.str_iter)
 *         except StopIteration:
 */
    }

    /* "grapheme/cython/finder.pyx":339
 *             self.buffer = ""
 *         else:
 *             _, state = FSM.default(get_group(self.buffer))             # <<<<<<<<<<<<<<
 *             self.state = state
 * 
 */
    /*else:*/ {
      __pyx_t_1 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM), __pyx_n_s_default); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 339, __pyx_L5_except_error)
      __Pyx_GOTREF(__pyx_t_1);
      __pyx_t_6 = __Pyx_PyObject_AsPy_UCS4(__pyx_v_self->buffer); if (unlikely((__pyx_t_6 == (Py_UCS4)-1) && PyErr_Occurred())) __PYX_ERR(0, 339, __pyx_L5_except_error)
      __pyx_t_7 = __Pyx_PyInt_From_int(__pyx_f_8grapheme_6cython_23grapheme_property_group_c_get_group(__pyx_t_6)); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 339, __pyx_L5_except_error)
      __Pyx_GOTREF(__pyx_t_7);
      __pyx_t_8 = NULL;
      if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_1))) {
        __pyx_t_8 = PyMethod_GET_SELF(__pyx_t_1);
        if (likely(__pyx_t_8)) {
          PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_1);
          __Pyx_INCREF(__pyx_t_8);
          __Pyx_INCREF(function);
          __Pyx_DECREF_SET(__pyx_t_1, function);
        }
      }
      __pyx_t_5 = (__pyx_t_8) ? __Pyx_PyObject_Call2Args(__pyx_t_1, __pyx_t_8, __pyx_t_7) : __Pyx_PyObject_CallOneArg(__pyx_t_1, __pyx_t_7);
      __Pyx_XDECREF(__pyx_t_8); __pyx_t_8 = 0;
      __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;
      if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 339, __pyx_L5_except_error)
      __Pyx_GOTREF(__pyx_t_5);
      __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
      if ((likely(PyTuple_CheckExact(__pyx_t_5))) || (PyList_CheckExact(__pyx_t_5))) {
        PyObject* sequence = __pyx_t_5;
        Py_ssize_t size = __Pyx_PySequence_SIZE(sequence);
        if (unlikely(size != 2)) {
          if (size > 2) __Pyx_RaiseTooManyValuesError(2);
          else if (size >= 0) __Pyx_RaiseNeedMoreValuesError(size);
          __PYX_ERR(0, 339, __pyx_L5_except_error)
        }
        #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
        if (likely(PyTuple_CheckExact(sequence))) {
          __pyx_t_1 = PyTuple_GET_ITEM(sequence, 0); 
          __pyx_t_7 = PyTuple_GET_ITEM(sequence, 1); 
        } else {
          __pyx_t_1 = PyList_GET_ITEM(sequence, 0); 
          __pyx_t_7 = PyList_GET_ITEM(sequence, 1); 
        }
        __Pyx_INCREF(__pyx_t_1);
        __Pyx_INCREF(__pyx_t_7);
        #else
        __pyx_t_1 = PySequence_ITEM(sequence, 0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 339, __pyx_L5_except_error)
        __Pyx_GOTREF(__pyx_t_1);
        __pyx_t_7 = PySequence_ITEM(sequence, 1); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 339, __pyx_L5_except_error)
        __Pyx_GOTREF(__pyx_t_7);
        #endif
        __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
      } else {
        Py_ssize_t index = -1;
        __pyx_t_8 = PyObject_GetIter(__pyx_t_5); if (unlikely(!__pyx_t_8)) __PYX_ERR(0, 339, __pyx_L5_except_error)
        __Pyx_GOTREF(__pyx_t_8);
        __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;
        __pyx_t_9 = Py_TYPE(__pyx_t_8)->tp_iternext;
        index = 0; __pyx_t_1 = __pyx_t_9(__pyx_t_8); if (unlikely(!__pyx_t_1)) goto __pyx_L9_unpacking_failed;
        __Pyx_GOTREF(__pyx_t_1);
        index = 1; __pyx_t_7 = __pyx_t_9(__pyx_t_8); if (unlikely(!__pyx_t_7)) goto __pyx_L9_unpacking_failed;
        __Pyx_GOTREF(__pyx_t_7);
        if (__Pyx_IternextUnpackEndCheck(__pyx_t_9(__pyx_t_8), 2) < 0) __PYX_ERR(0, 339, __pyx_L5_except_error)
        __pyx_t_9 = NULL;
        __Pyx_DECREF(__pyx_t_8); __pyx_t_8 = 0;
        goto __pyx_L10_unpacking_done;
        __pyx_L9_unpacking_failed:;
        __Pyx_DECREF(__pyx_t_8); __pyx_t_8 = 0;
        __pyx_t_9 = NULL;
        if (__Pyx_IterFinish() == 0) __Pyx_RaiseNeedMoreValuesError(index);
        __PYX_ERR(0, 339, __pyx_L5_except_error)
        __pyx_L10_unpacking_done:;
      }
      __pyx_v__ = __pyx_t_1;
      __pyx_t_1 = 0;
      __pyx_v_state = __pyx_t_7;
      __pyx_t_7 = 0;

      /* "grapheme/cython/finder.pyx":340
 *         else:
 *             _, state = FSM.default(get_group(self.buffer))
 *             self.state = state             # <<<<<<<<<<<<<<
 * 
 *     def __iter__(self):
 */
      __Pyx_INCREF(__pyx_v_state);
      __Pyx_GIVEREF(__pyx_v_state);
      __Pyx_GOTREF(__pyx_v_self->state);
      __Pyx_DECREF(__pyx_v_self->state);
      __pyx_v_self->state = __pyx_v_state;
    }
    __Pyx_XDECREF(__pyx_t_2); __pyx_t_2 = 0;
    __Pyx_XDECREF(__pyx_t_3); __pyx_t_3 = 0;
    __Pyx_XDECREF(__pyx_t_4); __pyx_t_4 = 0;
    goto __pyx_L8_try_end;
    __pyx_L3_error:;
    __Pyx_XDECREF(__pyx_t_1); __pyx_t_1 = 0;
    __Pyx_XDECREF(__pyx_t_5); __pyx_t_5 = 0;

    /* "grapheme/cython/finder.pyx":336
 *         try:
 *             self.buffer = next(self.str_iter)
 *         except StopIteration:             # <<<<<<<<<<<<<<
 *             self.buffer = ""
 *         else:
 */
    __pyx_t_10 = __Pyx_PyErr_ExceptionMatches(__pyx_builtin_StopIteration);
    if (__pyx_t_10) {
      __Pyx_AddTraceback("grapheme.cython.finder.GraphemeIterator.__cinit__", __pyx_clineno, __pyx_lineno, __pyx_filename);
      if (__Pyx_GetException(&__pyx_t_5, &__pyx_t_7, &__pyx_t_1) < 0) __PYX_ERR(0, 336, __pyx_L5_except_error)
      __Pyx_GOTREF(__pyx_t_5);
      __Pyx_GOTREF(__pyx_t_7);
      __Pyx_GOTREF(__pyx_t_1);

      /* "grapheme/cython/finder.pyx":337
 *             self.buffer = next(self.str_iter)
 *         except StopIteration:
 *             self.buffer = ""             # <<<<<<<<<<<<<<
 *         else:
 *             _, state = FSM.default(get_group(self.buffer))
 */
      __Pyx_INCREF(__pyx_kp_u_);
      __Pyx_GIVEREF(__pyx_kp_u_);
      __Pyx_GOTREF(__pyx_v_self->buffer);
      __Pyx_DECREF(__pyx_v_self->buffer);
      __pyx_v_self->buffer = __pyx_kp_u_;
      __Pyx_XDECREF(__pyx_t_5); __pyx_t_5 = 0;
      __Pyx_XDECREF(__pyx_t_7); __pyx_t_7 = 0;
      __Pyx_XDECREF(__pyx_t_1); __pyx_t_1 = 0;
      goto __pyx_L4_exception_handled;
    }
    goto __pyx_L5_except_error;
    __pyx_L5_except_error:;

    /* "grapheme/cython/finder.pyx":334
 *     def __cinit__(self, str string):
 *         self.str_iter = iter(string)
 *         try:             # <<<<<<<<<<<<<<
 *             self.buffer = next(self.str_iter)
 *         except StopIteration:
 */
    __Pyx_XGIVEREF(__pyx_t_2);
    __Pyx_XGIVEREF(__pyx_t_3);
    __Pyx_XGIVEREF(__pyx_t_4);
    __Pyx_ExceptionReset(__pyx_t_2, __pyx_t_3, __pyx_t_4);
    goto __pyx_L1_error;
    __pyx_L4_exception_handled:;
    __Pyx_XGIVEREF(__pyx_t_2);
    __Pyx_XGIVEREF(__pyx_t_3);
    __Pyx_XGIVEREF(__pyx_t_4);
    __Pyx_ExceptionReset(__pyx_t_2, __pyx_t_3, __pyx_t_4);
    __pyx_L8_try_end:;
  }

  /* "grapheme/cython/finder.pyx":332
 *     cdef state
 * 
 *     def __cinit__(self, str string):             # <<<<<<<<<<<<<<
 *         self.str_iter = iter(string)
 *         try:
 */

  /* function exit code */
  __pyx_r = 0;
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_5);
  __Pyx_XDECREF(__pyx_t_7);
  __Pyx_XDECREF(__pyx_t_8);
  __Pyx_AddTraceback("grapheme.cython.finder.GraphemeIterator.__cinit__", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = -1;
  __pyx_L0:;
  __Pyx_XDECREF(__pyx_v__);
  __Pyx_XDECREF(__pyx_v_state);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":342
 *             self.state = state
 * 
 *     def __iter__(self):             # <<<<<<<<<<<<<<
 *         return self
 * 
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_3__iter__(PyObject *__pyx_v_self); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_3__iter__(PyObject *__pyx_v_self) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__iter__ (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator_2__iter__(((struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *)__pyx_v_self));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator_2__iter__(struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *__pyx_v_self) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__iter__", 0);

  /* "grapheme/cython/finder.pyx":343
 * 
 *     def __iter__(self):
 *         return self             # <<<<<<<<<<<<<<
 * 
 *     def __next__(self):
 */
  __Pyx_XDECREF(__pyx_r);
  __Pyx_INCREF(((PyObject *)__pyx_v_self));
  __pyx_r = ((PyObject *)__pyx_v_self);
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":342
 *             self.state = state
 * 
 *     def __iter__(self):             # <<<<<<<<<<<<<<
 *         return self
 * 
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":345
 *         return self
 * 
 *     def __next__(self):             # <<<<<<<<<<<<<<
 *         for codepoint in self.str_iter:
 *             should_break, state = self.state(get_group(codepoint))
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_5__next__(PyObject *__pyx_v_self); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_5__next__(PyObject *__pyx_v_self) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__next__ (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator_4__next__(((struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *)__pyx_v_self));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator_4__next__(struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *__pyx_v_self) {
  PyObject *__pyx_v_codepoint = NULL;
  PyObject *__pyx_v_should_break = NULL;
  PyObject *__pyx_v_state = NULL;
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  Py_ssize_t __pyx_t_2;
  PyObject *(*__pyx_t_3)(PyObject *);
  PyObject *__pyx_t_4 = NULL;
  Py_UCS4 __pyx_t_5;
  PyObject *__pyx_t_6 = NULL;
  PyObject *__pyx_t_7 = NULL;
  PyObject *__pyx_t_8 = NULL;
  PyObject *(*__pyx_t_9)(PyObject *);
  int __pyx_t_10;
  __Pyx_RefNannySetupContext("__next__", 0);

  /* "grapheme/cython/finder.pyx":346
 * 
 *     def __next__(self):
 *         for codepoint in self.str_iter:             # <<<<<<<<<<<<<<
 *             should_break, state = self.state(get_group(codepoint))
 *             self.state = state
 */
  if (likely(PyList_CheckExact(__pyx_v_self->str_iter)) || PyTuple_CheckExact(__pyx_v_self->str_iter)) {
    __pyx_t_1 = __pyx_v_self->str_iter; __Pyx_INCREF(__pyx_t_1); __pyx_t_2 = 0;
    __pyx_t_3 = NULL;
  } else {
    __pyx_t_2 = -1; __pyx_t_1 = PyObject_GetIter(__pyx_v_self->str_iter); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 346, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_t_3 = Py_TYPE(__pyx_t_1)->tp_iternext; if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 346, __pyx_L1_error)
  }
  for (;;) {
    if (likely(!__pyx_t_3)) {
      if (likely(PyList_CheckExact(__pyx_t_1))) {
        if (__pyx_t_2 >= PyList_GET_SIZE(__pyx_t_1)) break;
        #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
        __pyx_t_4 = PyList_GET_ITEM(__pyx_t_1, __pyx_t_2); __Pyx_INCREF(__pyx_t_4); __pyx_t_2++; if (unlikely(0 < 0)) __PYX_ERR(0, 346, __pyx_L1_error)
        #else
        __pyx_t_4 = PySequence_ITEM(__pyx_t_1, __pyx_t_2); __pyx_t_2++; if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 346, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_4);
        #endif
      } else {
        if (__pyx_t_2 >= PyTuple_GET_SIZE(__pyx_t_1)) break;
        #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
        __pyx_t_4 = PyTuple_GET_ITEM(__pyx_t_1, __pyx_t_2); __Pyx_INCREF(__pyx_t_4); __pyx_t_2++; if (unlikely(0 < 0)) __PYX_ERR(0, 346, __pyx_L1_error)
        #else
        __pyx_t_4 = PySequence_ITEM(__pyx_t_1, __pyx_t_2); __pyx_t_2++; if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 346, __pyx_L1_error)
        __Pyx_GOTREF(__pyx_t_4);
        #endif
      }
    } else {
      __pyx_t_4 = __pyx_t_3(__pyx_t_1);
      if (unlikely(!__pyx_t_4)) {
        PyObject* exc_type = PyErr_Occurred();
        if (exc_type) {
          if (likely(__Pyx_PyErr_GivenExceptionMatches(exc_type, PyExc_StopIteration))) PyErr_Clear();
          else __PYX_ERR(0, 346, __pyx_L1_error)
        }
        break;
      }
      __Pyx_GOTREF(__pyx_t_4);
    }
    __Pyx_XDECREF_SET(__pyx_v_codepoint, __pyx_t_4);
    __pyx_t_4 = 0;

    /* "grapheme/cython/finder.pyx":347
 *     def __next__(self):
 *         for codepoint in self.str_iter:
 *             should_break, state = self.state(get_group(codepoint))             # <<<<<<<<<<<<<<
 *             self.state = state
 * 
 */
    __pyx_t_5 = __Pyx_PyObject_AsPy_UCS4(__pyx_v_codepoint); if (unlikely((__pyx_t_5 == (Py_UCS4)-1) && PyErr_Occurred())) __PYX_ERR(0, 347, __pyx_L1_error)
    __pyx_t_6 = __Pyx_PyInt_From_int(__pyx_f_8grapheme_6cython_23grapheme_property_group_c_get_group(__pyx_t_5)); if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 347, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_6);
    __Pyx_INCREF(__pyx_v_self->state);
    __pyx_t_7 = __pyx_v_self->state; __pyx_t_8 = NULL;
    if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_7))) {
      __pyx_t_8 = PyMethod_GET_SELF(__pyx_t_7);
      if (likely(__pyx_t_8)) {
        PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_7);
        __Pyx_INCREF(__pyx_t_8);
        __Pyx_INCREF(function);
        __Pyx_DECREF_SET(__pyx_t_7, function);
      }
    }
    __pyx_t_4 = (__pyx_t_8) ? __Pyx_PyObject_Call2Args(__pyx_t_7, __pyx_t_8, __pyx_t_6) : __Pyx_PyObject_CallOneArg(__pyx_t_7, __pyx_t_6);
    __Pyx_XDECREF(__pyx_t_8); __pyx_t_8 = 0;
    __Pyx_DECREF(__pyx_t_6); __pyx_t_6 = 0;
    if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 347, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_4);
    __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;
    if ((likely(PyTuple_CheckExact(__pyx_t_4))) || (PyList_CheckExact(__pyx_t_4))) {
      PyObject* sequence = __pyx_t_4;
      Py_ssize_t size = __Pyx_PySequence_SIZE(sequence);
      if (unlikely(size != 2)) {
        if (size > 2) __Pyx_RaiseTooManyValuesError(2);
        else if (size >= 0) __Pyx_RaiseNeedMoreValuesError(size);
        __PYX_ERR(0, 347, __pyx_L1_error)
      }
      #if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
      if (likely(PyTuple_CheckExact(sequence))) {
        __pyx_t_7 = PyTuple_GET_ITEM(sequence, 0); 
        __pyx_t_6 = PyTuple_GET_ITEM(sequence, 1); 
      } else {
        __pyx_t_7 = PyList_GET_ITEM(sequence, 0); 
        __pyx_t_6 = PyList_GET_ITEM(sequence, 1); 
      }
      __Pyx_INCREF(__pyx_t_7);
      __Pyx_INCREF(__pyx_t_6);
      #else
      __pyx_t_7 = PySequence_ITEM(sequence, 0); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 347, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_7);
      __pyx_t_6 = PySequence_ITEM(sequence, 1); if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 347, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_6);
      #endif
      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
    } else {
      Py_ssize_t index = -1;
      __pyx_t_8 = PyObject_GetIter(__pyx_t_4); if (unlikely(!__pyx_t_8)) __PYX_ERR(0, 347, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_8);
      __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
      __pyx_t_9 = Py_TYPE(__pyx_t_8)->tp_iternext;
      index = 0; __pyx_t_7 = __pyx_t_9(__pyx_t_8); if (unlikely(!__pyx_t_7)) goto __pyx_L5_unpacking_failed;
      __Pyx_GOTREF(__pyx_t_7);
      index = 1; __pyx_t_6 = __pyx_t_9(__pyx_t_8); if (unlikely(!__pyx_t_6)) goto __pyx_L5_unpacking_failed;
      __Pyx_GOTREF(__pyx_t_6);
      if (__Pyx_IternextUnpackEndCheck(__pyx_t_9(__pyx_t_8), 2) < 0) __PYX_ERR(0, 347, __pyx_L1_error)
      __pyx_t_9 = NULL;
      __Pyx_DECREF(__pyx_t_8); __pyx_t_8 = 0;
      goto __pyx_L6_unpacking_done;
      __pyx_L5_unpacking_failed:;
      __Pyx_DECREF(__pyx_t_8); __pyx_t_8 = 0;
      __pyx_t_9 = NULL;
      if (__Pyx_IterFinish() == 0) __Pyx_RaiseNeedMoreValuesError(index);
      __PYX_ERR(0, 347, __pyx_L1_error)
      __pyx_L6_unpacking_done:;
    }
    __Pyx_XDECREF_SET(__pyx_v_should_break, __pyx_t_7);
    __pyx_t_7 = 0;
    __Pyx_XDECREF_SET(__pyx_v_state, __pyx_t_6);
    __pyx_t_6 = 0;

    /* "grapheme/cython/finder.pyx":348
 *         for codepoint in self.str_iter:
 *             should_break, state = self.state(get_group(codepoint))
 *             self.state = state             # <<<<<<<<<<<<<<
 * 
 *             if should_break:
 */
    __Pyx_INCREF(__pyx_v_state);
    __Pyx_GIVEREF(__pyx_v_state);
    __Pyx_GOTREF(__pyx_v_self->state);
    __Pyx_DECREF(__pyx_v_self->state);
    __pyx_v_self->state = __pyx_v_state;

    /* "grapheme/cython/finder.pyx":350
 *             self.state = state
 * 
 *             if should_break:             # <<<<<<<<<<<<<<
 *                 return self._break(codepoint)
 *             self.buffer += codepoint
 */
    __pyx_t_10 = __Pyx_PyObject_IsTrue(__pyx_v_should_break); if (unlikely(__pyx_t_10 < 0)) __PYX_ERR(0, 350, __pyx_L1_error)
    if (__pyx_t_10) {

      /* "grapheme/cython/finder.pyx":351
 * 
 *             if should_break:
 *                 return self._break(codepoint)             # <<<<<<<<<<<<<<
 *             self.buffer += codepoint
 *         if self.buffer:
 */
      __Pyx_XDECREF(__pyx_r);
      if (!(likely(PyUnicode_CheckExact(__pyx_v_codepoint))||((__pyx_v_codepoint) == Py_None)||(PyErr_Format(PyExc_TypeError, "Expected %.16s, got %.200s", "unicode", Py_TYPE(__pyx_v_codepoint)->tp_name), 0))) __PYX_ERR(0, 351, __pyx_L1_error)
      __pyx_t_4 = ((struct __pyx_vtabstruct_8grapheme_6cython_6finder_GraphemeIterator *)__pyx_v_self->__pyx_vtab)->_break(__pyx_v_self, ((PyObject*)__pyx_v_codepoint)); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 351, __pyx_L1_error)
      __Pyx_GOTREF(__pyx_t_4);
      __pyx_r = __pyx_t_4;
      __pyx_t_4 = 0;
      __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
      goto __pyx_L0;

      /* "grapheme/cython/finder.pyx":350
 *             self.state = state
 * 
 *             if should_break:             # <<<<<<<<<<<<<<
 *                 return self._break(codepoint)
 *             self.buffer += codepoint
 */
    }

    /* "grapheme/cython/finder.pyx":352
 *             if should_break:
 *                 return self._break(codepoint)
 *             self.buffer += codepoint             # <<<<<<<<<<<<<<
 *         if self.buffer:
 *             return self._break("")
 */
    __pyx_t_4 = PyNumber_InPlaceAdd(__pyx_v_self->buffer, __pyx_v_codepoint); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 352, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_4);
    if (!(likely(PyUnicode_CheckExact(__pyx_t_4))||((__pyx_t_4) == Py_None)||(PyErr_Format(PyExc_TypeError, "Expected %.16s, got %.200s", "unicode", Py_TYPE(__pyx_t_4)->tp_name), 0))) __PYX_ERR(0, 352, __pyx_L1_error)
    __Pyx_GIVEREF(__pyx_t_4);
    __Pyx_GOTREF(__pyx_v_self->buffer);
    __Pyx_DECREF(__pyx_v_self->buffer);
    __pyx_v_self->buffer = ((PyObject*)__pyx_t_4);
    __pyx_t_4 = 0;

    /* "grapheme/cython/finder.pyx":346
 * 
 *     def __next__(self):
 *         for codepoint in self.str_iter:             # <<<<<<<<<<<<<<
 *             should_break, state = self.state(get_group(codepoint))
 *             self.state = state
 */
  }
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "grapheme/cython/finder.pyx":353
 *                 return self._break(codepoint)
 *             self.buffer += codepoint
 *         if self.buffer:             # <<<<<<<<<<<<<<
 *             return self._break("")
 *         raise StopIteration
 */
  __pyx_t_10 = (__pyx_v_self->buffer != Py_None)&&(__Pyx_PyUnicode_IS_TRUE(__pyx_v_self->buffer) != 0);
  if (__pyx_t_10) {

    /* "grapheme/cython/finder.pyx":354
 *             self.buffer += codepoint
 *         if self.buffer:
 *             return self._break("")             # <<<<<<<<<<<<<<
 *         raise StopIteration
 * 
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_t_1 = ((struct __pyx_vtabstruct_8grapheme_6cython_6finder_GraphemeIterator *)__pyx_v_self->__pyx_vtab)->_break(__pyx_v_self, __pyx_kp_u_); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 354, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_1);
    __pyx_r = __pyx_t_1;
    __pyx_t_1 = 0;
    goto __pyx_L0;

    /* "grapheme/cython/finder.pyx":353
 *                 return self._break(codepoint)
 *             self.buffer += codepoint
 *         if self.buffer:             # <<<<<<<<<<<<<<
 *             return self._break("")
 *         raise StopIteration
 */
  }

  /* "grapheme/cython/finder.pyx":355
 *         if self.buffer:
 *             return self._break("")
 *         raise StopIteration             # <<<<<<<<<<<<<<
 * 
 *     cdef _break(self, str new):
 */
  __Pyx_Raise(__pyx_builtin_StopIteration, 0, 0, 0);
  __PYX_ERR(0, 355, __pyx_L1_error)

  /* "grapheme/cython/finder.pyx":345
 *         return self
 * 
 *     def __next__(self):             # <<<<<<<<<<<<<<
 *         for codepoint in self.str_iter:
 *             should_break, state = self.state(get_group(codepoint))
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_XDECREF(__pyx_t_6);
  __Pyx_XDECREF(__pyx_t_7);
  __Pyx_XDECREF(__pyx_t_8);
  __Pyx_AddTraceback("grapheme.cython.finder.GraphemeIterator.__next__", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XDECREF(__pyx_v_codepoint);
  __Pyx_XDECREF(__pyx_v_should_break);
  __Pyx_XDECREF(__pyx_v_state);
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "grapheme/cython/finder.pyx":357
 *         raise StopIteration
 * 
 *     cdef _break(self, str new):             # <<<<<<<<<<<<<<
 *         old_buffer = self.buffer
 *         self.buffer = new
 */

static PyObject *__pyx_f_8grapheme_6cython_6finder_16GraphemeIterator__break(struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *__pyx_v_self, PyObject *__pyx_v_new) {
  PyObject *__pyx_v_old_buffer = NULL;
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  __Pyx_RefNannySetupContext("_break", 0);

  /* "grapheme/cython/finder.pyx":358
 * 
 *     cdef _break(self, str new):
 *         old_buffer = self.buffer             # <<<<<<<<<<<<<<
 *         self.buffer = new
 *         return old_buffer
 */
  __pyx_t_1 = __pyx_v_self->buffer;
  __Pyx_INCREF(__pyx_t_1);
  __pyx_v_old_buffer = ((PyObject*)__pyx_t_1);
  __pyx_t_1 = 0;

  /* "grapheme/cython/finder.pyx":359
 *     cdef _break(self, str new):
 *         old_buffer = self.buffer
 *         self.buffer = new             # <<<<<<<<<<<<<<
 *         return old_buffer
 * 
 */
  __Pyx_INCREF(__pyx_v_new);
  __Pyx_GIVEREF(__pyx_v_new);
  __Pyx_GOTREF(__pyx_v_self->buffer);
  __Pyx_DECREF(__pyx_v_self->buffer);
  __pyx_v_self->buffer = __pyx_v_new;

  /* "grapheme/cython/finder.pyx":360
 *         old_buffer = self.buffer
 *         self.buffer = new
 *         return old_buffer             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __Pyx_XDECREF(__pyx_r);
  __Pyx_INCREF(__pyx_v_old_buffer);
  __pyx_r = __pyx_v_old_buffer;
  goto __pyx_L0;

  /* "grapheme/cython/finder.pyx":357
 *         raise StopIteration
 * 
 *     cdef _break(self, str new):             # <<<<<<<<<<<<<<
 *         old_buffer = self.buffer
 *         self.buffer = new
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_XDECREF(__pyx_v_old_buffer);
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "(tree fragment)":1
 * def __reduce_cython__(self):             # <<<<<<<<<<<<<<
 *     raise TypeError("no default __reduce__ due to non-trivial __cinit__")
 * def __setstate_cython__(self, __pyx_state):
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_7__reduce_cython__(PyObject *__pyx_v_self, CYTHON_UNUSED PyObject *unused); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_7__reduce_cython__(PyObject *__pyx_v_self, CYTHON_UNUSED PyObject *unused) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__reduce_cython__ (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator_6__reduce_cython__(((struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *)__pyx_v_self));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator_6__reduce_cython__(CYTHON_UNUSED struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *__pyx_v_self) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  __Pyx_RefNannySetupContext("__reduce_cython__", 0);

  /* "(tree fragment)":2
 * def __reduce_cython__(self):
 *     raise TypeError("no default __reduce__ due to non-trivial __cinit__")             # <<<<<<<<<<<<<<
 * def __setstate_cython__(self, __pyx_state):
 *     raise TypeError("no default __reduce__ due to non-trivial __cinit__")
 */
  __pyx_t_1 = __Pyx_PyObject_Call(__pyx_builtin_TypeError, __pyx_tuple__2, NULL); if (unlikely(!__pyx_t_1)) __PYX_ERR(1, 2, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_Raise(__pyx_t_1, 0, 0, 0);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __PYX_ERR(1, 2, __pyx_L1_error)

  /* "(tree fragment)":1
 * def __reduce_cython__(self):             # <<<<<<<<<<<<<<
 *     raise TypeError("no default __reduce__ due to non-trivial __cinit__")
 * def __setstate_cython__(self, __pyx_state):
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_AddTraceback("grapheme.cython.finder.GraphemeIterator.__reduce_cython__", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "(tree fragment)":3
 * def __reduce_cython__(self):
 *     raise TypeError("no default __reduce__ due to non-trivial __cinit__")
 * def __setstate_cython__(self, __pyx_state):             # <<<<<<<<<<<<<<
 *     raise TypeError("no default __reduce__ due to non-trivial __cinit__")
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_9__setstate_cython__(PyObject *__pyx_v_self, PyObject *__pyx_v___pyx_state); /*proto*/
static PyObject *__pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_9__setstate_cython__(PyObject *__pyx_v_self, PyObject *__pyx_v___pyx_state) {
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__setstate_cython__ (wrapper)", 0);
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator_8__setstate_cython__(((struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *)__pyx_v_self), ((PyObject *)__pyx_v___pyx_state));

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_16GraphemeIterator_8__setstate_cython__(CYTHON_UNUSED struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *__pyx_v_self, CYTHON_UNUSED PyObject *__pyx_v___pyx_state) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  __Pyx_RefNannySetupContext("__setstate_cython__", 0);

  /* "(tree fragment)":4
 *     raise TypeError("no default __reduce__ due to non-trivial __cinit__")
 * def __setstate_cython__(self, __pyx_state):
 *     raise TypeError("no default __reduce__ due to non-trivial __cinit__")             # <<<<<<<<<<<<<<
 */
  __pyx_t_1 = __Pyx_PyObject_Call(__pyx_builtin_TypeError, __pyx_tuple__3, NULL); if (unlikely(!__pyx_t_1)) __PYX_ERR(1, 4, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_Raise(__pyx_t_1, 0, 0, 0);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __PYX_ERR(1, 4, __pyx_L1_error)

  /* "(tree fragment)":3
 * def __reduce_cython__(self):
 *     raise TypeError("no default __reduce__ due to non-trivial __cinit__")
 * def __setstate_cython__(self, __pyx_state):             # <<<<<<<<<<<<<<
 *     raise TypeError("no default __reduce__ due to non-trivial __cinit__")
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_AddTraceback("grapheme.cython.finder.GraphemeIterator.__setstate_cython__", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "(tree fragment)":1
 * def __pyx_unpickle_FSM(__pyx_type, long __pyx_checksum, __pyx_state):             # <<<<<<<<<<<<<<
 *     cdef object __pyx_PickleError
 *     cdef object __pyx_result
 */

/* Python wrapper */
static PyObject *__pyx_pw_8grapheme_6cython_6finder_7__pyx_unpickle_FSM(PyObject *__pyx_self, PyObject *__pyx_args, PyObject *__pyx_kwds); /*proto*/
static PyMethodDef __pyx_mdef_8grapheme_6cython_6finder_7__pyx_unpickle_FSM = {"__pyx_unpickle_FSM", (PyCFunction)(void*)(PyCFunctionWithKeywords)__pyx_pw_8grapheme_6cython_6finder_7__pyx_unpickle_FSM, METH_VARARGS|METH_KEYWORDS, 0};
static PyObject *__pyx_pw_8grapheme_6cython_6finder_7__pyx_unpickle_FSM(PyObject *__pyx_self, PyObject *__pyx_args, PyObject *__pyx_kwds) {
  PyObject *__pyx_v___pyx_type = 0;
  long __pyx_v___pyx_checksum;
  PyObject *__pyx_v___pyx_state = 0;
  PyObject *__pyx_r = 0;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__pyx_unpickle_FSM (wrapper)", 0);
  {
    static PyObject **__pyx_pyargnames[] = {&__pyx_n_s_pyx_type,&__pyx_n_s_pyx_checksum,&__pyx_n_s_pyx_state,0};
    PyObject* values[3] = {0,0,0};
    if (unlikely(__pyx_kwds)) {
      Py_ssize_t kw_args;
      const Py_ssize_t pos_args = PyTuple_GET_SIZE(__pyx_args);
      switch (pos_args) {
        case  3: values[2] = PyTuple_GET_ITEM(__pyx_args, 2);
        CYTHON_FALLTHROUGH;
        case  2: values[1] = PyTuple_GET_ITEM(__pyx_args, 1);
        CYTHON_FALLTHROUGH;
        case  1: values[0] = PyTuple_GET_ITEM(__pyx_args, 0);
        CYTHON_FALLTHROUGH;
        case  0: break;
        default: goto __pyx_L5_argtuple_error;
      }
      kw_args = PyDict_Size(__pyx_kwds);
      switch (pos_args) {
        case  0:
        if (likely((values[0] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_pyx_type)) != 0)) kw_args--;
        else goto __pyx_L5_argtuple_error;
        CYTHON_FALLTHROUGH;
        case  1:
        if (likely((values[1] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_pyx_checksum)) != 0)) kw_args--;
        else {
          __Pyx_RaiseArgtupleInvalid("__pyx_unpickle_FSM", 1, 3, 3, 1); __PYX_ERR(1, 1, __pyx_L3_error)
        }
        CYTHON_FALLTHROUGH;
        case  2:
        if (likely((values[2] = __Pyx_PyDict_GetItemStr(__pyx_kwds, __pyx_n_s_pyx_state)) != 0)) kw_args--;
        else {
          __Pyx_RaiseArgtupleInvalid("__pyx_unpickle_FSM", 1, 3, 3, 2); __PYX_ERR(1, 1, __pyx_L3_error)
        }
      }
      if (unlikely(kw_args > 0)) {
        if (unlikely(__Pyx_ParseOptionalKeywords(__pyx_kwds, __pyx_pyargnames, 0, values, pos_args, "__pyx_unpickle_FSM") < 0)) __PYX_ERR(1, 1, __pyx_L3_error)
      }
    } else if (PyTuple_GET_SIZE(__pyx_args) != 3) {
      goto __pyx_L5_argtuple_error;
    } else {
      values[0] = PyTuple_GET_ITEM(__pyx_args, 0);
      values[1] = PyTuple_GET_ITEM(__pyx_args, 1);
      values[2] = PyTuple_GET_ITEM(__pyx_args, 2);
    }
    __pyx_v___pyx_type = values[0];
    __pyx_v___pyx_checksum = __Pyx_PyInt_As_long(values[1]); if (unlikely((__pyx_v___pyx_checksum == (long)-1) && PyErr_Occurred())) __PYX_ERR(1, 1, __pyx_L3_error)
    __pyx_v___pyx_state = values[2];
  }
  goto __pyx_L4_argument_unpacking_done;
  __pyx_L5_argtuple_error:;
  __Pyx_RaiseArgtupleInvalid("__pyx_unpickle_FSM", 1, 3, 3, PyTuple_GET_SIZE(__pyx_args)); __PYX_ERR(1, 1, __pyx_L3_error)
  __pyx_L3_error:;
  __Pyx_AddTraceback("grapheme.cython.finder.__pyx_unpickle_FSM", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __Pyx_RefNannyFinishContext();
  return NULL;
  __pyx_L4_argument_unpacking_done:;
  __pyx_r = __pyx_pf_8grapheme_6cython_6finder_6__pyx_unpickle_FSM(__pyx_self, __pyx_v___pyx_type, __pyx_v___pyx_checksum, __pyx_v___pyx_state);

  /* function exit code */
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_pf_8grapheme_6cython_6finder_6__pyx_unpickle_FSM(CYTHON_UNUSED PyObject *__pyx_self, PyObject *__pyx_v___pyx_type, long __pyx_v___pyx_checksum, PyObject *__pyx_v___pyx_state) {
  PyObject *__pyx_v___pyx_PickleError = 0;
  PyObject *__pyx_v___pyx_result = 0;
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  PyObject *__pyx_t_2 = NULL;
  PyObject *__pyx_t_3 = NULL;
  PyObject *__pyx_t_4 = NULL;
  PyObject *__pyx_t_5 = NULL;
  int __pyx_t_6;
  __Pyx_RefNannySetupContext("__pyx_unpickle_FSM", 0);

  /* "(tree fragment)":4
 *     cdef object __pyx_PickleError
 *     cdef object __pyx_result
 *     if __pyx_checksum != 0xd41d8cd:             # <<<<<<<<<<<<<<
 *         from pickle import PickleError as __pyx_PickleError
 *         raise __pyx_PickleError("Incompatible checksums (%s vs 0xd41d8cd = ())" % __pyx_checksum)
 */
  __pyx_t_1 = ((__pyx_v___pyx_checksum != 0xd41d8cd) != 0);
  if (__pyx_t_1) {

    /* "(tree fragment)":5
 *     cdef object __pyx_result
 *     if __pyx_checksum != 0xd41d8cd:
 *         from pickle import PickleError as __pyx_PickleError             # <<<<<<<<<<<<<<
 *         raise __pyx_PickleError("Incompatible checksums (%s vs 0xd41d8cd = ())" % __pyx_checksum)
 *     __pyx_result = FSM.__new__(__pyx_type)
 */
    __pyx_t_2 = PyList_New(1); if (unlikely(!__pyx_t_2)) __PYX_ERR(1, 5, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_INCREF(__pyx_n_s_PickleError);
    __Pyx_GIVEREF(__pyx_n_s_PickleError);
    PyList_SET_ITEM(__pyx_t_2, 0, __pyx_n_s_PickleError);
    __pyx_t_3 = __Pyx_Import(__pyx_n_s_pickle, __pyx_t_2, 0); if (unlikely(!__pyx_t_3)) __PYX_ERR(1, 5, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    __pyx_t_2 = __Pyx_ImportFrom(__pyx_t_3, __pyx_n_s_PickleError); if (unlikely(!__pyx_t_2)) __PYX_ERR(1, 5, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __Pyx_INCREF(__pyx_t_2);
    __pyx_v___pyx_PickleError = __pyx_t_2;
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;

    /* "(tree fragment)":6
 *     if __pyx_checksum != 0xd41d8cd:
 *         from pickle import PickleError as __pyx_PickleError
 *         raise __pyx_PickleError("Incompatible checksums (%s vs 0xd41d8cd = ())" % __pyx_checksum)             # <<<<<<<<<<<<<<
 *     __pyx_result = FSM.__new__(__pyx_type)
 *     if __pyx_state is not None:
 */
    __pyx_t_2 = __Pyx_PyInt_From_long(__pyx_v___pyx_checksum); if (unlikely(!__pyx_t_2)) __PYX_ERR(1, 6, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_2);
    __pyx_t_4 = __Pyx_PyString_Format(__pyx_kp_s_Incompatible_checksums_s_vs_0xd4, __pyx_t_2); if (unlikely(!__pyx_t_4)) __PYX_ERR(1, 6, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_4);
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    __Pyx_INCREF(__pyx_v___pyx_PickleError);
    __pyx_t_2 = __pyx_v___pyx_PickleError; __pyx_t_5 = NULL;
    if (CYTHON_UNPACK_METHODS && unlikely(PyMethod_Check(__pyx_t_2))) {
      __pyx_t_5 = PyMethod_GET_SELF(__pyx_t_2);
      if (likely(__pyx_t_5)) {
        PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_2);
        __Pyx_INCREF(__pyx_t_5);
        __Pyx_INCREF(function);
        __Pyx_DECREF_SET(__pyx_t_2, function);
      }
    }
    __pyx_t_3 = (__pyx_t_5) ? __Pyx_PyObject_Call2Args(__pyx_t_2, __pyx_t_5, __pyx_t_4) : __Pyx_PyObject_CallOneArg(__pyx_t_2, __pyx_t_4);
    __Pyx_XDECREF(__pyx_t_5); __pyx_t_5 = 0;
    __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
    if (unlikely(!__pyx_t_3)) __PYX_ERR(1, 6, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
    __Pyx_Raise(__pyx_t_3, 0, 0, 0);
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
    __PYX_ERR(1, 6, __pyx_L1_error)

    /* "(tree fragment)":4
 *     cdef object __pyx_PickleError
 *     cdef object __pyx_result
 *     if __pyx_checksum != 0xd41d8cd:             # <<<<<<<<<<<<<<
 *         from pickle import PickleError as __pyx_PickleError
 *         raise __pyx_PickleError("Incompatible checksums (%s vs 0xd41d8cd = ())" % __pyx_checksum)
 */
  }

  /* "(tree fragment)":7
 *         from pickle import PickleError as __pyx_PickleError
 *         raise __pyx_PickleError("Incompatible checksums (%s vs 0xd41d8cd = ())" % __pyx_checksum)
 *     __pyx_result = FSM.__new__(__pyx_type)             # <<<<<<<<<<<<<<
 *     if __pyx_state is not None:
 *         __pyx_unpickle_FSM__set_state(<FSM> __pyx_result, __pyx_state)
 */
  __pyx_t_2 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM), __pyx_n_s_new); if (unlikely(!__pyx_t_2)) __PYX_ERR(1, 7, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_4 = NULL;
  if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_2))) {
    __pyx_t_4 = PyMethod_GET_SELF(__pyx_t_2);
    if (likely(__pyx_t_4)) {
      PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_2);
      __Pyx_INCREF(__pyx_t_4);
      __Pyx_INCREF(function);
      __Pyx_DECREF_SET(__pyx_t_2, function);
    }
  }
  __pyx_t_3 = (__pyx_t_4) ? __Pyx_PyObject_Call2Args(__pyx_t_2, __pyx_t_4, __pyx_v___pyx_type) : __Pyx_PyObject_CallOneArg(__pyx_t_2, __pyx_v___pyx_type);
  __Pyx_XDECREF(__pyx_t_4); __pyx_t_4 = 0;
  if (unlikely(!__pyx_t_3)) __PYX_ERR(1, 7, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_v___pyx_result = __pyx_t_3;
  __pyx_t_3 = 0;

  /* "(tree fragment)":8
 *         raise __pyx_PickleError("Incompatible checksums (%s vs 0xd41d8cd = ())" % __pyx_checksum)
 *     __pyx_result = FSM.__new__(__pyx_type)
 *     if __pyx_state is not None:             # <<<<<<<<<<<<<<
 *         __pyx_unpickle_FSM__set_state(<FSM> __pyx_result, __pyx_state)
 *     return __pyx_result
 */
  __pyx_t_1 = (__pyx_v___pyx_state != Py_None);
  __pyx_t_6 = (__pyx_t_1 != 0);
  if (__pyx_t_6) {

    /* "(tree fragment)":9
 *     __pyx_result = FSM.__new__(__pyx_type)
 *     if __pyx_state is not None:
 *         __pyx_unpickle_FSM__set_state(<FSM> __pyx_result, __pyx_state)             # <<<<<<<<<<<<<<
 *     return __pyx_result
 * cdef __pyx_unpickle_FSM__set_state(FSM __pyx_result, tuple __pyx_state):
 */
    if (!(likely(PyTuple_CheckExact(__pyx_v___pyx_state))||((__pyx_v___pyx_state) == Py_None)||(PyErr_Format(PyExc_TypeError, "Expected %.16s, got %.200s", "tuple", Py_TYPE(__pyx_v___pyx_state)->tp_name), 0))) __PYX_ERR(1, 9, __pyx_L1_error)
    __pyx_t_3 = __pyx_f_8grapheme_6cython_6finder___pyx_unpickle_FSM__set_state(((struct __pyx_obj_8grapheme_6cython_6finder_FSM *)__pyx_v___pyx_result), ((PyObject*)__pyx_v___pyx_state)); if (unlikely(!__pyx_t_3)) __PYX_ERR(1, 9, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_3);
    __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;

    /* "(tree fragment)":8
 *         raise __pyx_PickleError("Incompatible checksums (%s vs 0xd41d8cd = ())" % __pyx_checksum)
 *     __pyx_result = FSM.__new__(__pyx_type)
 *     if __pyx_state is not None:             # <<<<<<<<<<<<<<
 *         __pyx_unpickle_FSM__set_state(<FSM> __pyx_result, __pyx_state)
 *     return __pyx_result
 */
  }

  /* "(tree fragment)":10
 *     if __pyx_state is not None:
 *         __pyx_unpickle_FSM__set_state(<FSM> __pyx_result, __pyx_state)
 *     return __pyx_result             # <<<<<<<<<<<<<<
 * cdef __pyx_unpickle_FSM__set_state(FSM __pyx_result, tuple __pyx_state):
 *     if len(__pyx_state) > 0 and hasattr(__pyx_result, '__dict__'):
 */
  __Pyx_XDECREF(__pyx_r);
  __Pyx_INCREF(__pyx_v___pyx_result);
  __pyx_r = __pyx_v___pyx_result;
  goto __pyx_L0;

  /* "(tree fragment)":1
 * def __pyx_unpickle_FSM(__pyx_type, long __pyx_checksum, __pyx_state):             # <<<<<<<<<<<<<<
 *     cdef object __pyx_PickleError
 *     cdef object __pyx_result
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_3);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_XDECREF(__pyx_t_5);
  __Pyx_AddTraceback("grapheme.cython.finder.__pyx_unpickle_FSM", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = NULL;
  __pyx_L0:;
  __Pyx_XDECREF(__pyx_v___pyx_PickleError);
  __Pyx_XDECREF(__pyx_v___pyx_result);
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "(tree fragment)":11
 *         __pyx_unpickle_FSM__set_state(<FSM> __pyx_result, __pyx_state)
 *     return __pyx_result
 * cdef __pyx_unpickle_FSM__set_state(FSM __pyx_result, tuple __pyx_state):             # <<<<<<<<<<<<<<
 *     if len(__pyx_state) > 0 and hasattr(__pyx_result, '__dict__'):
 *         __pyx_result.__dict__.update(__pyx_state[0])
 */

static PyObject *__pyx_f_8grapheme_6cython_6finder___pyx_unpickle_FSM__set_state(struct __pyx_obj_8grapheme_6cython_6finder_FSM *__pyx_v___pyx_result, PyObject *__pyx_v___pyx_state) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  Py_ssize_t __pyx_t_2;
  int __pyx_t_3;
  int __pyx_t_4;
  PyObject *__pyx_t_5 = NULL;
  PyObject *__pyx_t_6 = NULL;
  PyObject *__pyx_t_7 = NULL;
  PyObject *__pyx_t_8 = NULL;
  __Pyx_RefNannySetupContext("__pyx_unpickle_FSM__set_state", 0);

  /* "(tree fragment)":12
 *     return __pyx_result
 * cdef __pyx_unpickle_FSM__set_state(FSM __pyx_result, tuple __pyx_state):
 *     if len(__pyx_state) > 0 and hasattr(__pyx_result, '__dict__'):             # <<<<<<<<<<<<<<
 *         __pyx_result.__dict__.update(__pyx_state[0])
 */
  if (unlikely(__pyx_v___pyx_state == Py_None)) {
    PyErr_SetString(PyExc_TypeError, "object of type 'NoneType' has no len()");
    __PYX_ERR(1, 12, __pyx_L1_error)
  }
  __pyx_t_2 = PyTuple_GET_SIZE(__pyx_v___pyx_state); if (unlikely(__pyx_t_2 == ((Py_ssize_t)-1))) __PYX_ERR(1, 12, __pyx_L1_error)
  __pyx_t_3 = ((__pyx_t_2 > 0) != 0);
  if (__pyx_t_3) {
  } else {
    __pyx_t_1 = __pyx_t_3;
    goto __pyx_L4_bool_binop_done;
  }
  __pyx_t_3 = __Pyx_HasAttr(((PyObject *)__pyx_v___pyx_result), __pyx_n_s_dict); if (unlikely(__pyx_t_3 == ((int)-1))) __PYX_ERR(1, 12, __pyx_L1_error)
  __pyx_t_4 = (__pyx_t_3 != 0);
  __pyx_t_1 = __pyx_t_4;
  __pyx_L4_bool_binop_done:;
  if (__pyx_t_1) {

    /* "(tree fragment)":13
 * cdef __pyx_unpickle_FSM__set_state(FSM __pyx_result, tuple __pyx_state):
 *     if len(__pyx_state) > 0 and hasattr(__pyx_result, '__dict__'):
 *         __pyx_result.__dict__.update(__pyx_state[0])             # <<<<<<<<<<<<<<
 */
    __pyx_t_6 = __Pyx_PyObject_GetAttrStr(((PyObject *)__pyx_v___pyx_result), __pyx_n_s_dict); if (unlikely(!__pyx_t_6)) __PYX_ERR(1, 13, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_6);
    __pyx_t_7 = __Pyx_PyObject_GetAttrStr(__pyx_t_6, __pyx_n_s_update); if (unlikely(!__pyx_t_7)) __PYX_ERR(1, 13, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_7);
    __Pyx_DECREF(__pyx_t_6); __pyx_t_6 = 0;
    if (unlikely(__pyx_v___pyx_state == Py_None)) {
      PyErr_SetString(PyExc_TypeError, "'NoneType' object is not subscriptable");
      __PYX_ERR(1, 13, __pyx_L1_error)
    }
    __pyx_t_6 = __Pyx_GetItemInt_Tuple(__pyx_v___pyx_state, 0, long, 1, __Pyx_PyInt_From_long, 0, 0, 1); if (unlikely(!__pyx_t_6)) __PYX_ERR(1, 13, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_6);
    __pyx_t_8 = NULL;
    if (CYTHON_UNPACK_METHODS && likely(PyMethod_Check(__pyx_t_7))) {
      __pyx_t_8 = PyMethod_GET_SELF(__pyx_t_7);
      if (likely(__pyx_t_8)) {
        PyObject* function = PyMethod_GET_FUNCTION(__pyx_t_7);
        __Pyx_INCREF(__pyx_t_8);
        __Pyx_INCREF(function);
        __Pyx_DECREF_SET(__pyx_t_7, function);
      }
    }
    __pyx_t_5 = (__pyx_t_8) ? __Pyx_PyObject_Call2Args(__pyx_t_7, __pyx_t_8, __pyx_t_6) : __Pyx_PyObject_CallOneArg(__pyx_t_7, __pyx_t_6);
    __Pyx_XDECREF(__pyx_t_8); __pyx_t_8 = 0;
    __Pyx_DECREF(__pyx_t_6); __pyx_t_6 = 0;
    if (unlikely(!__pyx_t_5)) __PYX_ERR(1, 13, __pyx_L1_error)
    __Pyx_GOTREF(__pyx_t_5);
    __Pyx_DECREF(__pyx_t_7); __pyx_t_7 = 0;
    __Pyx_DECREF(__pyx_t_5); __pyx_t_5 = 0;

    /* "(tree fragment)":12
 *     return __pyx_result
 * cdef __pyx_unpickle_FSM__set_state(FSM __pyx_result, tuple __pyx_state):
 *     if len(__pyx_state) > 0 and hasattr(__pyx_result, '__dict__'):             # <<<<<<<<<<<<<<
 *         __pyx_result.__dict__.update(__pyx_state[0])
 */
  }

  /* "(tree fragment)":11
 *         __pyx_unpickle_FSM__set_state(<FSM> __pyx_result, __pyx_state)
 *     return __pyx_result
 * cdef __pyx_unpickle_FSM__set_state(FSM __pyx_result, tuple __pyx_state):             # <<<<<<<<<<<<<<
 *     if len(__pyx_state) > 0 and hasattr(__pyx_result, '__dict__'):
 *         __pyx_result.__dict__.update(__pyx_state[0])
 */

  /* function exit code */
  __pyx_r = Py_None; __Pyx_INCREF(Py_None);
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_5);
  __Pyx_XDECREF(__pyx_t_6);
  __Pyx_XDECREF(__pyx_t_7);
  __Pyx_XDECREF(__pyx_t_8);
  __Pyx_AddTraceback("grapheme.cython.finder.__pyx_unpickle_FSM__set_state", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = 0;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

static PyObject *__pyx_tp_new_8grapheme_6cython_6finder_FSM(PyTypeObject *t, CYTHON_UNUSED PyObject *a, CYTHON_UNUSED PyObject *k) {
  PyObject *o;
  if (likely((t->tp_flags & Py_TPFLAGS_IS_ABSTRACT) == 0)) {
    o = (*t->tp_alloc)(t, 0);
  } else {
    o = (PyObject *) PyBaseObject_Type.tp_new(t, __pyx_empty_tuple, 0);
  }
  if (unlikely(!o)) return 0;
  return o;
}

static void __pyx_tp_dealloc_8grapheme_6cython_6finder_FSM(PyObject *o) {
  #if CYTHON_USE_TP_FINALIZE
  if (unlikely(PyType_HasFeature(Py_TYPE(o), Py_TPFLAGS_HAVE_FINALIZE) && Py_TYPE(o)->tp_finalize) && (!PyType_IS_GC(Py_TYPE(o)) || !_PyGC_FINALIZED(o))) {
    if (PyObject_CallFinalizerFromDealloc(o)) return;
  }
  #endif
  (*Py_TYPE(o)->tp_free)(o);
}

static PyMethodDef __pyx_methods_8grapheme_6cython_6finder_FSM[] = {
  {"default", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_3FSM_1default, METH_O, 0},
  {"default_next_state", (PyCFunction)(void*)(PyCFunctionWithKeywords)__pyx_pw_8grapheme_6cython_6finder_3FSM_3default_next_state, METH_VARARGS|METH_KEYWORDS, 0},
  {"cr", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_3FSM_5cr, METH_O, 0},
  {"lf_or_control", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_3FSM_7lf_or_control, METH_O, 0},
  {"prepend", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_3FSM_9prepend, METH_O, 0},
  {"hangul_l", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_3FSM_11hangul_l, METH_O, 0},
  {"hangul_lv_or_v", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_3FSM_13hangul_lv_or_v, METH_O, 0},
  {"hangul_lvt_or_t", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_3FSM_15hangul_lvt_or_t, METH_O, 0},
  {"emoji", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_3FSM_17emoji, METH_O, 0},
  {"emoji_zjw", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_3FSM_19emoji_zjw, METH_O, 0},
  {"ri", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_3FSM_21ri, METH_O, 0},
  {"__reduce_cython__", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_3FSM_23__reduce_cython__, METH_NOARGS, 0},
  {"__setstate_cython__", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_3FSM_25__setstate_cython__, METH_O, 0},
  {0, 0, 0, 0}
};

static PyTypeObject __pyx_type_8grapheme_6cython_6finder_FSM = {
  PyVarObject_HEAD_INIT(0, 0)
  "grapheme.cython.finder.FSM", /*tp_name*/
  sizeof(struct __pyx_obj_8grapheme_6cython_6finder_FSM), /*tp_basicsize*/
  0, /*tp_itemsize*/
  __pyx_tp_dealloc_8grapheme_6cython_6finder_FSM, /*tp_dealloc*/
  0, /*tp_print*/
  0, /*tp_getattr*/
  0, /*tp_setattr*/
  #if PY_MAJOR_VERSION < 3
  0, /*tp_compare*/
  #endif
  #if PY_MAJOR_VERSION >= 3
  0, /*tp_as_async*/
  #endif
  0, /*tp_repr*/
  0, /*tp_as_number*/
  0, /*tp_as_sequence*/
  0, /*tp_as_mapping*/
  0, /*tp_hash*/
  0, /*tp_call*/
  0, /*tp_str*/
  0, /*tp_getattro*/
  0, /*tp_setattro*/
  0, /*tp_as_buffer*/
  Py_TPFLAGS_DEFAULT|Py_TPFLAGS_HAVE_VERSION_TAG|Py_TPFLAGS_CHECKTYPES|Py_TPFLAGS_HAVE_NEWBUFFER|Py_TPFLAGS_BASETYPE, /*tp_flags*/
  0, /*tp_doc*/
  0, /*tp_traverse*/
  0, /*tp_clear*/
  0, /*tp_richcompare*/
  0, /*tp_weaklistoffset*/
  0, /*tp_iter*/
  0, /*tp_iternext*/
  __pyx_methods_8grapheme_6cython_6finder_FSM, /*tp_methods*/
  0, /*tp_members*/
  0, /*tp_getset*/
  0, /*tp_base*/
  0, /*tp_dict*/
  0, /*tp_descr_get*/
  0, /*tp_descr_set*/
  0, /*tp_dictoffset*/
  0, /*tp_init*/
  0, /*tp_alloc*/
  __pyx_tp_new_8grapheme_6cython_6finder_FSM, /*tp_new*/
  0, /*tp_free*/
  0, /*tp_is_gc*/
  0, /*tp_bases*/
  0, /*tp_mro*/
  0, /*tp_cache*/
  0, /*tp_subclasses*/
  0, /*tp_weaklist*/
  0, /*tp_del*/
  0, /*tp_version_tag*/
  #if PY_VERSION_HEX >= 0x030400a1
  0, /*tp_finalize*/
  #endif
};
static struct __pyx_vtabstruct_8grapheme_6cython_6finder_GraphemeIterator __pyx_vtable_8grapheme_6cython_6finder_GraphemeIterator;

static PyObject *__pyx_tp_new_8grapheme_6cython_6finder_GraphemeIterator(PyTypeObject *t, PyObject *a, PyObject *k) {
  struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *p;
  PyObject *o;
  if (likely((t->tp_flags & Py_TPFLAGS_IS_ABSTRACT) == 0)) {
    o = (*t->tp_alloc)(t, 0);
  } else {
    o = (PyObject *) PyBaseObject_Type.tp_new(t, __pyx_empty_tuple, 0);
  }
  if (unlikely(!o)) return 0;
  p = ((struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *)o);
  p->__pyx_vtab = __pyx_vtabptr_8grapheme_6cython_6finder_GraphemeIterator;
  p->str_iter = Py_None; Py_INCREF(Py_None);
  p->buffer = ((PyObject*)Py_None); Py_INCREF(Py_None);
  p->state = Py_None; Py_INCREF(Py_None);
  if (unlikely(__pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_1__cinit__(o, a, k) < 0)) goto bad;
  return o;
  bad:
  Py_DECREF(o); o = 0;
  return NULL;
}

static void __pyx_tp_dealloc_8grapheme_6cython_6finder_GraphemeIterator(PyObject *o) {
  struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *p = (struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *)o;
  #if CYTHON_USE_TP_FINALIZE
  if (unlikely(PyType_HasFeature(Py_TYPE(o), Py_TPFLAGS_HAVE_FINALIZE) && Py_TYPE(o)->tp_finalize) && !_PyGC_FINALIZED(o)) {
    if (PyObject_CallFinalizerFromDealloc(o)) return;
  }
  #endif
  PyObject_GC_UnTrack(o);
  Py_CLEAR(p->str_iter);
  Py_CLEAR(p->buffer);
  Py_CLEAR(p->state);
  (*Py_TYPE(o)->tp_free)(o);
}

static int __pyx_tp_traverse_8grapheme_6cython_6finder_GraphemeIterator(PyObject *o, visitproc v, void *a) {
  int e;
  struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *p = (struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *)o;
  if (p->str_iter) {
    e = (*v)(p->str_iter, a); if (e) return e;
  }
  if (p->state) {
    e = (*v)(p->state, a); if (e) return e;
  }
  return 0;
}

static int __pyx_tp_clear_8grapheme_6cython_6finder_GraphemeIterator(PyObject *o) {
  PyObject* tmp;
  struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *p = (struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *)o;
  tmp = ((PyObject*)p->str_iter);
  p->str_iter = Py_None; Py_INCREF(Py_None);
  Py_XDECREF(tmp);
  tmp = ((PyObject*)p->state);
  p->state = Py_None; Py_INCREF(Py_None);
  Py_XDECREF(tmp);
  return 0;
}

static PyObject *__pyx_specialmethod___pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_5__next__(PyObject *self, CYTHON_UNUSED PyObject *arg) {return __pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_5__next__(self);}

static PyMethodDef __pyx_methods_8grapheme_6cython_6finder_GraphemeIterator[] = {
  {"__next__", (PyCFunction)__pyx_specialmethod___pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_5__next__, METH_NOARGS|METH_COEXIST, 0},
  {"__reduce_cython__", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_7__reduce_cython__, METH_NOARGS, 0},
  {"__setstate_cython__", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_9__setstate_cython__, METH_O, 0},
  {0, 0, 0, 0}
};

static PyTypeObject __pyx_type_8grapheme_6cython_6finder_GraphemeIterator = {
  PyVarObject_HEAD_INIT(0, 0)
  "grapheme.cython.finder.GraphemeIterator", /*tp_name*/
  sizeof(struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator), /*tp_basicsize*/
  0, /*tp_itemsize*/
  __pyx_tp_dealloc_8grapheme_6cython_6finder_GraphemeIterator, /*tp_dealloc*/
  0, /*tp_print*/
  0, /*tp_getattr*/
  0, /*tp_setattr*/
  #if PY_MAJOR_VERSION < 3
  0, /*tp_compare*/
  #endif
  #if PY_MAJOR_VERSION >= 3
  0, /*tp_as_async*/
  #endif
  0, /*tp_repr*/
  0, /*tp_as_number*/
  0, /*tp_as_sequence*/
  0, /*tp_as_mapping*/
  0, /*tp_hash*/
  0, /*tp_call*/
  0, /*tp_str*/
  0, /*tp_getattro*/
  0, /*tp_setattro*/
  0, /*tp_as_buffer*/
  Py_TPFLAGS_DEFAULT|Py_TPFLAGS_HAVE_VERSION_TAG|Py_TPFLAGS_CHECKTYPES|Py_TPFLAGS_HAVE_NEWBUFFER|Py_TPFLAGS_BASETYPE|Py_TPFLAGS_HAVE_GC, /*tp_flags*/
  0, /*tp_doc*/
  __pyx_tp_traverse_8grapheme_6cython_6finder_GraphemeIterator, /*tp_traverse*/
  __pyx_tp_clear_8grapheme_6cython_6finder_GraphemeIterator, /*tp_clear*/
  0, /*tp_richcompare*/
  0, /*tp_weaklistoffset*/
  __pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_3__iter__, /*tp_iter*/
  __pyx_pw_8grapheme_6cython_6finder_16GraphemeIterator_5__next__, /*tp_iternext*/
  __pyx_methods_8grapheme_6cython_6finder_GraphemeIterator, /*tp_methods*/
  0, /*tp_members*/
  0, /*tp_getset*/
  0, /*tp_base*/
  0, /*tp_dict*/
  0, /*tp_descr_get*/
  0, /*tp_descr_set*/
  0, /*tp_dictoffset*/
  0, /*tp_init*/
  0, /*tp_alloc*/
  __pyx_tp_new_8grapheme_6cython_6finder_GraphemeIterator, /*tp_new*/
  0, /*tp_free*/
  0, /*tp_is_gc*/
  0, /*tp_bases*/
  0, /*tp_mro*/
  0, /*tp_cache*/
  0, /*tp_subclasses*/
  0, /*tp_weaklist*/
  0, /*tp_del*/
  0, /*tp_version_tag*/
  #if PY_VERSION_HEX >= 0x030400a1
  0, /*tp_finalize*/
  #endif
};

static PyMethodDef __pyx_methods[] = {
  {"c_length", (PyCFunction)__pyx_pw_8grapheme_6cython_6finder_5c_length, METH_O, 0},
  {0, 0, 0, 0}
};

#if PY_MAJOR_VERSION >= 3
#if CYTHON_PEP489_MULTI_PHASE_INIT
static PyObject* __pyx_pymod_create(PyObject *spec, PyModuleDef *def); /*proto*/
static int __pyx_pymod_exec_finder(PyObject* module); /*proto*/
static PyModuleDef_Slot __pyx_moduledef_slots[] = {
  {Py_mod_create, (void*)__pyx_pymod_create},
  {Py_mod_exec, (void*)__pyx_pymod_exec_finder},
  {0, NULL}
};
#endif

static struct PyModuleDef __pyx_moduledef = {
    PyModuleDef_HEAD_INIT,
    "finder",
    0, /* m_doc */
  #if CYTHON_PEP489_MULTI_PHASE_INIT
    0, /* m_size */
  #else
    -1, /* m_size */
  #endif
    __pyx_methods /* m_methods */,
  #if CYTHON_PEP489_MULTI_PHASE_INIT
    __pyx_moduledef_slots, /* m_slots */
  #else
    NULL, /* m_reload */
  #endif
    NULL, /* m_traverse */
    NULL, /* m_clear */
    NULL /* m_free */
};
#endif
#ifndef CYTHON_SMALL_CODE
#if defined(__clang__)
    #define CYTHON_SMALL_CODE
#elif defined(__GNUC__) && (__GNUC__ > 4 || (__GNUC__ == 4 && __GNUC_MINOR__ >= 3))
    #define CYTHON_SMALL_CODE __attribute__((cold))
#else
    #define CYTHON_SMALL_CODE
#endif
#endif

static __Pyx_StringTabEntry __pyx_string_tab[] = {
  {&__pyx_kp_u_, __pyx_k_, sizeof(__pyx_k_), 0, 1, 0, 0},
  {&__pyx_n_s_BreakPossibility, __pyx_k_BreakPossibility, sizeof(__pyx_k_BreakPossibility), 0, 0, 1, 1},
  {&__pyx_n_s_CERTAIN, __pyx_k_CERTAIN, sizeof(__pyx_k_CERTAIN), 0, 0, 1, 1},
  {&__pyx_n_s_CONTROL, __pyx_k_CONTROL, sizeof(__pyx_k_CONTROL), 0, 0, 1, 1},
  {&__pyx_n_s_CR, __pyx_k_CR, sizeof(__pyx_k_CR), 0, 0, 1, 1},
  {&__pyx_n_s_EXTEND, __pyx_k_EXTEND, sizeof(__pyx_k_EXTEND), 0, 0, 1, 1},
  {&__pyx_n_s_EXTENDED_PICTOGRAPHIC, __pyx_k_EXTENDED_PICTOGRAPHIC, sizeof(__pyx_k_EXTENDED_PICTOGRAPHIC), 0, 0, 1, 1},
  {&__pyx_n_s_Enum, __pyx_k_Enum, sizeof(__pyx_k_Enum), 0, 0, 1, 1},
  {&__pyx_n_s_FSM, __pyx_k_FSM, sizeof(__pyx_k_FSM), 0, 0, 1, 1},
  {&__pyx_n_s_G, __pyx_k_G, sizeof(__pyx_k_G), 0, 0, 1, 1},
  {&__pyx_n_s_GraphemeIterator, __pyx_k_GraphemeIterator, sizeof(__pyx_k_GraphemeIterator), 0, 0, 1, 1},
  {&__pyx_n_s_GraphemePropertyGroup, __pyx_k_GraphemePropertyGroup, sizeof(__pyx_k_GraphemePropertyGroup), 0, 0, 1, 1},
  {&__pyx_kp_s_Incompatible_checksums_s_vs_0xd4, __pyx_k_Incompatible_checksums_s_vs_0xd4, sizeof(__pyx_k_Incompatible_checksums_s_vs_0xd4), 0, 0, 1, 0},
  {&__pyx_n_s_L, __pyx_k_L, sizeof(__pyx_k_L), 0, 0, 1, 1},
  {&__pyx_n_s_LF, __pyx_k_LF, sizeof(__pyx_k_LF), 0, 0, 1, 1},
  {&__pyx_n_s_LV, __pyx_k_LV, sizeof(__pyx_k_LV), 0, 0, 1, 1},
  {&__pyx_n_s_LVT, __pyx_k_LVT, sizeof(__pyx_k_LVT), 0, 0, 1, 1},
  {&__pyx_n_s_NO_BREAK, __pyx_k_NO_BREAK, sizeof(__pyx_k_NO_BREAK), 0, 0, 1, 1},
  {&__pyx_n_s_OTHER, __pyx_k_OTHER, sizeof(__pyx_k_OTHER), 0, 0, 1, 1},
  {&__pyx_n_s_POSSIBLE, __pyx_k_POSSIBLE, sizeof(__pyx_k_POSSIBLE), 0, 0, 1, 1},
  {&__pyx_n_s_PREPEND, __pyx_k_PREPEND, sizeof(__pyx_k_PREPEND), 0, 0, 1, 1},
  {&__pyx_n_s_PickleError, __pyx_k_PickleError, sizeof(__pyx_k_PickleError), 0, 0, 1, 1},
  {&__pyx_n_s_REGIONAL_INDICATOR, __pyx_k_REGIONAL_INDICATOR, sizeof(__pyx_k_REGIONAL_INDICATOR), 0, 0, 1, 1},
  {&__pyx_n_s_SPACING_MARK, __pyx_k_SPACING_MARK, sizeof(__pyx_k_SPACING_MARK), 0, 0, 1, 1},
  {&__pyx_n_s_StopIteration, __pyx_k_StopIteration, sizeof(__pyx_k_StopIteration), 0, 0, 1, 1},
  {&__pyx_n_s_T, __pyx_k_T, sizeof(__pyx_k_T), 0, 0, 1, 1},
  {&__pyx_n_s_TypeError, __pyx_k_TypeError, sizeof(__pyx_k_TypeError), 0, 0, 1, 1},
  {&__pyx_n_s_V, __pyx_k_V, sizeof(__pyx_k_V), 0, 0, 1, 1},
  {&__pyx_n_s_ZWJ, __pyx_k_ZWJ, sizeof(__pyx_k_ZWJ), 0, 0, 1, 1},
  {&__pyx_n_s_a, __pyx_k_a, sizeof(__pyx_k_a), 0, 0, 1, 1},
  {&__pyx_n_s_b, __pyx_k_b, sizeof(__pyx_k_b), 0, 0, 1, 1},
  {&__pyx_n_u_certain, __pyx_k_certain, sizeof(__pyx_k_certain), 0, 1, 0, 1},
  {&__pyx_n_s_cline_in_traceback, __pyx_k_cline_in_traceback, sizeof(__pyx_k_cline_in_traceback), 0, 0, 1, 1},
  {&__pyx_n_s_cr, __pyx_k_cr, sizeof(__pyx_k_cr), 0, 0, 1, 1},
  {&__pyx_n_s_cur, __pyx_k_cur, sizeof(__pyx_k_cur), 0, 0, 1, 1},
  {&__pyx_n_s_default, __pyx_k_default, sizeof(__pyx_k_default), 0, 0, 1, 1},
  {&__pyx_n_s_default_next_state, __pyx_k_default_next_state, sizeof(__pyx_k_default_next_state), 0, 0, 1, 1},
  {&__pyx_n_s_dict, __pyx_k_dict, sizeof(__pyx_k_dict), 0, 0, 1, 1},
  {&__pyx_n_s_doc, __pyx_k_doc, sizeof(__pyx_k_doc), 0, 0, 1, 1},
  {&__pyx_n_s_emoji, __pyx_k_emoji, sizeof(__pyx_k_emoji), 0, 0, 1, 1},
  {&__pyx_n_s_emoji_zjw, __pyx_k_emoji_zjw, sizeof(__pyx_k_emoji_zjw), 0, 0, 1, 1},
  {&__pyx_n_s_enum, __pyx_k_enum, sizeof(__pyx_k_enum), 0, 0, 1, 1},
  {&__pyx_n_s_get_break_possibility, __pyx_k_get_break_possibility, sizeof(__pyx_k_get_break_possibility), 0, 0, 1, 1},
  {&__pyx_n_s_get_last_certain_break_index, __pyx_k_get_last_certain_break_index, sizeof(__pyx_k_get_last_certain_break_index), 0, 0, 1, 1},
  {&__pyx_n_s_getstate, __pyx_k_getstate, sizeof(__pyx_k_getstate), 0, 0, 1, 1},
  {&__pyx_n_s_grapheme_cython_finder, __pyx_k_grapheme_cython_finder, sizeof(__pyx_k_grapheme_cython_finder), 0, 0, 1, 1},
  {&__pyx_kp_s_grapheme_cython_finder_pyx, __pyx_k_grapheme_cython_finder_pyx, sizeof(__pyx_k_grapheme_cython_finder_pyx), 0, 0, 1, 0},
  {&__pyx_n_s_grapheme_cython_grapheme_propert, __pyx_k_grapheme_cython_grapheme_propert, sizeof(__pyx_k_grapheme_cython_grapheme_propert), 0, 0, 1, 1},
  {&__pyx_n_s_hangul_l, __pyx_k_hangul_l, sizeof(__pyx_k_hangul_l), 0, 0, 1, 1},
  {&__pyx_n_s_hangul_lv_or_v, __pyx_k_hangul_lv_or_v, sizeof(__pyx_k_hangul_lv_or_v), 0, 0, 1, 1},
  {&__pyx_n_s_hangul_lvt_or_t, __pyx_k_hangul_lvt_or_t, sizeof(__pyx_k_hangul_lvt_or_t), 0, 0, 1, 1},
  {&__pyx_n_s_import, __pyx_k_import, sizeof(__pyx_k_import), 0, 0, 1, 1},
  {&__pyx_n_s_index, __pyx_k_index, sizeof(__pyx_k_index), 0, 0, 1, 1},
  {&__pyx_n_s_lf_or_control, __pyx_k_lf_or_control, sizeof(__pyx_k_lf_or_control), 0, 0, 1, 1},
  {&__pyx_n_s_main, __pyx_k_main, sizeof(__pyx_k_main), 0, 0, 1, 1},
  {&__pyx_n_s_metaclass, __pyx_k_metaclass, sizeof(__pyx_k_metaclass), 0, 0, 1, 1},
  {&__pyx_n_s_module, __pyx_k_module, sizeof(__pyx_k_module), 0, 0, 1, 1},
  {&__pyx_n_s_n, __pyx_k_n, sizeof(__pyx_k_n), 0, 0, 1, 1},
  {&__pyx_n_s_name, __pyx_k_name, sizeof(__pyx_k_name), 0, 0, 1, 1},
  {&__pyx_n_s_new, __pyx_k_new, sizeof(__pyx_k_new), 0, 0, 1, 1},
  {&__pyx_kp_s_no_default___reduce___due_to_non, __pyx_k_no_default___reduce___due_to_non, sizeof(__pyx_k_no_default___reduce___due_to_non), 0, 0, 1, 0},
  {&__pyx_n_u_nobreak, __pyx_k_nobreak, sizeof(__pyx_k_nobreak), 0, 1, 0, 1},
  {&__pyx_n_s_pickle, __pyx_k_pickle, sizeof(__pyx_k_pickle), 0, 0, 1, 1},
  {&__pyx_n_u_possible, __pyx_k_possible, sizeof(__pyx_k_possible), 0, 1, 0, 1},
  {&__pyx_n_s_prepare, __pyx_k_prepare, sizeof(__pyx_k_prepare), 0, 0, 1, 1},
  {&__pyx_n_s_prepend, __pyx_k_prepend, sizeof(__pyx_k_prepend), 0, 0, 1, 1},
  {&__pyx_n_s_prev, __pyx_k_prev, sizeof(__pyx_k_prev), 0, 0, 1, 1},
  {&__pyx_n_s_pyx_PickleError, __pyx_k_pyx_PickleError, sizeof(__pyx_k_pyx_PickleError), 0, 0, 1, 1},
  {&__pyx_n_s_pyx_checksum, __pyx_k_pyx_checksum, sizeof(__pyx_k_pyx_checksum), 0, 0, 1, 1},
  {&__pyx_n_s_pyx_result, __pyx_k_pyx_result, sizeof(__pyx_k_pyx_result), 0, 0, 1, 1},
  {&__pyx_n_s_pyx_state, __pyx_k_pyx_state, sizeof(__pyx_k_pyx_state), 0, 0, 1, 1},
  {&__pyx_n_s_pyx_type, __pyx_k_pyx_type, sizeof(__pyx_k_pyx_type), 0, 0, 1, 1},
  {&__pyx_n_s_pyx_unpickle_FSM, __pyx_k_pyx_unpickle_FSM, sizeof(__pyx_k_pyx_unpickle_FSM), 0, 0, 1, 1},
  {&__pyx_n_s_pyx_vtable, __pyx_k_pyx_vtable, sizeof(__pyx_k_pyx_vtable), 0, 0, 1, 1},
  {&__pyx_n_s_qualname, __pyx_k_qualname, sizeof(__pyx_k_qualname), 0, 0, 1, 1},
  {&__pyx_n_s_reduce, __pyx_k_reduce, sizeof(__pyx_k_reduce), 0, 0, 1, 1},
  {&__pyx_n_s_reduce_cython, __pyx_k_reduce_cython, sizeof(__pyx_k_reduce_cython), 0, 0, 1, 1},
  {&__pyx_n_s_reduce_ex, __pyx_k_reduce_ex, sizeof(__pyx_k_reduce_ex), 0, 0, 1, 1},
  {&__pyx_n_s_ri, __pyx_k_ri, sizeof(__pyx_k_ri), 0, 0, 1, 1},
  {&__pyx_n_s_setstate, __pyx_k_setstate, sizeof(__pyx_k_setstate), 0, 0, 1, 1},
  {&__pyx_n_s_setstate_cython, __pyx_k_setstate_cython, sizeof(__pyx_k_setstate_cython), 0, 0, 1, 1},
  {&__pyx_n_s_should_break, __pyx_k_should_break, sizeof(__pyx_k_should_break), 0, 0, 1, 1},
  {&__pyx_n_s_string, __pyx_k_string, sizeof(__pyx_k_string), 0, 0, 1, 1},
  {&__pyx_kp_s_stringsource, __pyx_k_stringsource, sizeof(__pyx_k_stringsource), 0, 0, 1, 0},
  {&__pyx_n_s_test, __pyx_k_test, sizeof(__pyx_k_test), 0, 0, 1, 1},
  {&__pyx_n_s_update, __pyx_k_update, sizeof(__pyx_k_update), 0, 0, 1, 1},
  {0, 0, 0, 0, 0, 0, 0}
};
static CYTHON_SMALL_CODE int __Pyx_InitCachedBuiltins(void) {
  __pyx_builtin_StopIteration = __Pyx_GetBuiltinName(__pyx_n_s_StopIteration); if (!__pyx_builtin_StopIteration) __PYX_ERR(0, 336, __pyx_L1_error)
  __pyx_builtin_TypeError = __Pyx_GetBuiltinName(__pyx_n_s_TypeError); if (!__pyx_builtin_TypeError) __PYX_ERR(1, 2, __pyx_L1_error)
  return 0;
  __pyx_L1_error:;
  return -1;
}

static CYTHON_SMALL_CODE int __Pyx_InitCachedConstants(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_InitCachedConstants", 0);

  /* "(tree fragment)":2
 * def __reduce_cython__(self):
 *     raise TypeError("no default __reduce__ due to non-trivial __cinit__")             # <<<<<<<<<<<<<<
 * def __setstate_cython__(self, __pyx_state):
 *     raise TypeError("no default __reduce__ due to non-trivial __cinit__")
 */
  __pyx_tuple__2 = PyTuple_Pack(1, __pyx_kp_s_no_default___reduce___due_to_non); if (unlikely(!__pyx_tuple__2)) __PYX_ERR(1, 2, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__2);
  __Pyx_GIVEREF(__pyx_tuple__2);

  /* "(tree fragment)":4
 *     raise TypeError("no default __reduce__ due to non-trivial __cinit__")
 * def __setstate_cython__(self, __pyx_state):
 *     raise TypeError("no default __reduce__ due to non-trivial __cinit__")             # <<<<<<<<<<<<<<
 */
  __pyx_tuple__3 = PyTuple_Pack(1, __pyx_kp_s_no_default___reduce___due_to_non); if (unlikely(!__pyx_tuple__3)) __PYX_ERR(1, 4, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__3);
  __Pyx_GIVEREF(__pyx_tuple__3);

  /* "grapheme/cython/finder.pyx":248
 * 
 * 
 * def get_break_possibility(a, b):             # <<<<<<<<<<<<<<
 *     # Probably most common, included as short circuit before checking all else
 *     if a == G.OTHER and b == G.OTHER:
 */
  __pyx_tuple__4 = PyTuple_Pack(2, __pyx_n_s_a, __pyx_n_s_b); if (unlikely(!__pyx_tuple__4)) __PYX_ERR(0, 248, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__4);
  __Pyx_GIVEREF(__pyx_tuple__4);
  __pyx_codeobj__5 = (PyObject*)__Pyx_PyCode_New(2, 0, 2, 0, CO_OPTIMIZED|CO_NEWLOCALS, __pyx_empty_bytes, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_tuple__4, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_kp_s_grapheme_cython_finder_pyx, __pyx_n_s_get_break_possibility, 248, __pyx_empty_bytes); if (unlikely(!__pyx_codeobj__5)) __PYX_ERR(0, 248, __pyx_L1_error)

  /* "grapheme/cython/finder.pyx":297
 * 
 * 
 * def get_last_certain_break_index(string, index):             # <<<<<<<<<<<<<<
 *     if index >= len(string):
 *         return len(string)
 */
  __pyx_tuple__6 = PyTuple_Pack(4, __pyx_n_s_string, __pyx_n_s_index, __pyx_n_s_prev, __pyx_n_s_cur); if (unlikely(!__pyx_tuple__6)) __PYX_ERR(0, 297, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__6);
  __Pyx_GIVEREF(__pyx_tuple__6);
  __pyx_codeobj__7 = (PyObject*)__Pyx_PyCode_New(2, 0, 4, 0, CO_OPTIMIZED|CO_NEWLOCALS, __pyx_empty_bytes, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_tuple__6, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_kp_s_grapheme_cython_finder_pyx, __pyx_n_s_get_last_certain_break_index, 297, __pyx_empty_bytes); if (unlikely(!__pyx_codeobj__7)) __PYX_ERR(0, 297, __pyx_L1_error)

  /* "(tree fragment)":1
 * def __pyx_unpickle_FSM(__pyx_type, long __pyx_checksum, __pyx_state):             # <<<<<<<<<<<<<<
 *     cdef object __pyx_PickleError
 *     cdef object __pyx_result
 */
  __pyx_tuple__8 = PyTuple_Pack(5, __pyx_n_s_pyx_type, __pyx_n_s_pyx_checksum, __pyx_n_s_pyx_state, __pyx_n_s_pyx_PickleError, __pyx_n_s_pyx_result); if (unlikely(!__pyx_tuple__8)) __PYX_ERR(1, 1, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__8);
  __Pyx_GIVEREF(__pyx_tuple__8);
  __pyx_codeobj__9 = (PyObject*)__Pyx_PyCode_New(3, 0, 5, 0, CO_OPTIMIZED|CO_NEWLOCALS, __pyx_empty_bytes, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_tuple__8, __pyx_empty_tuple, __pyx_empty_tuple, __pyx_kp_s_stringsource, __pyx_n_s_pyx_unpickle_FSM, 1, __pyx_empty_bytes); if (unlikely(!__pyx_codeobj__9)) __PYX_ERR(1, 1, __pyx_L1_error)
  __Pyx_RefNannyFinishContext();
  return 0;
  __pyx_L1_error:;
  __Pyx_RefNannyFinishContext();
  return -1;
}

static CYTHON_SMALL_CODE int __Pyx_InitGlobals(void) {
  if (__Pyx_InitStrings(__pyx_string_tab) < 0) __PYX_ERR(0, 1, __pyx_L1_error);
  __pyx_int_0 = PyInt_FromLong(0); if (unlikely(!__pyx_int_0)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_1 = PyInt_FromLong(1); if (unlikely(!__pyx_int_1)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_222419149 = PyInt_FromLong(222419149L); if (unlikely(!__pyx_int_222419149)) __PYX_ERR(0, 1, __pyx_L1_error)
  return 0;
  __pyx_L1_error:;
  return -1;
}

static CYTHON_SMALL_CODE int __Pyx_modinit_global_init_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_variable_export_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_function_export_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_type_init_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_type_import_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_variable_import_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_function_import_code(void); /*proto*/

static int __Pyx_modinit_global_init_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_global_init_code", 0);
  /*--- Global init code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_variable_export_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_variable_export_code", 0);
  /*--- Variable export code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_function_export_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_function_export_code", 0);
  /*--- Function export code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_type_init_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_type_init_code", 0);
  /*--- Type init code ---*/
  if (PyType_Ready(&__pyx_type_8grapheme_6cython_6finder_FSM) < 0) __PYX_ERR(0, 136, __pyx_L1_error)
  __pyx_type_8grapheme_6cython_6finder_FSM.tp_print = 0;
  if ((CYTHON_USE_TYPE_SLOTS && CYTHON_USE_PYTYPE_LOOKUP) && likely(!__pyx_type_8grapheme_6cython_6finder_FSM.tp_dictoffset && __pyx_type_8grapheme_6cython_6finder_FSM.tp_getattro == PyObject_GenericGetAttr)) {
    __pyx_type_8grapheme_6cython_6finder_FSM.tp_getattro = __Pyx_PyObject_GenericGetAttr;
  }
  if (PyObject_SetAttr(__pyx_m, __pyx_n_s_FSM, (PyObject *)&__pyx_type_8grapheme_6cython_6finder_FSM) < 0) __PYX_ERR(0, 136, __pyx_L1_error)
  if (__Pyx_setup_reduce((PyObject*)&__pyx_type_8grapheme_6cython_6finder_FSM) < 0) __PYX_ERR(0, 136, __pyx_L1_error)
  __pyx_ptype_8grapheme_6cython_6finder_FSM = &__pyx_type_8grapheme_6cython_6finder_FSM;
  __pyx_vtabptr_8grapheme_6cython_6finder_GraphemeIterator = &__pyx_vtable_8grapheme_6cython_6finder_GraphemeIterator;
  __pyx_vtable_8grapheme_6cython_6finder_GraphemeIterator._break = (PyObject *(*)(struct __pyx_obj_8grapheme_6cython_6finder_GraphemeIterator *, PyObject *))__pyx_f_8grapheme_6cython_6finder_16GraphemeIterator__break;
  if (PyType_Ready(&__pyx_type_8grapheme_6cython_6finder_GraphemeIterator) < 0) __PYX_ERR(0, 327, __pyx_L1_error)
  __pyx_type_8grapheme_6cython_6finder_GraphemeIterator.tp_print = 0;
  if ((CYTHON_USE_TYPE_SLOTS && CYTHON_USE_PYTYPE_LOOKUP) && likely(!__pyx_type_8grapheme_6cython_6finder_GraphemeIterator.tp_dictoffset && __pyx_type_8grapheme_6cython_6finder_GraphemeIterator.tp_getattro == PyObject_GenericGetAttr)) {
    __pyx_type_8grapheme_6cython_6finder_GraphemeIterator.tp_getattro = __Pyx_PyObject_GenericGetAttr;
  }
  if (__Pyx_SetVtable(__pyx_type_8grapheme_6cython_6finder_GraphemeIterator.tp_dict, __pyx_vtabptr_8grapheme_6cython_6finder_GraphemeIterator) < 0) __PYX_ERR(0, 327, __pyx_L1_error)
  if (PyObject_SetAttr(__pyx_m, __pyx_n_s_GraphemeIterator, (PyObject *)&__pyx_type_8grapheme_6cython_6finder_GraphemeIterator) < 0) __PYX_ERR(0, 327, __pyx_L1_error)
  if (__Pyx_setup_reduce((PyObject*)&__pyx_type_8grapheme_6cython_6finder_GraphemeIterator) < 0) __PYX_ERR(0, 327, __pyx_L1_error)
  __pyx_ptype_8grapheme_6cython_6finder_GraphemeIterator = &__pyx_type_8grapheme_6cython_6finder_GraphemeIterator;
  __Pyx_RefNannyFinishContext();
  return 0;
  __pyx_L1_error:;
  __Pyx_RefNannyFinishContext();
  return -1;
}

static int __Pyx_modinit_type_import_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_type_import_code", 0);
  /*--- Type import code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_variable_import_code(void) {
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  __Pyx_RefNannySetupContext("__Pyx_modinit_variable_import_code", 0);
  /*--- Variable import code ---*/
  __pyx_t_1 = PyImport_ImportModule("grapheme.cython.constants"); if (!__pyx_t_1) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_PREPEND", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_PREPEND, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_CR", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_CR, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_LF", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_LF, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_CONTROL", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_CONTROL, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_EXTEND", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_EXTEND, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_REGIONAL_INDICATOR", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_REGIONAL_INDICATOR, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_SPACING_MARK", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_SPACING_MARK, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_L", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_L, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_V", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_V, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_T", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_T, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_LV", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_LV, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_LVT", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_LVT, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_ZWJ", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_ZWJ, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_EXTENDED_PICTOGRAPHIC", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_EXTENDED_PICTOGRAPHIC, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "G_OTHER", (void **)&__pyx_vp_8grapheme_6cython_9constants_G_OTHER, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "FSM_default", (void **)&__pyx_vp_8grapheme_6cython_9constants_FSM_default, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "FSM_cr", (void **)&__pyx_vp_8grapheme_6cython_9constants_FSM_cr, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "FSM_lf_or_control", (void **)&__pyx_vp_8grapheme_6cython_9constants_FSM_lf_or_control, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "FSM_prepend", (void **)&__pyx_vp_8grapheme_6cython_9constants_FSM_prepend, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "FSM_hangul_l", (void **)&__pyx_vp_8grapheme_6cython_9constants_FSM_hangul_l, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "FSM_hangul_lv_or_v", (void **)&__pyx_vp_8grapheme_6cython_9constants_FSM_hangul_lv_or_v, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "FSM_hangul_lvt_or_t", (void **)&__pyx_vp_8grapheme_6cython_9constants_FSM_hangul_lvt_or_t, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "FSM_emoji", (void **)&__pyx_vp_8grapheme_6cython_9constants_FSM_emoji, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "FSM_emoji_zjw", (void **)&__pyx_vp_8grapheme_6cython_9constants_FSM_emoji_zjw, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr(__pyx_t_1, "FSM_ri", (void **)&__pyx_vp_8grapheme_6cython_9constants_FSM_ri, "int") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  Py_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __Pyx_RefNannyFinishContext();
  return 0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_RefNannyFinishContext();
  return -1;
}

static int __Pyx_modinit_function_import_code(void) {
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  __Pyx_RefNannySetupContext("__Pyx_modinit_function_import_code", 0);
  /*--- Function import code ---*/
  __pyx_t_1 = PyImport_ImportModule("grapheme.cython.grapheme_property_group"); if (!__pyx_t_1) __PYX_ERR(0, 1, __pyx_L1_error)
  if (__Pyx_ImportFunction(__pyx_t_1, "c_get_group", (void (**)(void))&__pyx_f_8grapheme_6cython_23grapheme_property_group_c_get_group, "int (Py_UCS4)") < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  Py_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __Pyx_RefNannyFinishContext();
  return 0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_RefNannyFinishContext();
  return -1;
}


#if PY_MAJOR_VERSION < 3
#ifdef CYTHON_NO_PYINIT_EXPORT
#define __Pyx_PyMODINIT_FUNC void
#else
#define __Pyx_PyMODINIT_FUNC PyMODINIT_FUNC
#endif
#else
#ifdef CYTHON_NO_PYINIT_EXPORT
#define __Pyx_PyMODINIT_FUNC PyObject *
#else
#define __Pyx_PyMODINIT_FUNC PyMODINIT_FUNC
#endif
#endif


#if PY_MAJOR_VERSION < 3
__Pyx_PyMODINIT_FUNC initfinder(void) CYTHON_SMALL_CODE; /*proto*/
__Pyx_PyMODINIT_FUNC initfinder(void)
#else
__Pyx_PyMODINIT_FUNC PyInit_finder(void) CYTHON_SMALL_CODE; /*proto*/
__Pyx_PyMODINIT_FUNC PyInit_finder(void)
#if CYTHON_PEP489_MULTI_PHASE_INIT
{
  return PyModuleDef_Init(&__pyx_moduledef);
}
static CYTHON_SMALL_CODE int __Pyx_check_single_interpreter(void) {
    #if PY_VERSION_HEX >= 0x030700A1
    static PY_INT64_T main_interpreter_id = -1;
    PY_INT64_T current_id = PyInterpreterState_GetID(PyThreadState_Get()->interp);
    if (main_interpreter_id == -1) {
        main_interpreter_id = current_id;
        return (unlikely(current_id == -1)) ? -1 : 0;
    } else if (unlikely(main_interpreter_id != current_id))
    #else
    static PyInterpreterState *main_interpreter = NULL;
    PyInterpreterState *current_interpreter = PyThreadState_Get()->interp;
    if (!main_interpreter) {
        main_interpreter = current_interpreter;
    } else if (unlikely(main_interpreter != current_interpreter))
    #endif
    {
        PyErr_SetString(
            PyExc_ImportError,
            "Interpreter change detected - this module can only be loaded into one interpreter per process.");
        return -1;
    }
    return 0;
}
static CYTHON_SMALL_CODE int __Pyx_copy_spec_to_module(PyObject *spec, PyObject *moddict, const char* from_name, const char* to_name, int allow_none) {
    PyObject *value = PyObject_GetAttrString(spec, from_name);
    int result = 0;
    if (likely(value)) {
        if (allow_none || value != Py_None) {
            result = PyDict_SetItemString(moddict, to_name, value);
        }
        Py_DECREF(value);
    } else if (PyErr_ExceptionMatches(PyExc_AttributeError)) {
        PyErr_Clear();
    } else {
        result = -1;
    }
    return result;
}
static CYTHON_SMALL_CODE PyObject* __pyx_pymod_create(PyObject *spec, CYTHON_UNUSED PyModuleDef *def) {
    PyObject *module = NULL, *moddict, *modname;
    if (__Pyx_check_single_interpreter())
        return NULL;
    if (__pyx_m)
        return __Pyx_NewRef(__pyx_m);
    modname = PyObject_GetAttrString(spec, "name");
    if (unlikely(!modname)) goto bad;
    module = PyModule_NewObject(modname);
    Py_DECREF(modname);
    if (unlikely(!module)) goto bad;
    moddict = PyModule_GetDict(module);
    if (unlikely(!moddict)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "loader", "__loader__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "origin", "__file__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "parent", "__package__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "submodule_search_locations", "__path__", 0) < 0)) goto bad;
    return module;
bad:
    Py_XDECREF(module);
    return NULL;
}


static CYTHON_SMALL_CODE int __pyx_pymod_exec_finder(PyObject *__pyx_pyinit_module)
#endif
#endif
{
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  PyObject *__pyx_t_3 = NULL;
  PyObject *__pyx_t_4 = NULL;
  __Pyx_RefNannyDeclarations
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  if (__pyx_m) {
    if (__pyx_m == __pyx_pyinit_module) return 0;
    PyErr_SetString(PyExc_RuntimeError, "Module 'finder' has already been imported. Re-initialisation is not supported.");
    return -1;
  }
  #elif PY_MAJOR_VERSION >= 3
  if (__pyx_m) return __Pyx_NewRef(__pyx_m);
  #endif
  #if CYTHON_REFNANNY
__Pyx_RefNanny = __Pyx_RefNannyImportAPI("refnanny");
if (!__Pyx_RefNanny) {
  PyErr_Clear();
  __Pyx_RefNanny = __Pyx_RefNannyImportAPI("Cython.Runtime.refnanny");
  if (!__Pyx_RefNanny)
      Py_FatalError("failed to import 'refnanny' module");
}
#endif
  __Pyx_RefNannySetupContext("__Pyx_PyMODINIT_FUNC PyInit_finder(void)", 0);
  if (__Pyx_check_binary_version() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #ifdef __Pxy_PyFrame_Initialize_Offsets
  __Pxy_PyFrame_Initialize_Offsets();
  #endif
  __pyx_empty_tuple = PyTuple_New(0); if (unlikely(!__pyx_empty_tuple)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_empty_bytes = PyBytes_FromStringAndSize("", 0); if (unlikely(!__pyx_empty_bytes)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_empty_unicode = PyUnicode_FromStringAndSize("", 0); if (unlikely(!__pyx_empty_unicode)) __PYX_ERR(0, 1, __pyx_L1_error)
  #ifdef __Pyx_CyFunction_USED
  if (__pyx_CyFunction_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_FusedFunction_USED
  if (__pyx_FusedFunction_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_Coroutine_USED
  if (__pyx_Coroutine_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_Generator_USED
  if (__pyx_Generator_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_AsyncGen_USED
  if (__pyx_AsyncGen_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_StopAsyncIteration_USED
  if (__pyx_StopAsyncIteration_init() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  /*--- Library function declarations ---*/
  /*--- Threads initialization code ---*/
  #if defined(__PYX_FORCE_INIT_THREADS) && __PYX_FORCE_INIT_THREADS
  #ifdef WITH_THREAD /* Python build with threading support? */
  PyEval_InitThreads();
  #endif
  #endif
  /*--- Module creation code ---*/
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  __pyx_m = __pyx_pyinit_module;
  Py_INCREF(__pyx_m);
  #else
  #if PY_MAJOR_VERSION < 3
  __pyx_m = Py_InitModule4("finder", __pyx_methods, 0, 0, PYTHON_API_VERSION); Py_XINCREF(__pyx_m);
  #else
  __pyx_m = PyModule_Create(&__pyx_moduledef);
  #endif
  if (unlikely(!__pyx_m)) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  __pyx_d = PyModule_GetDict(__pyx_m); if (unlikely(!__pyx_d)) __PYX_ERR(0, 1, __pyx_L1_error)
  Py_INCREF(__pyx_d);
  __pyx_b = PyImport_AddModule(__Pyx_BUILTIN_MODULE_NAME); if (unlikely(!__pyx_b)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_cython_runtime = PyImport_AddModule((char *) "cython_runtime"); if (unlikely(!__pyx_cython_runtime)) __PYX_ERR(0, 1, __pyx_L1_error)
  #if CYTHON_COMPILING_IN_PYPY
  Py_INCREF(__pyx_b);
  #endif
  if (PyObject_SetAttrString(__pyx_m, "__builtins__", __pyx_b) < 0) __PYX_ERR(0, 1, __pyx_L1_error);
  /*--- Initialize various global constants etc. ---*/
  if (__Pyx_InitGlobals() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #if PY_MAJOR_VERSION < 3 && (__PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT)
  if (__Pyx_init_sys_getdefaultencoding_params() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  if (__pyx_module_is_main_grapheme__cython__finder) {
    if (PyObject_SetAttr(__pyx_m, __pyx_n_s_name, __pyx_n_s_main) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  }
  #if PY_MAJOR_VERSION >= 3
  {
    PyObject *modules = PyImport_GetModuleDict(); if (unlikely(!modules)) __PYX_ERR(0, 1, __pyx_L1_error)
    if (!PyDict_GetItemString(modules, "grapheme.cython.finder")) {
      if (unlikely(PyDict_SetItemString(modules, "grapheme.cython.finder", __pyx_m) < 0)) __PYX_ERR(0, 1, __pyx_L1_error)
    }
  }
  #endif
  /*--- Builtin init code ---*/
  if (__Pyx_InitCachedBuiltins() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  /*--- Constants init code ---*/
  if (__Pyx_InitCachedConstants() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  /*--- Global type/function init code ---*/
  (void)__Pyx_modinit_global_init_code();
  (void)__Pyx_modinit_variable_export_code();
  (void)__Pyx_modinit_function_export_code();
  if (unlikely(__Pyx_modinit_type_init_code() != 0)) goto __pyx_L1_error;
  (void)__Pyx_modinit_type_import_code();
  if (unlikely(__Pyx_modinit_variable_import_code() != 0)) goto __pyx_L1_error;
  if (unlikely(__Pyx_modinit_function_import_code() != 0)) goto __pyx_L1_error;
  /*--- Execution code ---*/
  #if defined(__Pyx_Generator_USED) || defined(__Pyx_Coroutine_USED)
  if (__Pyx_patch_abc() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif

  /* "grapheme/cython/finder.pyx":4
 * # cython: profile=False
 * 
 * from enum import Enum             # <<<<<<<<<<<<<<
 * 
 * from grapheme.cython.grapheme_property_group import GraphemePropertyGroup as G
 */
  __pyx_t_1 = PyList_New(1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 4, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_INCREF(__pyx_n_s_Enum);
  __Pyx_GIVEREF(__pyx_n_s_Enum);
  PyList_SET_ITEM(__pyx_t_1, 0, __pyx_n_s_Enum);
  __pyx_t_2 = __Pyx_Import(__pyx_n_s_enum, __pyx_t_1, 0); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 4, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = __Pyx_ImportFrom(__pyx_t_2, __pyx_n_s_Enum); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 4, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_Enum, __pyx_t_1) < 0) __PYX_ERR(0, 4, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "grapheme/cython/finder.pyx":6
 * from enum import Enum
 * 
 * from grapheme.cython.grapheme_property_group import GraphemePropertyGroup as G             # <<<<<<<<<<<<<<
 * from grapheme.cython.grapheme_property_group cimport c_get_group as get_group
 * 
 */
  __pyx_t_2 = PyList_New(1); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_INCREF(__pyx_n_s_GraphemePropertyGroup);
  __Pyx_GIVEREF(__pyx_n_s_GraphemePropertyGroup);
  PyList_SET_ITEM(__pyx_t_2, 0, __pyx_n_s_GraphemePropertyGroup);
  __pyx_t_1 = __Pyx_Import(__pyx_n_s_grapheme_cython_grapheme_propert, __pyx_t_2, 0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __pyx_t_2 = __Pyx_ImportFrom(__pyx_t_1, __pyx_n_s_GraphemePropertyGroup); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_G, __pyx_t_2) < 0) __PYX_ERR(0, 6, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "grapheme/cython/finder.pyx":138
 * cdef class FSM:
 *     @classmethod
 *     def default(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.OTHER:
 *             return True, cls.default
 */
  __Pyx_GetNameInClass(__pyx_t_1, (PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM, __pyx_n_s_default); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 138, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);

  /* "grapheme/cython/finder.pyx":137
 * 
 * cdef class FSM:
 *     @classmethod             # <<<<<<<<<<<<<<
 *     def default(cls, n):
 *         if n == G.OTHER:
 */
  __pyx_t_2 = __Pyx_Method_ClassMethod(__pyx_t_1); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 137, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (PyDict_SetItem((PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM->tp_dict, __pyx_n_s_default, __pyx_t_2) < 0) __PYX_ERR(0, 138, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  PyType_Modified(__pyx_ptype_8grapheme_6cython_6finder_FSM);

  /* "grapheme/cython/finder.pyx":172
 * 
 *     @classmethod
 *     def default_next_state(cls, n, should_break):             # <<<<<<<<<<<<<<
 *         _, next_state = cls.default(n)
 *         return should_break, next_state
 */
  __Pyx_GetNameInClass(__pyx_t_2, (PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM, __pyx_n_s_default_next_state); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 172, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);

  /* "grapheme/cython/finder.pyx":171
 *         return True, cls.default
 * 
 *     @classmethod             # <<<<<<<<<<<<<<
 *     def default_next_state(cls, n, should_break):
 *         _, next_state = cls.default(n)
 */
  __pyx_t_1 = __Pyx_Method_ClassMethod(__pyx_t_2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 171, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (PyDict_SetItem((PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM->tp_dict, __pyx_n_s_default_next_state, __pyx_t_1) < 0) __PYX_ERR(0, 172, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  PyType_Modified(__pyx_ptype_8grapheme_6cython_6finder_FSM);

  /* "grapheme/cython/finder.pyx":177
 * 
 *     @classmethod
 *     def cr(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.LF:
 *             return False, cls.lf_or_control
 */
  __Pyx_GetNameInClass(__pyx_t_1, (PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM, __pyx_n_s_cr); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 177, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);

  /* "grapheme/cython/finder.pyx":176
 *         return should_break, next_state
 * 
 *     @classmethod             # <<<<<<<<<<<<<<
 *     def cr(cls, n):
 *         if n == G.LF:
 */
  __pyx_t_2 = __Pyx_Method_ClassMethod(__pyx_t_1); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 176, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (PyDict_SetItem((PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM->tp_dict, __pyx_n_s_cr, __pyx_t_2) < 0) __PYX_ERR(0, 177, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  PyType_Modified(__pyx_ptype_8grapheme_6cython_6finder_FSM);

  /* "grapheme/cython/finder.pyx":183
 * 
 *     @classmethod
 *     def lf_or_control(cls, n):             # <<<<<<<<<<<<<<
 *         return cls.default_next_state(n, should_break=True)
 * 
 */
  __Pyx_GetNameInClass(__pyx_t_2, (PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM, __pyx_n_s_lf_or_control); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 183, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);

  /* "grapheme/cython/finder.pyx":182
 *         return cls.default_next_state(n, should_break=True)
 * 
 *     @classmethod             # <<<<<<<<<<<<<<
 *     def lf_or_control(cls, n):
 *         return cls.default_next_state(n, should_break=True)
 */
  __pyx_t_1 = __Pyx_Method_ClassMethod(__pyx_t_2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 182, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (PyDict_SetItem((PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM->tp_dict, __pyx_n_s_lf_or_control, __pyx_t_1) < 0) __PYX_ERR(0, 183, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  PyType_Modified(__pyx_ptype_8grapheme_6cython_6finder_FSM);

  /* "grapheme/cython/finder.pyx":187
 * 
 *     @classmethod
 *     def prepend(cls, n):             # <<<<<<<<<<<<<<
 *         if n in [G.CONTROL, G.LF]:
 *             return True, cls.default
 */
  __Pyx_GetNameInClass(__pyx_t_1, (PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM, __pyx_n_s_prepend); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 187, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);

  /* "grapheme/cython/finder.pyx":186
 *         return cls.default_next_state(n, should_break=True)
 * 
 *     @classmethod             # <<<<<<<<<<<<<<
 *     def prepend(cls, n):
 *         if n in [G.CONTROL, G.LF]:
 */
  __pyx_t_2 = __Pyx_Method_ClassMethod(__pyx_t_1); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 186, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (PyDict_SetItem((PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM->tp_dict, __pyx_n_s_prepend, __pyx_t_2) < 0) __PYX_ERR(0, 187, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  PyType_Modified(__pyx_ptype_8grapheme_6cython_6finder_FSM);

  /* "grapheme/cython/finder.pyx":196
 *     # Hanguls
 *     @classmethod
 *     def hangul_l(cls, n):             # <<<<<<<<<<<<<<
 *         if n in [G.V, G.LV]:
 *             return False, cls.hangul_lv_or_v
 */
  __Pyx_GetNameInClass(__pyx_t_2, (PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM, __pyx_n_s_hangul_l); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 196, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);

  /* "grapheme/cython/finder.pyx":195
 * 
 *     # Hanguls
 *     @classmethod             # <<<<<<<<<<<<<<
 *     def hangul_l(cls, n):
 *         if n in [G.V, G.LV]:
 */
  __pyx_t_1 = __Pyx_Method_ClassMethod(__pyx_t_2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 195, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (PyDict_SetItem((PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM->tp_dict, __pyx_n_s_hangul_l, __pyx_t_1) < 0) __PYX_ERR(0, 196, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  PyType_Modified(__pyx_ptype_8grapheme_6cython_6finder_FSM);

  /* "grapheme/cython/finder.pyx":206
 * 
 *     @classmethod
 *     def hangul_lv_or_v(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.V:
 *             return False, cls.hangul_lv_or_v
 */
  __Pyx_GetNameInClass(__pyx_t_1, (PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM, __pyx_n_s_hangul_lv_or_v); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 206, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);

  /* "grapheme/cython/finder.pyx":205
 *         return cls.default(n)
 * 
 *     @classmethod             # <<<<<<<<<<<<<<
 *     def hangul_lv_or_v(cls, n):
 *         if n == G.V:
 */
  __pyx_t_2 = __Pyx_Method_ClassMethod(__pyx_t_1); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 205, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (PyDict_SetItem((PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM->tp_dict, __pyx_n_s_hangul_lv_or_v, __pyx_t_2) < 0) __PYX_ERR(0, 206, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  PyType_Modified(__pyx_ptype_8grapheme_6cython_6finder_FSM);

  /* "grapheme/cython/finder.pyx":214
 * 
 *     @classmethod
 *     def hangul_lvt_or_t(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.T:
 *             return False, cls.hangul_lvt_or_t
 */
  __Pyx_GetNameInClass(__pyx_t_2, (PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM, __pyx_n_s_hangul_lvt_or_t); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 214, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);

  /* "grapheme/cython/finder.pyx":213
 *         return cls.default(n)
 * 
 *     @classmethod             # <<<<<<<<<<<<<<
 *     def hangul_lvt_or_t(cls, n):
 *         if n == G.T:
 */
  __pyx_t_1 = __Pyx_Method_ClassMethod(__pyx_t_2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 213, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (PyDict_SetItem((PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM->tp_dict, __pyx_n_s_hangul_lvt_or_t, __pyx_t_1) < 0) __PYX_ERR(0, 214, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  PyType_Modified(__pyx_ptype_8grapheme_6cython_6finder_FSM);

  /* "grapheme/cython/finder.pyx":221
 *     # Emojis
 *     @classmethod
 *     def emoji(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.EXTEND:
 *             return False, cls.emoji
 */
  __Pyx_GetNameInClass(__pyx_t_1, (PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM, __pyx_n_s_emoji); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 221, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);

  /* "grapheme/cython/finder.pyx":220
 * 
 *     # Emojis
 *     @classmethod             # <<<<<<<<<<<<<<
 *     def emoji(cls, n):
 *         if n == G.EXTEND:
 */
  __pyx_t_2 = __Pyx_Method_ClassMethod(__pyx_t_1); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 220, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (PyDict_SetItem((PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM->tp_dict, __pyx_n_s_emoji, __pyx_t_2) < 0) __PYX_ERR(0, 221, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  PyType_Modified(__pyx_ptype_8grapheme_6cython_6finder_FSM);

  /* "grapheme/cython/finder.pyx":229
 * 
 *     @classmethod
 *     def emoji_zjw(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.EXTENDED_PICTOGRAPHIC:
 *             return False, cls.emoji
 */
  __Pyx_GetNameInClass(__pyx_t_2, (PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM, __pyx_n_s_emoji_zjw); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 229, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);

  /* "grapheme/cython/finder.pyx":228
 *         return cls.default(n)
 * 
 *     @classmethod             # <<<<<<<<<<<<<<
 *     def emoji_zjw(cls, n):
 *         if n == G.EXTENDED_PICTOGRAPHIC:
 */
  __pyx_t_1 = __Pyx_Method_ClassMethod(__pyx_t_2); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 228, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (PyDict_SetItem((PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM->tp_dict, __pyx_n_s_emoji_zjw, __pyx_t_1) < 0) __PYX_ERR(0, 229, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  PyType_Modified(__pyx_ptype_8grapheme_6cython_6finder_FSM);

  /* "grapheme/cython/finder.pyx":236
 *     # Regional indication (flag)
 *     @classmethod
 *     def ri(cls, n):             # <<<<<<<<<<<<<<
 *         if n == G.REGIONAL_INDICATOR:
 *             return False, cls.default
 */
  __Pyx_GetNameInClass(__pyx_t_1, (PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM, __pyx_n_s_ri); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 236, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);

  /* "grapheme/cython/finder.pyx":235
 * 
 *     # Regional indication (flag)
 *     @classmethod             # <<<<<<<<<<<<<<
 *     def ri(cls, n):
 *         if n == G.REGIONAL_INDICATOR:
 */
  __pyx_t_2 = __Pyx_Method_ClassMethod(__pyx_t_1); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 235, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  if (PyDict_SetItem((PyObject *)__pyx_ptype_8grapheme_6cython_6finder_FSM->tp_dict, __pyx_n_s_ri, __pyx_t_2) < 0) __PYX_ERR(0, 236, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  PyType_Modified(__pyx_ptype_8grapheme_6cython_6finder_FSM);

  /* "grapheme/cython/finder.pyx":242
 * 
 * 
 * class BreakPossibility(Enum):             # <<<<<<<<<<<<<<
 *     CERTAIN = "certain"
 *     POSSIBLE = "possible"
 */
  __Pyx_GetModuleGlobalName(__pyx_t_2, __pyx_n_s_Enum); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 242, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_1 = PyTuple_New(1); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 242, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_GIVEREF(__pyx_t_2);
  PyTuple_SET_ITEM(__pyx_t_1, 0, __pyx_t_2);
  __pyx_t_2 = 0;
  __pyx_t_2 = __Pyx_CalculateMetaclass(NULL, __pyx_t_1); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 242, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __pyx_t_3 = __Pyx_Py3MetaclassPrepare(__pyx_t_2, __pyx_t_1, __pyx_n_s_BreakPossibility, __pyx_n_s_BreakPossibility, (PyObject *) NULL, __pyx_n_s_grapheme_cython_finder, (PyObject *) NULL); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 242, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);

  /* "grapheme/cython/finder.pyx":243
 * 
 * class BreakPossibility(Enum):
 *     CERTAIN = "certain"             # <<<<<<<<<<<<<<
 *     POSSIBLE = "possible"
 *     NO_BREAK = "nobreak"
 */
  if (__Pyx_SetNameInClass(__pyx_t_3, __pyx_n_s_CERTAIN, __pyx_n_u_certain) < 0) __PYX_ERR(0, 243, __pyx_L1_error)

  /* "grapheme/cython/finder.pyx":244
 * class BreakPossibility(Enum):
 *     CERTAIN = "certain"
 *     POSSIBLE = "possible"             # <<<<<<<<<<<<<<
 *     NO_BREAK = "nobreak"
 * 
 */
  if (__Pyx_SetNameInClass(__pyx_t_3, __pyx_n_s_POSSIBLE, __pyx_n_u_possible) < 0) __PYX_ERR(0, 244, __pyx_L1_error)

  /* "grapheme/cython/finder.pyx":245
 *     CERTAIN = "certain"
 *     POSSIBLE = "possible"
 *     NO_BREAK = "nobreak"             # <<<<<<<<<<<<<<
 * 
 * 
 */
  if (__Pyx_SetNameInClass(__pyx_t_3, __pyx_n_s_NO_BREAK, __pyx_n_u_nobreak) < 0) __PYX_ERR(0, 245, __pyx_L1_error)

  /* "grapheme/cython/finder.pyx":242
 * 
 * 
 * class BreakPossibility(Enum):             # <<<<<<<<<<<<<<
 *     CERTAIN = "certain"
 *     POSSIBLE = "possible"
 */
  __pyx_t_4 = __Pyx_Py3ClassCreate(__pyx_t_2, __pyx_n_s_BreakPossibility, __pyx_t_1, __pyx_t_3, NULL, 0, 0); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 242, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_BreakPossibility, __pyx_t_4) < 0) __PYX_ERR(0, 242, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_4); __pyx_t_4 = 0;
  __Pyx_DECREF(__pyx_t_3); __pyx_t_3 = 0;
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "grapheme/cython/finder.pyx":248
 * 
 * 
 * def get_break_possibility(a, b):             # <<<<<<<<<<<<<<
 *     # Probably most common, included as short circuit before checking all else
 *     if a == G.OTHER and b == G.OTHER:
 */
  __pyx_t_1 = PyCFunction_NewEx(&__pyx_mdef_8grapheme_6cython_6finder_1get_break_possibility, NULL, __pyx_n_s_grapheme_cython_finder); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 248, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_get_break_possibility, __pyx_t_1) < 0) __PYX_ERR(0, 248, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "grapheme/cython/finder.pyx":297
 * 
 * 
 * def get_last_certain_break_index(string, index):             # <<<<<<<<<<<<<<
 *     if index >= len(string):
 *         return len(string)
 */
  __pyx_t_1 = PyCFunction_NewEx(&__pyx_mdef_8grapheme_6cython_6finder_3get_last_certain_break_index, NULL, __pyx_n_s_grapheme_cython_finder); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 297, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_get_last_certain_break_index, __pyx_t_1) < 0) __PYX_ERR(0, 297, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "(tree fragment)":1
 * def __pyx_unpickle_FSM(__pyx_type, long __pyx_checksum, __pyx_state):             # <<<<<<<<<<<<<<
 *     cdef object __pyx_PickleError
 *     cdef object __pyx_result
 */
  __pyx_t_1 = PyCFunction_NewEx(&__pyx_mdef_8grapheme_6cython_6finder_7__pyx_unpickle_FSM, NULL, __pyx_n_s_grapheme_cython_finder); if (unlikely(!__pyx_t_1)) __PYX_ERR(1, 1, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_pyx_unpickle_FSM, __pyx_t_1) < 0) __PYX_ERR(1, 1, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "grapheme/cython/finder.pyx":1
 * # cython: language_level=3             # <<<<<<<<<<<<<<
 * # cython: profile=False
 * 
 */
  __pyx_t_1 = __Pyx_PyDict_NewPresized(0); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 1, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_test, __pyx_t_1) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /*--- Wrapped vars code ---*/

  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_3);
  __Pyx_XDECREF(__pyx_t_4);
  if (__pyx_m) {
    if (__pyx_d) {
      __Pyx_AddTraceback("init grapheme.cython.finder", __pyx_clineno, __pyx_lineno, __pyx_filename);
    }
    Py_CLEAR(__pyx_m);
  } else if (!PyErr_Occurred()) {
    PyErr_SetString(PyExc_ImportError, "init grapheme.cython.finder");
  }
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  return (__pyx_m != NULL) ? 0 : -1;
  #elif PY_MAJOR_VERSION >= 3
  return __pyx_m;
  #else
  return;
  #endif
}

/* --- Runtime support code --- */
/* Refnanny */
#if CYTHON_REFNANNY
static __Pyx_RefNannyAPIStruct *__Pyx_RefNannyImportAPI(const char *modname) {
    PyObject *m = NULL, *p = NULL;
    void *r = NULL;
    m = PyImport_ImportModule(modname);
    if (!m) goto end;
    p = PyObject_GetAttrString(m, "RefNannyAPI");
    if (!p) goto end;
    r = PyLong_AsVoidPtr(p);
end:
    Py_XDECREF(p);
    Py_XDECREF(m);
    return (__Pyx_RefNannyAPIStruct *)r;
}
#endif

/* PyObjectGetAttrStr */
#if CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStr(PyObject* obj, PyObject* attr_name) {
    PyTypeObject* tp = Py_TYPE(obj);
    if (likely(tp->tp_getattro))
        return tp->tp_getattro(obj, attr_name);
#if PY_MAJOR_VERSION < 3
    if (likely(tp->tp_getattr))
        return tp->tp_getattr(obj, PyString_AS_STRING(attr_name));
#endif
    return PyObject_GetAttr(obj, attr_name);
}
#endif

/* GetBuiltinName */
static PyObject *__Pyx_GetBuiltinName(PyObject *name) {
    PyObject* result = __Pyx_PyObject_GetAttrStr(__pyx_b, name);
    if (unlikely(!result)) {
        PyErr_Format(PyExc_NameError,
#if PY_MAJOR_VERSION >= 3
            "name '%U' is not defined", name);
#else
            "name '%.200s' is not defined", PyString_AS_STRING(name));
#endif
    }
    return result;
}

/* PyErrFetchRestore */
#if CYTHON_FAST_THREAD_STATE
static CYTHON_INLINE void __Pyx_ErrRestoreInState(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb) {
    PyObject *tmp_type, *tmp_value, *tmp_tb;
    tmp_type = tstate->curexc_type;
    tmp_value = tstate->curexc_value;
    tmp_tb = tstate->curexc_traceback;
    tstate->curexc_type = type;
    tstate->curexc_value = value;
    tstate->curexc_traceback = tb;
    Py_XDECREF(tmp_type);
    Py_XDECREF(tmp_value);
    Py_XDECREF(tmp_tb);
}
static CYTHON_INLINE void __Pyx_ErrFetchInState(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb) {
    *type = tstate->curexc_type;
    *value = tstate->curexc_value;
    *tb = tstate->curexc_traceback;
    tstate->curexc_type = 0;
    tstate->curexc_value = 0;
    tstate->curexc_traceback = 0;
}
#endif

/* WriteUnraisableException */
static void __Pyx_WriteUnraisable(const char *name, CYTHON_UNUSED int clineno,
                                  CYTHON_UNUSED int lineno, CYTHON_UNUSED const char *filename,
                                  int full_traceback, CYTHON_UNUSED int nogil) {
    PyObject *old_exc, *old_val, *old_tb;
    PyObject *ctx;
    __Pyx_PyThreadState_declare
#ifdef WITH_THREAD
    PyGILState_STATE state;
    if (nogil)
        state = PyGILState_Ensure();
#ifdef _MSC_VER
    else state = (PyGILState_STATE)-1;
#endif
#endif
    __Pyx_PyThreadState_assign
    __Pyx_ErrFetch(&old_exc, &old_val, &old_tb);
    if (full_traceback) {
        Py_XINCREF(old_exc);
        Py_XINCREF(old_val);
        Py_XINCREF(old_tb);
        __Pyx_ErrRestore(old_exc, old_val, old_tb);
        PyErr_PrintEx(1);
    }
    #if PY_MAJOR_VERSION < 3
    ctx = PyString_FromString(name);
    #else
    ctx = PyUnicode_FromString(name);
    #endif
    __Pyx_ErrRestore(old_exc, old_val, old_tb);
    if (!ctx) {
        PyErr_WriteUnraisable(Py_None);
    } else {
        PyErr_WriteUnraisable(ctx);
        Py_DECREF(ctx);
    }
#ifdef WITH_THREAD
    if (nogil)
        PyGILState_Release(state);
#endif
}

/* GetModuleGlobalName */
#if CYTHON_USE_DICT_VERSIONS
static PyObject *__Pyx__GetModuleGlobalName(PyObject *name, PY_UINT64_T *dict_version, PyObject **dict_cached_value)
#else
static CYTHON_INLINE PyObject *__Pyx__GetModuleGlobalName(PyObject *name)
#endif
{
    PyObject *result;
#if !CYTHON_AVOID_BORROWED_REFS
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x030500A1
    result = _PyDict_GetItem_KnownHash(__pyx_d, name, ((PyASCIIObject *) name)->hash);
    __PYX_UPDATE_DICT_CACHE(__pyx_d, result, *dict_cached_value, *dict_version)
    if (likely(result)) {
        return __Pyx_NewRef(result);
    } else if (unlikely(PyErr_Occurred())) {
        return NULL;
    }
#else
    result = PyDict_GetItem(__pyx_d, name);
    __PYX_UPDATE_DICT_CACHE(__pyx_d, result, *dict_cached_value, *dict_version)
    if (likely(result)) {
        return __Pyx_NewRef(result);
    }
#endif
#else
    result = PyObject_GetItem(__pyx_d, name);
    __PYX_UPDATE_DICT_CACHE(__pyx_d, result, *dict_cached_value, *dict_version)
    if (likely(result)) {
        return __Pyx_NewRef(result);
    }
    PyErr_Clear();
#endif
    return __Pyx_GetBuiltinName(name);
}

/* RaiseArgTupleInvalid */
static void __Pyx_RaiseArgtupleInvalid(
    const char* func_name,
    int exact,
    Py_ssize_t num_min,
    Py_ssize_t num_max,
    Py_ssize_t num_found)
{
    Py_ssize_t num_expected;
    const char *more_or_less;
    if (num_found < num_min) {
        num_expected = num_min;
        more_or_less = "at least";
    } else {
        num_expected = num_max;
        more_or_less = "at most";
    }
    if (exact) {
        more_or_less = "exactly";
    }
    PyErr_Format(PyExc_TypeError,
                 "%.200s() takes %.8s %" CYTHON_FORMAT_SSIZE_T "d positional argument%.1s (%" CYTHON_FORMAT_SSIZE_T "d given)",
                 func_name, more_or_less, num_expected,
                 (num_expected == 1) ? "" : "s", num_found);
}

/* RaiseDoubleKeywords */
static void __Pyx_RaiseDoubleKeywordsError(
    const char* func_name,
    PyObject* kw_name)
{
    PyErr_Format(PyExc_TypeError,
        #if PY_MAJOR_VERSION >= 3
        "%s() got multiple values for keyword argument '%U'", func_name, kw_name);
        #else
        "%s() got multiple values for keyword argument '%s'", func_name,
        PyString_AsString(kw_name));
        #endif
}

/* ParseKeywords */
static int __Pyx_ParseOptionalKeywords(
    PyObject *kwds,
    PyObject **argnames[],
    PyObject *kwds2,
    PyObject *values[],
    Py_ssize_t num_pos_args,
    const char* function_name)
{
    PyObject *key = 0, *value = 0;
    Py_ssize_t pos = 0;
    PyObject*** name;
    PyObject*** first_kw_arg = argnames + num_pos_args;
    while (PyDict_Next(kwds, &pos, &key, &value)) {
        name = first_kw_arg;
        while (*name && (**name != key)) name++;
        if (*name) {
            values[name-argnames] = value;
            continue;
        }
        name = first_kw_arg;
        #if PY_MAJOR_VERSION < 3
        if (likely(PyString_CheckExact(key)) || likely(PyString_Check(key))) {
            while (*name) {
                if ((CYTHON_COMPILING_IN_PYPY || PyString_GET_SIZE(**name) == PyString_GET_SIZE(key))
                        && _PyString_Eq(**name, key)) {
                    values[name-argnames] = value;
                    break;
                }
                name++;
            }
            if (*name) continue;
            else {
                PyObject*** argname = argnames;
                while (argname != first_kw_arg) {
                    if ((**argname == key) || (
                            (CYTHON_COMPILING_IN_PYPY || PyString_GET_SIZE(**argname) == PyString_GET_SIZE(key))
                             && _PyString_Eq(**argname, key))) {
                        goto arg_passed_twice;
                    }
                    argname++;
                }
            }
        } else
        #endif
        if (likely(PyUnicode_Check(key))) {
            while (*name) {
                int cmp = (**name == key) ? 0 :
                #if !CYTHON_COMPILING_IN_PYPY && PY_MAJOR_VERSION >= 3
                    (PyUnicode_GET_SIZE(**name) != PyUnicode_GET_SIZE(key)) ? 1 :
                #endif
                    PyUnicode_Compare(**name, key);
                if (cmp < 0 && unlikely(PyErr_Occurred())) goto bad;
                if (cmp == 0) {
                    values[name-argnames] = value;
                    break;
                }
                name++;
            }
            if (*name) continue;
            else {
                PyObject*** argname = argnames;
                while (argname != first_kw_arg) {
                    int cmp = (**argname == key) ? 0 :
                    #if !CYTHON_COMPILING_IN_PYPY && PY_MAJOR_VERSION >= 3
                        (PyUnicode_GET_SIZE(**argname) != PyUnicode_GET_SIZE(key)) ? 1 :
                    #endif
                        PyUnicode_Compare(**argname, key);
                    if (cmp < 0 && unlikely(PyErr_Occurred())) goto bad;
                    if (cmp == 0) goto arg_passed_twice;
                    argname++;
                }
            }
        } else
            goto invalid_keyword_type;
        if (kwds2) {
            if (unlikely(PyDict_SetItem(kwds2, key, value))) goto bad;
        } else {
            goto invalid_keyword;
        }
    }
    return 0;
arg_passed_twice:
    __Pyx_RaiseDoubleKeywordsError(function_name, key);
    goto bad;
invalid_keyword_type:
    PyErr_Format(PyExc_TypeError,
        "%.200s() keywords must be strings", function_name);
    goto bad;
invalid_keyword:
    PyErr_Format(PyExc_TypeError,
    #if PY_MAJOR_VERSION < 3
        "%.200s() got an unexpected keyword argument '%.200s'",
        function_name, PyString_AsString(key));
    #else
        "%s() got an unexpected keyword argument '%U'",
        function_name, key);
    #endif
bad:
    return -1;
}

/* PyCFunctionFastCall */
#if CYTHON_FAST_PYCCALL
static CYTHON_INLINE PyObject * __Pyx_PyCFunction_FastCall(PyObject *func_obj, PyObject **args, Py_ssize_t nargs) {
    PyCFunctionObject *func = (PyCFunctionObject*)func_obj;
    PyCFunction meth = PyCFunction_GET_FUNCTION(func);
    PyObject *self = PyCFunction_GET_SELF(func);
    int flags = PyCFunction_GET_FLAGS(func);
    assert(PyCFunction_Check(func));
    assert(METH_FASTCALL == (flags & ~(METH_CLASS | METH_STATIC | METH_COEXIST | METH_KEYWORDS | METH_STACKLESS)));
    assert(nargs >= 0);
    assert(nargs == 0 || args != NULL);
    /* _PyCFunction_FastCallDict() must not be called with an exception set,
       because it may clear it (directly or indirectly) and so the
       caller loses its exception */
    assert(!PyErr_Occurred());
    if ((PY_VERSION_HEX < 0x030700A0) || unlikely(flags & METH_KEYWORDS)) {
        return (*((__Pyx_PyCFunctionFastWithKeywords)(void*)meth)) (self, args, nargs, NULL);
    } else {
        return (*((__Pyx_PyCFunctionFast)(void*)meth)) (self, args, nargs);
    }
}
#endif

/* PyFunctionFastCall */
#if CYTHON_FAST_PYCALL
static PyObject* __Pyx_PyFunction_FastCallNoKw(PyCodeObject *co, PyObject **args, Py_ssize_t na,
                                               PyObject *globals) {
    PyFrameObject *f;
    PyThreadState *tstate = __Pyx_PyThreadState_Current;
    PyObject **fastlocals;
    Py_ssize_t i;
    PyObject *result;
    assert(globals != NULL);
    /* XXX Perhaps we should create a specialized
       PyFrame_New() that doesn't take locals, but does
       take builtins without sanity checking them.
       */
    assert(tstate != NULL);
    f = PyFrame_New(tstate, co, globals, NULL);
    if (f == NULL) {
        return NULL;
    }
    fastlocals = __Pyx_PyFrame_GetLocalsplus(f);
    for (i = 0; i < na; i++) {
        Py_INCREF(*args);
        fastlocals[i] = *args++;
    }
    result = PyEval_EvalFrameEx(f,0);
    ++tstate->recursion_depth;
    Py_DECREF(f);
    --tstate->recursion_depth;
    return result;
}
#if 1 || PY_VERSION_HEX < 0x030600B1
static PyObject *__Pyx_PyFunction_FastCallDict(PyObject *func, PyObject **args, int nargs, PyObject *kwargs) {
    PyCodeObject *co = (PyCodeObject *)PyFunction_GET_CODE(func);
    PyObject *globals = PyFunction_GET_GLOBALS(func);
    PyObject *argdefs = PyFunction_GET_DEFAULTS(func);
    PyObject *closure;
#if PY_MAJOR_VERSION >= 3
    PyObject *kwdefs;
#endif
    PyObject *kwtuple, **k;
    PyObject **d;
    Py_ssize_t nd;
    Py_ssize_t nk;
    PyObject *result;
    assert(kwargs == NULL || PyDict_Check(kwargs));
    nk = kwargs ? PyDict_Size(kwargs) : 0;
    if (Py_EnterRecursiveCall((char*)" while calling a Python object")) {
        return NULL;
    }
    if (
#if PY_MAJOR_VERSION >= 3
            co->co_kwonlyargcount == 0 &&
#endif
            likely(kwargs == NULL || nk == 0) &&
            co->co_flags == (CO_OPTIMIZED | CO_NEWLOCALS | CO_NOFREE)) {
        if (argdefs == NULL && co->co_argcount == nargs) {
            result = __Pyx_PyFunction_FastCallNoKw(co, args, nargs, globals);
            goto done;
        }
        else if (nargs == 0 && argdefs != NULL
                 && co->co_argcount == Py_SIZE(argdefs)) {
            /* function called with no arguments, but all parameters have
               a default value: use default values as arguments .*/
            args = &PyTuple_GET_ITEM(argdefs, 0);
            result =__Pyx_PyFunction_FastCallNoKw(co, args, Py_SIZE(argdefs), globals);
            goto done;
        }
    }
    if (kwargs != NULL) {
        Py_ssize_t pos, i;
        kwtuple = PyTuple_New(2 * nk);
        if (kwtuple == NULL) {
            result = NULL;
            goto done;
        }
        k = &PyTuple_GET_ITEM(kwtuple, 0);
        pos = i = 0;
        while (PyDict_Next(kwargs, &pos, &k[i], &k[i+1])) {
            Py_INCREF(k[i]);
            Py_INCREF(k[i+1]);
            i += 2;
        }
        nk = i / 2;
    }
    else {
        kwtuple = NULL;
        k = NULL;
    }
    closure = PyFunction_GET_CLOSURE(func);
#if PY_MAJOR_VERSION >= 3
    kwdefs = PyFunction_GET_KW_DEFAULTS(func);
#endif
    if (argdefs != NULL) {
        d = &PyTuple_GET_ITEM(argdefs, 0);
        nd = Py_SIZE(argdefs);
    }
    else {
        d = NULL;
        nd = 0;
    }
#if PY_MAJOR_VERSION >= 3
    result = PyEval_EvalCodeEx((PyObject*)co, globals, (PyObject *)NULL,
                               args, nargs,
                               k, (int)nk,
                               d, (int)nd, kwdefs, closure);
#else
    result = PyEval_EvalCodeEx(co, globals, (PyObject *)NULL,
                               args, nargs,
                               k, (int)nk,
                               d, (int)nd, closure);
#endif
    Py_XDECREF(kwtuple);
done:
    Py_LeaveRecursiveCall();
    return result;
}
#endif
#endif

/* PyObjectCall */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_Call(PyObject *func, PyObject *arg, PyObject *kw) {
    PyObject *result;
    ternaryfunc call = func->ob_type->tp_call;
    if (unlikely(!call))
        return PyObject_Call(func, arg, kw);
    if (unlikely(Py_EnterRecursiveCall((char*)" while calling a Python object")))
        return NULL;
    result = (*call)(func, arg, kw);
    Py_LeaveRecursiveCall();
    if (unlikely(!result) && unlikely(!PyErr_Occurred())) {
        PyErr_SetString(
            PyExc_SystemError,
            "NULL result without error in PyObject_Call");
    }
    return result;
}
#endif

/* PyObjectCall2Args */
static CYTHON_UNUSED PyObject* __Pyx_PyObject_Call2Args(PyObject* function, PyObject* arg1, PyObject* arg2) {
    PyObject *args, *result = NULL;
    #if CYTHON_FAST_PYCALL
    if (PyFunction_Check(function)) {
        PyObject *args[2] = {arg1, arg2};
        return __Pyx_PyFunction_FastCall(function, args, 2);
    }
    #endif
    #if CYTHON_FAST_PYCCALL
    if (__Pyx_PyFastCFunction_Check(function)) {
        PyObject *args[2] = {arg1, arg2};
        return __Pyx_PyCFunction_FastCall(function, args, 2);
    }
    #endif
    args = PyTuple_New(2);
    if (unlikely(!args)) goto done;
    Py_INCREF(arg1);
    PyTuple_SET_ITEM(args, 0, arg1);
    Py_INCREF(arg2);
    PyTuple_SET_ITEM(args, 1, arg2);
    Py_INCREF(function);
    result = __Pyx_PyObject_Call(function, args, NULL);
    Py_DECREF(args);
    Py_DECREF(function);
done:
    return result;
}

/* PyObjectCallMethO */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallMethO(PyObject *func, PyObject *arg) {
    PyObject *self, *result;
    PyCFunction cfunc;
    cfunc = PyCFunction_GET_FUNCTION(func);
    self = PyCFunction_GET_SELF(func);
    if (unlikely(Py_EnterRecursiveCall((char*)" while calling a Python object")))
        return NULL;
    result = cfunc(self, arg);
    Py_LeaveRecursiveCall();
    if (unlikely(!result) && unlikely(!PyErr_Occurred())) {
        PyErr_SetString(
            PyExc_SystemError,
            "NULL result without error in PyObject_Call");
    }
    return result;
}
#endif

/* PyObjectCallOneArg */
#if CYTHON_COMPILING_IN_CPYTHON
static PyObject* __Pyx__PyObject_CallOneArg(PyObject *func, PyObject *arg) {
    PyObject *result;
    PyObject *args = PyTuple_New(1);
    if (unlikely(!args)) return NULL;
    Py_INCREF(arg);
    PyTuple_SET_ITEM(args, 0, arg);
    result = __Pyx_PyObject_Call(func, args, NULL);
    Py_DECREF(args);
    return result;
}
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallOneArg(PyObject *func, PyObject *arg) {
#if CYTHON_FAST_PYCALL
    if (PyFunction_Check(func)) {
        return __Pyx_PyFunction_FastCall(func, &arg, 1);
    }
#endif
    if (likely(PyCFunction_Check(func))) {
        if (likely(PyCFunction_GET_FLAGS(func) & METH_O)) {
            return __Pyx_PyObject_CallMethO(func, arg);
#if CYTHON_FAST_PYCCALL
        } else if (PyCFunction_GET_FLAGS(func) & METH_FASTCALL) {
            return __Pyx_PyCFunction_FastCall(func, &arg, 1);
#endif
        }
    }
    return __Pyx__PyObject_CallOneArg(func, arg);
}
#else
static CYTHON_INLINE PyObject* __Pyx_PyObject_CallOneArg(PyObject *func, PyObject *arg) {
    PyObject *result;
    PyObject *args = PyTuple_Pack(1, arg);
    if (unlikely(!args)) return NULL;
    result = __Pyx_PyObject_Call(func, args, NULL);
    Py_DECREF(args);
    return result;
}
#endif

/* RaiseTooManyValuesToUnpack */
static CYTHON_INLINE void __Pyx_RaiseTooManyValuesError(Py_ssize_t expected) {
    PyErr_Format(PyExc_ValueError,
                 "too many values to unpack (expected %" CYTHON_FORMAT_SSIZE_T "d)", expected);
}

/* RaiseNeedMoreValuesToUnpack */
static CYTHON_INLINE void __Pyx_RaiseNeedMoreValuesError(Py_ssize_t index) {
    PyErr_Format(PyExc_ValueError,
                 "need more than %" CYTHON_FORMAT_SSIZE_T "d value%.1s to unpack",
                 index, (index == 1) ? "" : "s");
}

/* IterFinish */
static CYTHON_INLINE int __Pyx_IterFinish(void) {
#if CYTHON_FAST_THREAD_STATE
    PyThreadState *tstate = __Pyx_PyThreadState_Current;
    PyObject* exc_type = tstate->curexc_type;
    if (unlikely(exc_type)) {
        if (likely(__Pyx_PyErr_GivenExceptionMatches(exc_type, PyExc_StopIteration))) {
            PyObject *exc_value, *exc_tb;
            exc_value = tstate->curexc_value;
            exc_tb = tstate->curexc_traceback;
            tstate->curexc_type = 0;
            tstate->curexc_value = 0;
            tstate->curexc_traceback = 0;
            Py_DECREF(exc_type);
            Py_XDECREF(exc_value);
            Py_XDECREF(exc_tb);
            return 0;
        } else {
            return -1;
        }
    }
    return 0;
#else
    if (unlikely(PyErr_Occurred())) {
        if (likely(PyErr_ExceptionMatches(PyExc_StopIteration))) {
            PyErr_Clear();
            return 0;
        } else {
            return -1;
        }
    }
    return 0;
#endif
}

/* UnpackItemEndCheck */
static int __Pyx_IternextUnpackEndCheck(PyObject *retval, Py_ssize_t expected) {
    if (unlikely(retval)) {
        Py_DECREF(retval);
        __Pyx_RaiseTooManyValuesError(expected);
        return -1;
    } else {
        return __Pyx_IterFinish();
    }
    return 0;
}

/* PyErrExceptionMatches */
#if CYTHON_FAST_THREAD_STATE
static int __Pyx_PyErr_ExceptionMatchesTuple(PyObject *exc_type, PyObject *tuple) {
    Py_ssize_t i, n;
    n = PyTuple_GET_SIZE(tuple);
#if PY_MAJOR_VERSION >= 3
    for (i=0; i<n; i++) {
        if (exc_type == PyTuple_GET_ITEM(tuple, i)) return 1;
    }
#endif
    for (i=0; i<n; i++) {
        if (__Pyx_PyErr_GivenExceptionMatches(exc_type, PyTuple_GET_ITEM(tuple, i))) return 1;
    }
    return 0;
}
static CYTHON_INLINE int __Pyx_PyErr_ExceptionMatchesInState(PyThreadState* tstate, PyObject* err) {
    PyObject *exc_type = tstate->curexc_type;
    if (exc_type == err) return 1;
    if (unlikely(!exc_type)) return 0;
    if (unlikely(PyTuple_Check(err)))
        return __Pyx_PyErr_ExceptionMatchesTuple(exc_type, err);
    return __Pyx_PyErr_GivenExceptionMatches(exc_type, err);
}
#endif

/* GetAttr */
static CYTHON_INLINE PyObject *__Pyx_GetAttr(PyObject *o, PyObject *n) {
#if CYTHON_USE_TYPE_SLOTS
#if PY_MAJOR_VERSION >= 3
    if (likely(PyUnicode_Check(n)))
#else
    if (likely(PyString_Check(n)))
#endif
        return __Pyx_PyObject_GetAttrStr(o, n);
#endif
    return PyObject_GetAttr(o, n);
}

/* GetAttr3 */
static PyObject *__Pyx_GetAttr3Default(PyObject *d) {
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    if (unlikely(!__Pyx_PyErr_ExceptionMatches(PyExc_AttributeError)))
        return NULL;
    __Pyx_PyErr_Clear();
    Py_INCREF(d);
    return d;
}
static CYTHON_INLINE PyObject *__Pyx_GetAttr3(PyObject *o, PyObject *n, PyObject *d) {
    PyObject *r = __Pyx_GetAttr(o, n);
    return (likely(r)) ? r : __Pyx_GetAttr3Default(d);
}

/* GetItemInt */
static PyObject *__Pyx_GetItemInt_Generic(PyObject *o, PyObject* j) {
    PyObject *r;
    if (!j) return NULL;
    r = PyObject_GetItem(o, j);
    Py_DECREF(j);
    return r;
}
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_List_Fast(PyObject *o, Py_ssize_t i,
                                                              CYTHON_NCP_UNUSED int wraparound,
                                                              CYTHON_NCP_UNUSED int boundscheck) {
#if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
    Py_ssize_t wrapped_i = i;
    if (wraparound & unlikely(i < 0)) {
        wrapped_i += PyList_GET_SIZE(o);
    }
    if ((!boundscheck) || likely(__Pyx_is_valid_index(wrapped_i, PyList_GET_SIZE(o)))) {
        PyObject *r = PyList_GET_ITEM(o, wrapped_i);
        Py_INCREF(r);
        return r;
    }
    return __Pyx_GetItemInt_Generic(o, PyInt_FromSsize_t(i));
#else
    return PySequence_GetItem(o, i);
#endif
}
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_Tuple_Fast(PyObject *o, Py_ssize_t i,
                                                              CYTHON_NCP_UNUSED int wraparound,
                                                              CYTHON_NCP_UNUSED int boundscheck) {
#if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS
    Py_ssize_t wrapped_i = i;
    if (wraparound & unlikely(i < 0)) {
        wrapped_i += PyTuple_GET_SIZE(o);
    }
    if ((!boundscheck) || likely(__Pyx_is_valid_index(wrapped_i, PyTuple_GET_SIZE(o)))) {
        PyObject *r = PyTuple_GET_ITEM(o, wrapped_i);
        Py_INCREF(r);
        return r;
    }
    return __Pyx_GetItemInt_Generic(o, PyInt_FromSsize_t(i));
#else
    return PySequence_GetItem(o, i);
#endif
}
static CYTHON_INLINE PyObject *__Pyx_GetItemInt_Fast(PyObject *o, Py_ssize_t i, int is_list,
                                                     CYTHON_NCP_UNUSED int wraparound,
                                                     CYTHON_NCP_UNUSED int boundscheck) {
#if CYTHON_ASSUME_SAFE_MACROS && !CYTHON_AVOID_BORROWED_REFS && CYTHON_USE_TYPE_SLOTS
    if (is_list || PyList_CheckExact(o)) {
        Py_ssize_t n = ((!wraparound) | likely(i >= 0)) ? i : i + PyList_GET_SIZE(o);
        if ((!boundscheck) || (likely(__Pyx_is_valid_index(n, PyList_GET_SIZE(o))))) {
            PyObject *r = PyList_GET_ITEM(o, n);
            Py_INCREF(r);
            return r;
        }
    }
    else if (PyTuple_CheckExact(o)) {
        Py_ssize_t n = ((!wraparound) | likely(i >= 0)) ? i : i + PyTuple_GET_SIZE(o);
        if ((!boundscheck) || likely(__Pyx_is_valid_index(n, PyTuple_GET_SIZE(o)))) {
            PyObject *r = PyTuple_GET_ITEM(o, n);
            Py_INCREF(r);
            return r;
        }
    } else {
        PySequenceMethods *m = Py_TYPE(o)->tp_as_sequence;
        if (likely(m && m->sq_item)) {
            if (wraparound && unlikely(i < 0) && likely(m->sq_length)) {
                Py_ssize_t l = m->sq_length(o);
                if (likely(l >= 0)) {
                    i += l;
                } else {
                    if (!PyErr_ExceptionMatches(PyExc_OverflowError))
                        return NULL;
                    PyErr_Clear();
                }
            }
            return m->sq_item(o, i);
        }
    }
#else
    if (is_list || PySequence_Check(o)) {
        return PySequence_GetItem(o, i);
    }
#endif
    return __Pyx_GetItemInt_Generic(o, PyInt_FromSsize_t(i));
}

/* ObjectGetItem */
#if CYTHON_USE_TYPE_SLOTS
static PyObject *__Pyx_PyObject_GetIndex(PyObject *obj, PyObject* index) {
    PyObject *runerr;
    Py_ssize_t key_value;
    PySequenceMethods *m = Py_TYPE(obj)->tp_as_sequence;
    if (unlikely(!(m && m->sq_item))) {
        PyErr_Format(PyExc_TypeError, "'%.200s' object is not subscriptable", Py_TYPE(obj)->tp_name);
        return NULL;
    }
    key_value = __Pyx_PyIndex_AsSsize_t(index);
    if (likely(key_value != -1 || !(runerr = PyErr_Occurred()))) {
        return __Pyx_GetItemInt_Fast(obj, key_value, 0, 1, 1);
    }
    if (PyErr_GivenExceptionMatches(runerr, PyExc_OverflowError)) {
        PyErr_Clear();
        PyErr_Format(PyExc_IndexError, "cannot fit '%.200s' into an index-sized integer", Py_TYPE(index)->tp_name);
    }
    return NULL;
}
static PyObject *__Pyx_PyObject_GetItem(PyObject *obj, PyObject* key) {
    PyMappingMethods *m = Py_TYPE(obj)->tp_as_mapping;
    if (likely(m && m->mp_subscript)) {
        return m->mp_subscript(obj, key);
    }
    return __Pyx_PyObject_GetIndex(obj, key);
}
#endif

/* PyIntBinop */
#if !CYTHON_COMPILING_IN_PYPY
static PyObject* __Pyx_PyInt_SubtractObjC(PyObject *op1, PyObject *op2, CYTHON_UNUSED long intval, int inplace, int zerodivision_check) {
    (void)inplace;
    (void)zerodivision_check;
    #if PY_MAJOR_VERSION < 3
    if (likely(PyInt_CheckExact(op1))) {
        const long b = intval;
        long x;
        long a = PyInt_AS_LONG(op1);
            x = (long)((unsigned long)a - b);
            if (likely((x^a) >= 0 || (x^~b) >= 0))
                return PyInt_FromLong(x);
            return PyLong_Type.tp_as_number->nb_subtract(op1, op2);
    }
    #endif
    #if CYTHON_USE_PYLONG_INTERNALS
    if (likely(PyLong_CheckExact(op1))) {
        const long b = intval;
        long a, x;
#ifdef HAVE_LONG_LONG
        const PY_LONG_LONG llb = intval;
        PY_LONG_LONG lla, llx;
#endif
        const digit* digits = ((PyLongObject*)op1)->ob_digit;
        const Py_ssize_t size = Py_SIZE(op1);
        if (likely(__Pyx_sst_abs(size) <= 1)) {
            a = likely(size) ? digits[0] : 0;
            if (size == -1) a = -a;
        } else {
            switch (size) {
                case -2:
                    if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                        a = -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));
                        break;
#ifdef HAVE_LONG_LONG
                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 2 * PyLong_SHIFT) {
                        lla = -(PY_LONG_LONG) (((((unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));
                        goto long_long;
#endif
                    }
                    CYTHON_FALLTHROUGH;
                case 2:
                    if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                        a = (long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));
                        break;
#ifdef HAVE_LONG_LONG
                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 2 * PyLong_SHIFT) {
                        lla = (PY_LONG_LONG) (((((unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));
                        goto long_long;
#endif
                    }
                    CYTHON_FALLTHROUGH;
                case -3:
                    if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                        a = -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));
                        break;
#ifdef HAVE_LONG_LONG
                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 3 * PyLong_SHIFT) {
                        lla = -(PY_LONG_LONG) (((((((unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));
                        goto long_long;
#endif
                    }
                    CYTHON_FALLTHROUGH;
                case 3:
                    if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                        a = (long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));
                        break;
#ifdef HAVE_LONG_LONG
                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 3 * PyLong_SHIFT) {
                        lla = (PY_LONG_LONG) (((((((unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));
                        goto long_long;
#endif
                    }
                    CYTHON_FALLTHROUGH;
                case -4:
                    if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {
                        a = -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));
                        break;
#ifdef HAVE_LONG_LONG
                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 4 * PyLong_SHIFT) {
                        lla = -(PY_LONG_LONG) (((((((((unsigned PY_LONG_LONG)digits[3]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));
                        goto long_long;
#endif
                    }
                    CYTHON_FALLTHROUGH;
                case 4:
                    if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {
                        a = (long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));
                        break;
#ifdef HAVE_LONG_LONG
                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 4 * PyLong_SHIFT) {
                        lla = (PY_LONG_LONG) (((((((((unsigned PY_LONG_LONG)digits[3]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));
                        goto long_long;
#endif
                    }
                    CYTHON_FALLTHROUGH;
                default: return PyLong_Type.tp_as_number->nb_subtract(op1, op2);
            }
        }
                x = a - b;
            return PyLong_FromLong(x);
#ifdef HAVE_LONG_LONG
        long_long:
                llx = lla - llb;
            return PyLong_FromLongLong(llx);
#endif
        
        
    }
    #endif
    if (PyFloat_CheckExact(op1)) {
        const long b = intval;
        double a = PyFloat_AS_DOUBLE(op1);
            double result;
            PyFPE_START_PROTECT("subtract", return NULL)
            result = ((double)a) - (double)b;
            PyFPE_END_PROTECT(result)
            return PyFloat_FromDouble(result);
    }
    return (inplace ? PyNumber_InPlaceSubtract : PyNumber_Subtract)(op1, op2);
}
#endif

/* PyIntBinop */
#if !CYTHON_COMPILING_IN_PYPY
static PyObject* __Pyx_PyInt_AddObjC(PyObject *op1, PyObject *op2, CYTHON_UNUSED long intval, int inplace, int zerodivision_check) {
    (void)inplace;
    (void)zerodivision_check;
    #if PY_MAJOR_VERSION < 3
    if (likely(PyInt_CheckExact(op1))) {
        const long b = intval;
        long x;
        long a = PyInt_AS_LONG(op1);
            x = (long)((unsigned long)a + b);
            if (likely((x^a) >= 0 || (x^b) >= 0))
                return PyInt_FromLong(x);
            return PyLong_Type.tp_as_number->nb_add(op1, op2);
    }
    #endif
    #if CYTHON_USE_PYLONG_INTERNALS
    if (likely(PyLong_CheckExact(op1))) {
        const long b = intval;
        long a, x;
#ifdef HAVE_LONG_LONG
        const PY_LONG_LONG llb = intval;
        PY_LONG_LONG lla, llx;
#endif
        const digit* digits = ((PyLongObject*)op1)->ob_digit;
        const Py_ssize_t size = Py_SIZE(op1);
        if (likely(__Pyx_sst_abs(size) <= 1)) {
            a = likely(size) ? digits[0] : 0;
            if (size == -1) a = -a;
        } else {
            switch (size) {
                case -2:
                    if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                        a = -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));
                        break;
#ifdef HAVE_LONG_LONG
                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 2 * PyLong_SHIFT) {
                        lla = -(PY_LONG_LONG) (((((unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));
                        goto long_long;
#endif
                    }
                    CYTHON_FALLTHROUGH;
                case 2:
                    if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                        a = (long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));
                        break;
#ifdef HAVE_LONG_LONG
                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 2 * PyLong_SHIFT) {
                        lla = (PY_LONG_LONG) (((((unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));
                        goto long_long;
#endif
                    }
                    CYTHON_FALLTHROUGH;
                case -3:
                    if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                        a = -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));
                        break;
#ifdef HAVE_LONG_LONG
                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 3 * PyLong_SHIFT) {
                        lla = -(PY_LONG_LONG) (((((((unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));
                        goto long_long;
#endif
                    }
                    CYTHON_FALLTHROUGH;
                case 3:
                    if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                        a = (long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));
                        break;
#ifdef HAVE_LONG_LONG
                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 3 * PyLong_SHIFT) {
                        lla = (PY_LONG_LONG) (((((((unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));
                        goto long_long;
#endif
                    }
                    CYTHON_FALLTHROUGH;
                case -4:
                    if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {
                        a = -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));
                        break;
#ifdef HAVE_LONG_LONG
                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 4 * PyLong_SHIFT) {
                        lla = -(PY_LONG_LONG) (((((((((unsigned PY_LONG_LONG)digits[3]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));
                        goto long_long;
#endif
                    }
                    CYTHON_FALLTHROUGH;
                case 4:
                    if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {
                        a = (long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0]));
                        break;
#ifdef HAVE_LONG_LONG
                    } else if (8 * sizeof(PY_LONG_LONG) - 1 > 4 * PyLong_SHIFT) {
                        lla = (PY_LONG_LONG) (((((((((unsigned PY_LONG_LONG)digits[3]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[2]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[1]) << PyLong_SHIFT) | (unsigned PY_LONG_LONG)digits[0]));
                        goto long_long;
#endif
                    }
                    CYTHON_FALLTHROUGH;
                default: return PyLong_Type.tp_as_number->nb_add(op1, op2);
            }
        }
                x = a + b;
            return PyLong_FromLong(x);
#ifdef HAVE_LONG_LONG
        long_long:
                llx = lla + llb;
            return PyLong_FromLongLong(llx);
#endif
        
        
    }
    #endif
    if (PyFloat_CheckExact(op1)) {
        const long b = intval;
        double a = PyFloat_AS_DOUBLE(op1);
            double result;
            PyFPE_START_PROTECT("add", return NULL)
            result = ((double)a) + (double)b;
            PyFPE_END_PROTECT(result)
            return PyFloat_FromDouble(result);
    }
    return (inplace ? PyNumber_InPlaceAdd : PyNumber_Add)(op1, op2);
}
#endif

/* BytesEquals */
static CYTHON_INLINE int __Pyx_PyBytes_Equals(PyObject* s1, PyObject* s2, int equals) {
#if CYTHON_COMPILING_IN_PYPY
    return PyObject_RichCompareBool(s1, s2, equals);
#else
    if (s1 == s2) {
        return (equals == Py_EQ);
    } else if (PyBytes_CheckExact(s1) & PyBytes_CheckExact(s2)) {
        const char *ps1, *ps2;
        Py_ssize_t length = PyBytes_GET_SIZE(s1);
        if (length != PyBytes_GET_SIZE(s2))
            return (equals == Py_NE);
        ps1 = PyBytes_AS_STRING(s1);
        ps2 = PyBytes_AS_STRING(s2);
        if (ps1[0] != ps2[0]) {
            return (equals == Py_NE);
        } else if (length == 1) {
            return (equals == Py_EQ);
        } else {
            int result;
#if CYTHON_USE_UNICODE_INTERNALS
            Py_hash_t hash1, hash2;
            hash1 = ((PyBytesObject*)s1)->ob_shash;
            hash2 = ((PyBytesObject*)s2)->ob_shash;
            if (hash1 != hash2 && hash1 != -1 && hash2 != -1) {
                return (equals == Py_NE);
            }
#endif
            result = memcmp(ps1, ps2, (size_t)length);
            return (equals == Py_EQ) ? (result == 0) : (result != 0);
        }
    } else if ((s1 == Py_None) & PyBytes_CheckExact(s2)) {
        return (equals == Py_NE);
    } else if ((s2 == Py_None) & PyBytes_CheckExact(s1)) {
        return (equals == Py_NE);
    } else {
        int result;
        PyObject* py_result = PyObject_RichCompare(s1, s2, equals);
        if (!py_result)
            return -1;
        result = __Pyx_PyObject_IsTrue(py_result);
        Py_DECREF(py_result);
        return result;
    }
#endif
}

/* UnicodeEquals */
static CYTHON_INLINE int __Pyx_PyUnicode_Equals(PyObject* s1, PyObject* s2, int equals) {
#if CYTHON_COMPILING_IN_PYPY
    return PyObject_RichCompareBool(s1, s2, equals);
#else
#if PY_MAJOR_VERSION < 3
    PyObject* owned_ref = NULL;
#endif
    int s1_is_unicode, s2_is_unicode;
    if (s1 == s2) {
        goto return_eq;
    }
    s1_is_unicode = PyUnicode_CheckExact(s1);
    s2_is_unicode = PyUnicode_CheckExact(s2);
#if PY_MAJOR_VERSION < 3
    if ((s1_is_unicode & (!s2_is_unicode)) && PyString_CheckExact(s2)) {
        owned_ref = PyUnicode_FromObject(s2);
        if (unlikely(!owned_ref))
            return -1;
        s2 = owned_ref;
        s2_is_unicode = 1;
    } else if ((s2_is_unicode & (!s1_is_unicode)) && PyString_CheckExact(s1)) {
        owned_ref = PyUnicode_FromObject(s1);
        if (unlikely(!owned_ref))
            return -1;
        s1 = owned_ref;
        s1_is_unicode = 1;
    } else if (((!s2_is_unicode) & (!s1_is_unicode))) {
        return __Pyx_PyBytes_Equals(s1, s2, equals);
    }
#endif
    if (s1_is_unicode & s2_is_unicode) {
        Py_ssize_t length;
        int kind;
        void *data1, *data2;
        if (unlikely(__Pyx_PyUnicode_READY(s1) < 0) || unlikely(__Pyx_PyUnicode_READY(s2) < 0))
            return -1;
        length = __Pyx_PyUnicode_GET_LENGTH(s1);
        if (length != __Pyx_PyUnicode_GET_LENGTH(s2)) {
            goto return_ne;
        }
#if CYTHON_USE_UNICODE_INTERNALS
        {
            Py_hash_t hash1, hash2;
        #if CYTHON_PEP393_ENABLED
            hash1 = ((PyASCIIObject*)s1)->hash;
            hash2 = ((PyASCIIObject*)s2)->hash;
        #else
            hash1 = ((PyUnicodeObject*)s1)->hash;
            hash2 = ((PyUnicodeObject*)s2)->hash;
        #endif
            if (hash1 != hash2 && hash1 != -1 && hash2 != -1) {
                goto return_ne;
            }
        }
#endif
        kind = __Pyx_PyUnicode_KIND(s1);
        if (kind != __Pyx_PyUnicode_KIND(s2)) {
            goto return_ne;
        }
        data1 = __Pyx_PyUnicode_DATA(s1);
        data2 = __Pyx_PyUnicode_DATA(s2);
        if (__Pyx_PyUnicode_READ(kind, data1, 0) != __Pyx_PyUnicode_READ(kind, data2, 0)) {
            goto return_ne;
        } else if (length == 1) {
            goto return_eq;
        } else {
            int result = memcmp(data1, data2, (size_t)(length * kind));
            #if PY_MAJOR_VERSION < 3
            Py_XDECREF(owned_ref);
            #endif
            return (equals == Py_EQ) ? (result == 0) : (result != 0);
        }
    } else if ((s1 == Py_None) & s2_is_unicode) {
        goto return_ne;
    } else if ((s2 == Py_None) & s1_is_unicode) {
        goto return_ne;
    } else {
        int result;
        PyObject* py_result = PyObject_RichCompare(s1, s2, equals);
        #if PY_MAJOR_VERSION < 3
        Py_XDECREF(owned_ref);
        #endif
        if (!py_result)
            return -1;
        result = __Pyx_PyObject_IsTrue(py_result);
        Py_DECREF(py_result);
        return result;
    }
return_eq:
    #if PY_MAJOR_VERSION < 3
    Py_XDECREF(owned_ref);
    #endif
    return (equals == Py_EQ);
return_ne:
    #if PY_MAJOR_VERSION < 3
    Py_XDECREF(owned_ref);
    #endif
    return (equals == Py_NE);
#endif
}

/* GetItemIntUnicode */
static CYTHON_INLINE Py_UCS4 __Pyx_GetItemInt_Unicode_Fast(PyObject* ustring, Py_ssize_t i,
                                                           int wraparound, int boundscheck) {
    Py_ssize_t length;
    if (unlikely(__Pyx_PyUnicode_READY(ustring) < 0)) return (Py_UCS4)-1;
    if (wraparound | boundscheck) {
        length = __Pyx_PyUnicode_GET_LENGTH(ustring);
        if (wraparound & unlikely(i < 0)) i += length;
        if ((!boundscheck) || likely(__Pyx_is_valid_index(i, length))) {
            return __Pyx_PyUnicode_READ_CHAR(ustring, i);
        } else {
            PyErr_SetString(PyExc_IndexError, "string index out of range");
            return (Py_UCS4)-1;
        }
    } else {
        return __Pyx_PyUnicode_READ_CHAR(ustring, i);
    }
}

/* PyUnicode_Substring */
static CYTHON_INLINE PyObject* __Pyx_PyUnicode_Substring(
            PyObject* text, Py_ssize_t start, Py_ssize_t stop) {
    Py_ssize_t length;
    if (unlikely(__Pyx_PyUnicode_READY(text) == -1)) return NULL;
    length = __Pyx_PyUnicode_GET_LENGTH(text);
    if (start < 0) {
        start += length;
        if (start < 0)
            start = 0;
    }
    if (stop < 0)
        stop += length;
    else if (stop > length)
        stop = length;
    length = stop - start;
    if (length <= 0)
        return PyUnicode_FromUnicode(NULL, 0);
#if CYTHON_PEP393_ENABLED
    return PyUnicode_FromKindAndData(PyUnicode_KIND(text),
        PyUnicode_1BYTE_DATA(text) + start*PyUnicode_KIND(text), stop-start);
#else
    return PyUnicode_FromUnicode(PyUnicode_AS_UNICODE(text)+start, stop-start);
#endif
}

/* unicode_iter */
static CYTHON_INLINE int __Pyx_init_unicode_iteration(
    PyObject* ustring, Py_ssize_t *length, void** data, int *kind) {
#if CYTHON_PEP393_ENABLED
    if (unlikely(__Pyx_PyUnicode_READY(ustring) < 0)) return -1;
    *kind   = PyUnicode_KIND(ustring);
    *length = PyUnicode_GET_LENGTH(ustring);
    *data   = PyUnicode_DATA(ustring);
#else
    *kind   = 0;
    *length = PyUnicode_GET_SIZE(ustring);
    *data   = (void*)PyUnicode_AS_UNICODE(ustring);
#endif
    return 0;
}

/* ArgTypeTest */
static int __Pyx__ArgTypeTest(PyObject *obj, PyTypeObject *type, const char *name, int exact)
{
    if (unlikely(!type)) {
        PyErr_SetString(PyExc_SystemError, "Missing type object");
        return 0;
    }
    else if (exact) {
        #if PY_MAJOR_VERSION == 2
        if ((type == &PyBaseString_Type) && likely(__Pyx_PyBaseString_CheckExact(obj))) return 1;
        #endif
    }
    else {
        if (likely(__Pyx_TypeCheck(obj, type))) return 1;
    }
    PyErr_Format(PyExc_TypeError,
        "Argument '%.200s' has incorrect type (expected %.200s, got %.200s)",
        name, type->tp_name, Py_TYPE(obj)->tp_name);
    return 0;
}

/* IterNext */
static PyObject *__Pyx_PyIter_Next2Default(PyObject* defval) {
    PyObject* exc_type;
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    exc_type = __Pyx_PyErr_Occurred();
    if (unlikely(exc_type)) {
        if (!defval || unlikely(!__Pyx_PyErr_GivenExceptionMatches(exc_type, PyExc_StopIteration)))
            return NULL;
        __Pyx_PyErr_Clear();
        Py_INCREF(defval);
        return defval;
    }
    if (defval) {
        Py_INCREF(defval);
        return defval;
    }
    __Pyx_PyErr_SetNone(PyExc_StopIteration);
    return NULL;
}
static void __Pyx_PyIter_Next_ErrorNoIterator(PyObject *iterator) {
    PyErr_Format(PyExc_TypeError,
        "%.200s object is not an iterator", Py_TYPE(iterator)->tp_name);
}
static CYTHON_INLINE PyObject *__Pyx_PyIter_Next2(PyObject* iterator, PyObject* defval) {
    PyObject* next;
    iternextfunc iternext = Py_TYPE(iterator)->tp_iternext;
    if (likely(iternext)) {
#if CYTHON_USE_TYPE_SLOTS
        next = iternext(iterator);
        if (likely(next))
            return next;
        #if PY_VERSION_HEX >= 0x02070000
        if (unlikely(iternext == &_PyObject_NextNotImplemented))
            return NULL;
        #endif
#else
        next = PyIter_Next(iterator);
        if (likely(next))
            return next;
#endif
    } else if (CYTHON_USE_TYPE_SLOTS || unlikely(!PyIter_Check(iterator))) {
        __Pyx_PyIter_Next_ErrorNoIterator(iterator);
        return NULL;
    }
#if !CYTHON_USE_TYPE_SLOTS
    else {
        next = PyIter_Next(iterator);
        if (likely(next))
            return next;
    }
#endif
    return __Pyx_PyIter_Next2Default(defval);
}

/* GetTopmostException */
#if CYTHON_USE_EXC_INFO_STACK
static _PyErr_StackItem *
__Pyx_PyErr_GetTopmostException(PyThreadState *tstate)
{
    _PyErr_StackItem *exc_info = tstate->exc_info;
    while ((exc_info->exc_type == NULL || exc_info->exc_type == Py_None) &&
           exc_info->previous_item != NULL)
    {
        exc_info = exc_info->previous_item;
    }
    return exc_info;
}
#endif

/* SaveResetException */
#if CYTHON_FAST_THREAD_STATE
static CYTHON_INLINE void __Pyx__ExceptionSave(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb) {
    #if CYTHON_USE_EXC_INFO_STACK
    _PyErr_StackItem *exc_info = __Pyx_PyErr_GetTopmostException(tstate);
    *type = exc_info->exc_type;
    *value = exc_info->exc_value;
    *tb = exc_info->exc_traceback;
    #else
    *type = tstate->exc_type;
    *value = tstate->exc_value;
    *tb = tstate->exc_traceback;
    #endif
    Py_XINCREF(*type);
    Py_XINCREF(*value);
    Py_XINCREF(*tb);
}
static CYTHON_INLINE void __Pyx__ExceptionReset(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb) {
    PyObject *tmp_type, *tmp_value, *tmp_tb;
    #if CYTHON_USE_EXC_INFO_STACK
    _PyErr_StackItem *exc_info = tstate->exc_info;
    tmp_type = exc_info->exc_type;
    tmp_value = exc_info->exc_value;
    tmp_tb = exc_info->exc_traceback;
    exc_info->exc_type = type;
    exc_info->exc_value = value;
    exc_info->exc_traceback = tb;
    #else
    tmp_type = tstate->exc_type;
    tmp_value = tstate->exc_value;
    tmp_tb = tstate->exc_traceback;
    tstate->exc_type = type;
    tstate->exc_value = value;
    tstate->exc_traceback = tb;
    #endif
    Py_XDECREF(tmp_type);
    Py_XDECREF(tmp_value);
    Py_XDECREF(tmp_tb);
}
#endif

/* GetException */
#if CYTHON_FAST_THREAD_STATE
static int __Pyx__GetException(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb)
#else
static int __Pyx_GetException(PyObject **type, PyObject **value, PyObject **tb)
#endif
{
    PyObject *local_type, *local_value, *local_tb;
#if CYTHON_FAST_THREAD_STATE
    PyObject *tmp_type, *tmp_value, *tmp_tb;
    local_type = tstate->curexc_type;
    local_value = tstate->curexc_value;
    local_tb = tstate->curexc_traceback;
    tstate->curexc_type = 0;
    tstate->curexc_value = 0;
    tstate->curexc_traceback = 0;
#else
    PyErr_Fetch(&local_type, &local_value, &local_tb);
#endif
    PyErr_NormalizeException(&local_type, &local_value, &local_tb);
#if CYTHON_FAST_THREAD_STATE
    if (unlikely(tstate->curexc_type))
#else
    if (unlikely(PyErr_Occurred()))
#endif
        goto bad;
    #if PY_MAJOR_VERSION >= 3
    if (local_tb) {
        if (unlikely(PyException_SetTraceback(local_value, local_tb) < 0))
            goto bad;
    }
    #endif
    Py_XINCREF(local_tb);
    Py_XINCREF(local_type);
    Py_XINCREF(local_value);
    *type = local_type;
    *value = local_value;
    *tb = local_tb;
#if CYTHON_FAST_THREAD_STATE
    #if CYTHON_USE_EXC_INFO_STACK
    {
        _PyErr_StackItem *exc_info = tstate->exc_info;
        tmp_type = exc_info->exc_type;
        tmp_value = exc_info->exc_value;
        tmp_tb = exc_info->exc_traceback;
        exc_info->exc_type = local_type;
        exc_info->exc_value = local_value;
        exc_info->exc_traceback = local_tb;
    }
    #else
    tmp_type = tstate->exc_type;
    tmp_value = tstate->exc_value;
    tmp_tb = tstate->exc_traceback;
    tstate->exc_type = local_type;
    tstate->exc_value = local_value;
    tstate->exc_traceback = local_tb;
    #endif
    Py_XDECREF(tmp_type);
    Py_XDECREF(tmp_value);
    Py_XDECREF(tmp_tb);
#else
    PyErr_SetExcInfo(local_type, local_value, local_tb);
#endif
    return 0;
bad:
    *type = 0;
    *value = 0;
    *tb = 0;
    Py_XDECREF(local_type);
    Py_XDECREF(local_value);
    Py_XDECREF(local_tb);
    return -1;
}

/* RaiseException */
#if PY_MAJOR_VERSION < 3
static void __Pyx_Raise(PyObject *type, PyObject *value, PyObject *tb,
                        CYTHON_UNUSED PyObject *cause) {
    __Pyx_PyThreadState_declare
    Py_XINCREF(type);
    if (!value || value == Py_None)
        value = NULL;
    else
        Py_INCREF(value);
    if (!tb || tb == Py_None)
        tb = NULL;
    else {
        Py_INCREF(tb);
        if (!PyTraceBack_Check(tb)) {
            PyErr_SetString(PyExc_TypeError,
                "raise: arg 3 must be a traceback or None");
            goto raise_error;
        }
    }
    if (PyType_Check(type)) {
#if CYTHON_COMPILING_IN_PYPY
        if (!value) {
            Py_INCREF(Py_None);
            value = Py_None;
        }
#endif
        PyErr_NormalizeException(&type, &value, &tb);
    } else {
        if (value) {
            PyErr_SetString(PyExc_TypeError,
                "instance exception may not have a separate value");
            goto raise_error;
        }
        value = type;
        type = (PyObject*) Py_TYPE(type);
        Py_INCREF(type);
        if (!PyType_IsSubtype((PyTypeObject *)type, (PyTypeObject *)PyExc_BaseException)) {
            PyErr_SetString(PyExc_TypeError,
                "raise: exception class must be a subclass of BaseException");
            goto raise_error;
        }
    }
    __Pyx_PyThreadState_assign
    __Pyx_ErrRestore(type, value, tb);
    return;
raise_error:
    Py_XDECREF(value);
    Py_XDECREF(type);
    Py_XDECREF(tb);
    return;
}
#else
static void __Pyx_Raise(PyObject *type, PyObject *value, PyObject *tb, PyObject *cause) {
    PyObject* owned_instance = NULL;
    if (tb == Py_None) {
        tb = 0;
    } else if (tb && !PyTraceBack_Check(tb)) {
        PyErr_SetString(PyExc_TypeError,
            "raise: arg 3 must be a traceback or None");
        goto bad;
    }
    if (value == Py_None)
        value = 0;
    if (PyExceptionInstance_Check(type)) {
        if (value) {
            PyErr_SetString(PyExc_TypeError,
                "instance exception may not have a separate value");
            goto bad;
        }
        value = type;
        type = (PyObject*) Py_TYPE(value);
    } else if (PyExceptionClass_Check(type)) {
        PyObject *instance_class = NULL;
        if (value && PyExceptionInstance_Check(value)) {
            instance_class = (PyObject*) Py_TYPE(value);
            if (instance_class != type) {
                int is_subclass = PyObject_IsSubclass(instance_class, type);
                if (!is_subclass) {
                    instance_class = NULL;
                } else if (unlikely(is_subclass == -1)) {
                    goto bad;
                } else {
                    type = instance_class;
                }
            }
        }
        if (!instance_class) {
            PyObject *args;
            if (!value)
                args = PyTuple_New(0);
            else if (PyTuple_Check(value)) {
                Py_INCREF(value);
                args = value;
            } else
                args = PyTuple_Pack(1, value);
            if (!args)
                goto bad;
            owned_instance = PyObject_Call(type, args, NULL);
            Py_DECREF(args);
            if (!owned_instance)
                goto bad;
            value = owned_instance;
            if (!PyExceptionInstance_Check(value)) {
                PyErr_Format(PyExc_TypeError,
                             "calling %R should have returned an instance of "
                             "BaseException, not %R",
                             type, Py_TYPE(value));
                goto bad;
            }
        }
    } else {
        PyErr_SetString(PyExc_TypeError,
            "raise: exception class must be a subclass of BaseException");
        goto bad;
    }
    if (cause) {
        PyObject *fixed_cause;
        if (cause == Py_None) {
            fixed_cause = NULL;
        } else if (PyExceptionClass_Check(cause)) {
            fixed_cause = PyObject_CallObject(cause, NULL);
            if (fixed_cause == NULL)
                goto bad;
        } else if (PyExceptionInstance_Check(cause)) {
            fixed_cause = cause;
            Py_INCREF(fixed_cause);
        } else {
            PyErr_SetString(PyExc_TypeError,
                            "exception causes must derive from "
                            "BaseException");
            goto bad;
        }
        PyException_SetCause(value, fixed_cause);
    }
    PyErr_SetObject(type, value);
    if (tb) {
#if CYTHON_COMPILING_IN_PYPY
        PyObject *tmp_type, *tmp_value, *tmp_tb;
        PyErr_Fetch(&tmp_type, &tmp_value, &tmp_tb);
        Py_INCREF(tb);
        PyErr_Restore(tmp_type, tmp_value, tb);
        Py_XDECREF(tmp_tb);
#else
        PyThreadState *tstate = __Pyx_PyThreadState_Current;
        PyObject* tmp_tb = tstate->curexc_traceback;
        if (tb != tmp_tb) {
            Py_INCREF(tb);
            tstate->curexc_traceback = tb;
            Py_XDECREF(tmp_tb);
        }
#endif
    }
bad:
    Py_XDECREF(owned_instance);
    return;
}
#endif

/* Import */
static PyObject *__Pyx_Import(PyObject *name, PyObject *from_list, int level) {
    PyObject *empty_list = 0;
    PyObject *module = 0;
    PyObject *global_dict = 0;
    PyObject *empty_dict = 0;
    PyObject *list;
    #if PY_MAJOR_VERSION < 3
    PyObject *py_import;
    py_import = __Pyx_PyObject_GetAttrStr(__pyx_b, __pyx_n_s_import);
    if (!py_import)
        goto bad;
    #endif
    if (from_list)
        list = from_list;
    else {
        empty_list = PyList_New(0);
        if (!empty_list)
            goto bad;
        list = empty_list;
    }
    global_dict = PyModule_GetDict(__pyx_m);
    if (!global_dict)
        goto bad;
    empty_dict = PyDict_New();
    if (!empty_dict)
        goto bad;
    {
        #if PY_MAJOR_VERSION >= 3
        if (level == -1) {
            if (strchr(__Pyx_MODULE_NAME, '.')) {
                module = PyImport_ImportModuleLevelObject(
                    name, global_dict, empty_dict, list, 1);
                if (!module) {
                    if (!PyErr_ExceptionMatches(PyExc_ImportError))
                        goto bad;
                    PyErr_Clear();
                }
            }
            level = 0;
        }
        #endif
        if (!module) {
            #if PY_MAJOR_VERSION < 3
            PyObject *py_level = PyInt_FromLong(level);
            if (!py_level)
                goto bad;
            module = PyObject_CallFunctionObjArgs(py_import,
                name, global_dict, empty_dict, list, py_level, (PyObject *)NULL);
            Py_DECREF(py_level);
            #else
            module = PyImport_ImportModuleLevelObject(
                name, global_dict, empty_dict, list, level);
            #endif
        }
    }
bad:
    #if PY_MAJOR_VERSION < 3
    Py_XDECREF(py_import);
    #endif
    Py_XDECREF(empty_list);
    Py_XDECREF(empty_dict);
    return module;
}

/* ImportFrom */
static PyObject* __Pyx_ImportFrom(PyObject* module, PyObject* name) {
    PyObject* value = __Pyx_PyObject_GetAttrStr(module, name);
    if (unlikely(!value) && PyErr_ExceptionMatches(PyExc_AttributeError)) {
        PyErr_Format(PyExc_ImportError,
        #if PY_MAJOR_VERSION < 3
            "cannot import name %.230s", PyString_AS_STRING(name));
        #else
            "cannot import name %S", name);
        #endif
    }
    return value;
}

/* HasAttr */
static CYTHON_INLINE int __Pyx_HasAttr(PyObject *o, PyObject *n) {
    PyObject *r;
    if (unlikely(!__Pyx_PyBaseString_Check(n))) {
        PyErr_SetString(PyExc_TypeError,
                        "hasattr(): attribute name must be string");
        return -1;
    }
    r = __Pyx_GetAttr(o, n);
    if (unlikely(!r)) {
        PyErr_Clear();
        return 0;
    } else {
        Py_DECREF(r);
        return 1;
    }
}

/* PyObject_GenericGetAttrNoDict */
#if CYTHON_USE_TYPE_SLOTS && CYTHON_USE_PYTYPE_LOOKUP && PY_VERSION_HEX < 0x03070000
static PyObject *__Pyx_RaiseGenericGetAttributeError(PyTypeObject *tp, PyObject *attr_name) {
    PyErr_Format(PyExc_AttributeError,
#if PY_MAJOR_VERSION >= 3
                 "'%.50s' object has no attribute '%U'",
                 tp->tp_name, attr_name);
#else
                 "'%.50s' object has no attribute '%.400s'",
                 tp->tp_name, PyString_AS_STRING(attr_name));
#endif
    return NULL;
}
static CYTHON_INLINE PyObject* __Pyx_PyObject_GenericGetAttrNoDict(PyObject* obj, PyObject* attr_name) {
    PyObject *descr;
    PyTypeObject *tp = Py_TYPE(obj);
    if (unlikely(!PyString_Check(attr_name))) {
        return PyObject_GenericGetAttr(obj, attr_name);
    }
    assert(!tp->tp_dictoffset);
    descr = _PyType_Lookup(tp, attr_name);
    if (unlikely(!descr)) {
        return __Pyx_RaiseGenericGetAttributeError(tp, attr_name);
    }
    Py_INCREF(descr);
    #if PY_MAJOR_VERSION < 3
    if (likely(PyType_HasFeature(Py_TYPE(descr), Py_TPFLAGS_HAVE_CLASS)))
    #endif
    {
        descrgetfunc f = Py_TYPE(descr)->tp_descr_get;
        if (unlikely(f)) {
            PyObject *res = f(descr, obj, (PyObject *)tp);
            Py_DECREF(descr);
            return res;
        }
    }
    return descr;
}
#endif

/* PyObject_GenericGetAttr */
#if CYTHON_USE_TYPE_SLOTS && CYTHON_USE_PYTYPE_LOOKUP && PY_VERSION_HEX < 0x03070000
static PyObject* __Pyx_PyObject_GenericGetAttr(PyObject* obj, PyObject* attr_name) {
    if (unlikely(Py_TYPE(obj)->tp_dictoffset)) {
        return PyObject_GenericGetAttr(obj, attr_name);
    }
    return __Pyx_PyObject_GenericGetAttrNoDict(obj, attr_name);
}
#endif

/* SetupReduce */
static int __Pyx_setup_reduce_is_named(PyObject* meth, PyObject* name) {
  int ret;
  PyObject *name_attr;
  name_attr = __Pyx_PyObject_GetAttrStr(meth, __pyx_n_s_name);
  if (likely(name_attr)) {
      ret = PyObject_RichCompareBool(name_attr, name, Py_EQ);
  } else {
      ret = -1;
  }
  if (unlikely(ret < 0)) {
      PyErr_Clear();
      ret = 0;
  }
  Py_XDECREF(name_attr);
  return ret;
}
static int __Pyx_setup_reduce(PyObject* type_obj) {
    int ret = 0;
    PyObject *object_reduce = NULL;
    PyObject *object_reduce_ex = NULL;
    PyObject *reduce = NULL;
    PyObject *reduce_ex = NULL;
    PyObject *reduce_cython = NULL;
    PyObject *setstate = NULL;
    PyObject *setstate_cython = NULL;
#if CYTHON_USE_PYTYPE_LOOKUP
    if (_PyType_Lookup((PyTypeObject*)type_obj, __pyx_n_s_getstate)) goto GOOD;
#else
    if (PyObject_HasAttr(type_obj, __pyx_n_s_getstate)) goto GOOD;
#endif
#if CYTHON_USE_PYTYPE_LOOKUP
    object_reduce_ex = _PyType_Lookup(&PyBaseObject_Type, __pyx_n_s_reduce_ex); if (!object_reduce_ex) goto BAD;
#else
    object_reduce_ex = __Pyx_PyObject_GetAttrStr((PyObject*)&PyBaseObject_Type, __pyx_n_s_reduce_ex); if (!object_reduce_ex) goto BAD;
#endif
    reduce_ex = __Pyx_PyObject_GetAttrStr(type_obj, __pyx_n_s_reduce_ex); if (unlikely(!reduce_ex)) goto BAD;
    if (reduce_ex == object_reduce_ex) {
#if CYTHON_USE_PYTYPE_LOOKUP
        object_reduce = _PyType_Lookup(&PyBaseObject_Type, __pyx_n_s_reduce); if (!object_reduce) goto BAD;
#else
        object_reduce = __Pyx_PyObject_GetAttrStr((PyObject*)&PyBaseObject_Type, __pyx_n_s_reduce); if (!object_reduce) goto BAD;
#endif
        reduce = __Pyx_PyObject_GetAttrStr(type_obj, __pyx_n_s_reduce); if (unlikely(!reduce)) goto BAD;
        if (reduce == object_reduce || __Pyx_setup_reduce_is_named(reduce, __pyx_n_s_reduce_cython)) {
            reduce_cython = __Pyx_PyObject_GetAttrStr(type_obj, __pyx_n_s_reduce_cython); if (unlikely(!reduce_cython)) goto BAD;
            ret = PyDict_SetItem(((PyTypeObject*)type_obj)->tp_dict, __pyx_n_s_reduce, reduce_cython); if (unlikely(ret < 0)) goto BAD;
            ret = PyDict_DelItem(((PyTypeObject*)type_obj)->tp_dict, __pyx_n_s_reduce_cython); if (unlikely(ret < 0)) goto BAD;
            setstate = __Pyx_PyObject_GetAttrStr(type_obj, __pyx_n_s_setstate);
            if (!setstate) PyErr_Clear();
            if (!setstate || __Pyx_setup_reduce_is_named(setstate, __pyx_n_s_setstate_cython)) {
                setstate_cython = __Pyx_PyObject_GetAttrStr(type_obj, __pyx_n_s_setstate_cython); if (unlikely(!setstate_cython)) goto BAD;
                ret = PyDict_SetItem(((PyTypeObject*)type_obj)->tp_dict, __pyx_n_s_setstate, setstate_cython); if (unlikely(ret < 0)) goto BAD;
                ret = PyDict_DelItem(((PyTypeObject*)type_obj)->tp_dict, __pyx_n_s_setstate_cython); if (unlikely(ret < 0)) goto BAD;
            }
            PyType_Modified((PyTypeObject*)type_obj);
        }
    }
    goto GOOD;
BAD:
    if (!PyErr_Occurred())
        PyErr_Format(PyExc_RuntimeError, "Unable to initialize pickling for %s", ((PyTypeObject*)type_obj)->tp_name);
    ret = -1;
GOOD:
#if !CYTHON_USE_PYTYPE_LOOKUP
    Py_XDECREF(object_reduce);
    Py_XDECREF(object_reduce_ex);
#endif
    Py_XDECREF(reduce);
    Py_XDECREF(reduce_ex);
    Py_XDECREF(reduce_cython);
    Py_XDECREF(setstate);
    Py_XDECREF(setstate_cython);
    return ret;
}

/* SetVTable */
static int __Pyx_SetVtable(PyObject *dict, void *vtable) {
#if PY_VERSION_HEX >= 0x02070000
    PyObject *ob = PyCapsule_New(vtable, 0, 0);
#else
    PyObject *ob = PyCObject_FromVoidPtr(vtable, 0);
#endif
    if (!ob)
        goto bad;
    if (PyDict_SetItem(dict, __pyx_n_s_pyx_vtable, ob) < 0)
        goto bad;
    Py_DECREF(ob);
    return 0;
bad:
    Py_XDECREF(ob);
    return -1;
}

/* ClassMethod */
static PyObject* __Pyx_Method_ClassMethod(PyObject *method) {
#if CYTHON_COMPILING_IN_PYPY && PYPY_VERSION_NUM <= 0x05080000
    if (PyObject_TypeCheck(method, &PyWrapperDescr_Type)) {
        return PyClassMethod_New(method);
    }
#else
#if CYTHON_COMPILING_IN_PYSTON || CYTHON_COMPILING_IN_PYPY
    if (PyMethodDescr_Check(method))
#else
    static PyTypeObject *methoddescr_type = NULL;
    if (methoddescr_type == NULL) {
       PyObject *meth = PyObject_GetAttrString((PyObject*)&PyList_Type, "append");
       if (!meth) return NULL;
       methoddescr_type = Py_TYPE(meth);
       Py_DECREF(meth);
    }
    if (__Pyx_TypeCheck(method, methoddescr_type))
#endif
    {
        PyMethodDescrObject *descr = (PyMethodDescrObject *)method;
        #if PY_VERSION_HEX < 0x03020000
        PyTypeObject *d_type = descr->d_type;
        #else
        PyTypeObject *d_type = descr->d_common.d_type;
        #endif
        return PyDescr_NewClassMethod(d_type, descr->d_method);
    }
#endif
    else if (PyMethod_Check(method)) {
        return PyClassMethod_New(PyMethod_GET_FUNCTION(method));
    }
    else if (PyCFunction_Check(method)) {
        return PyClassMethod_New(method);
    }
#ifdef __Pyx_CyFunction_USED
    else if (__Pyx_CyFunction_Check(method)) {
        return PyClassMethod_New(method);
    }
#endif
    PyErr_SetString(PyExc_TypeError,
                   "Class-level classmethod() can only be called on "
                   "a method_descriptor or instance method.");
    return NULL;
}

/* GetNameInClass */
static PyObject *__Pyx_GetGlobalNameAfterAttributeLookup(PyObject *name) {
    PyObject *result;
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    if (unlikely(!__Pyx_PyErr_ExceptionMatches(PyExc_AttributeError)))
        return NULL;
    __Pyx_PyErr_Clear();
    __Pyx_GetModuleGlobalNameUncached(result, name);
    return result;
}
static PyObject *__Pyx__GetNameInClass(PyObject *nmspace, PyObject *name) {
    PyObject *result;
    result = __Pyx_PyObject_GetAttrStr(nmspace, name);
    if (!result) {
        result = __Pyx_GetGlobalNameAfterAttributeLookup(name);
    }
    return result;
}

/* CalculateMetaclass */
static PyObject *__Pyx_CalculateMetaclass(PyTypeObject *metaclass, PyObject *bases) {
    Py_ssize_t i, nbases = PyTuple_GET_SIZE(bases);
    for (i=0; i < nbases; i++) {
        PyTypeObject *tmptype;
        PyObject *tmp = PyTuple_GET_ITEM(bases, i);
        tmptype = Py_TYPE(tmp);
#if PY_MAJOR_VERSION < 3
        if (tmptype == &PyClass_Type)
            continue;
#endif
        if (!metaclass) {
            metaclass = tmptype;
            continue;
        }
        if (PyType_IsSubtype(metaclass, tmptype))
            continue;
        if (PyType_IsSubtype(tmptype, metaclass)) {
            metaclass = tmptype;
            continue;
        }
        PyErr_SetString(PyExc_TypeError,
                        "metaclass conflict: "
                        "the metaclass of a derived class "
                        "must be a (non-strict) subclass "
                        "of the metaclasses of all its bases");
        return NULL;
    }
    if (!metaclass) {
#if PY_MAJOR_VERSION < 3
        metaclass = &PyClass_Type;
#else
        metaclass = &PyType_Type;
#endif
    }
    Py_INCREF((PyObject*) metaclass);
    return (PyObject*) metaclass;
}

/* Py3ClassCreate */
static PyObject *__Pyx_Py3MetaclassPrepare(PyObject *metaclass, PyObject *bases, PyObject *name,
                                           PyObject *qualname, PyObject *mkw, PyObject *modname, PyObject *doc) {
    PyObject *ns;
    if (metaclass) {
        PyObject *prep = __Pyx_PyObject_GetAttrStr(metaclass, __pyx_n_s_prepare);
        if (prep) {
            PyObject *pargs = PyTuple_Pack(2, name, bases);
            if (unlikely(!pargs)) {
                Py_DECREF(prep);
                return NULL;
            }
            ns = PyObject_Call(prep, pargs, mkw);
            Py_DECREF(prep);
            Py_DECREF(pargs);
        } else {
            if (unlikely(!PyErr_ExceptionMatches(PyExc_AttributeError)))
                return NULL;
            PyErr_Clear();
            ns = PyDict_New();
        }
    } else {
        ns = PyDict_New();
    }
    if (unlikely(!ns))
        return NULL;
    if (unlikely(PyObject_SetItem(ns, __pyx_n_s_module, modname) < 0)) goto bad;
    if (unlikely(PyObject_SetItem(ns, __pyx_n_s_qualname, qualname) < 0)) goto bad;
    if (unlikely(doc && PyObject_SetItem(ns, __pyx_n_s_doc, doc) < 0)) goto bad;
    return ns;
bad:
    Py_DECREF(ns);
    return NULL;
}
static PyObject *__Pyx_Py3ClassCreate(PyObject *metaclass, PyObject *name, PyObject *bases,
                                      PyObject *dict, PyObject *mkw,
                                      int calculate_metaclass, int allow_py2_metaclass) {
    PyObject *result, *margs;
    PyObject *owned_metaclass = NULL;
    if (allow_py2_metaclass) {
        owned_metaclass = PyObject_GetItem(dict, __pyx_n_s_metaclass);
        if (owned_metaclass) {
            metaclass = owned_metaclass;
        } else if (likely(PyErr_ExceptionMatches(PyExc_KeyError))) {
            PyErr_Clear();
        } else {
            return NULL;
        }
    }
    if (calculate_metaclass && (!metaclass || PyType_Check(metaclass))) {
        metaclass = __Pyx_CalculateMetaclass((PyTypeObject*) metaclass, bases);
        Py_XDECREF(owned_metaclass);
        if (unlikely(!metaclass))
            return NULL;
        owned_metaclass = metaclass;
    }
    margs = PyTuple_Pack(3, name, bases, dict);
    if (unlikely(!margs)) {
        result = NULL;
    } else {
        result = PyObject_Call(metaclass, margs, mkw);
        Py_DECREF(margs);
    }
    Py_XDECREF(owned_metaclass);
    return result;
}

/* CLineInTraceback */
#ifndef CYTHON_CLINE_IN_TRACEBACK
static int __Pyx_CLineForTraceback(PyThreadState *tstate, int c_line) {
    PyObject *use_cline;
    PyObject *ptype, *pvalue, *ptraceback;
#if CYTHON_COMPILING_IN_CPYTHON
    PyObject **cython_runtime_dict;
#endif
    if (unlikely(!__pyx_cython_runtime)) {
        return c_line;
    }
    __Pyx_ErrFetchInState(tstate, &ptype, &pvalue, &ptraceback);
#if CYTHON_COMPILING_IN_CPYTHON
    cython_runtime_dict = _PyObject_GetDictPtr(__pyx_cython_runtime);
    if (likely(cython_runtime_dict)) {
        __PYX_PY_DICT_LOOKUP_IF_MODIFIED(
            use_cline, *cython_runtime_dict,
            __Pyx_PyDict_GetItemStr(*cython_runtime_dict, __pyx_n_s_cline_in_traceback))
    } else
#endif
    {
      PyObject *use_cline_obj = __Pyx_PyObject_GetAttrStr(__pyx_cython_runtime, __pyx_n_s_cline_in_traceback);
      if (use_cline_obj) {
        use_cline = PyObject_Not(use_cline_obj) ? Py_False : Py_True;
        Py_DECREF(use_cline_obj);
      } else {
        PyErr_Clear();
        use_cline = NULL;
      }
    }
    if (!use_cline) {
        c_line = 0;
        PyObject_SetAttr(__pyx_cython_runtime, __pyx_n_s_cline_in_traceback, Py_False);
    }
    else if (use_cline == Py_False || (use_cline != Py_True && PyObject_Not(use_cline) != 0)) {
        c_line = 0;
    }
    __Pyx_ErrRestoreInState(tstate, ptype, pvalue, ptraceback);
    return c_line;
}
#endif

/* CodeObjectCache */
static int __pyx_bisect_code_objects(__Pyx_CodeObjectCacheEntry* entries, int count, int code_line) {
    int start = 0, mid = 0, end = count - 1;
    if (end >= 0 && code_line > entries[end].code_line) {
        return count;
    }
    while (start < end) {
        mid = start + (end - start) / 2;
        if (code_line < entries[mid].code_line) {
            end = mid;
        } else if (code_line > entries[mid].code_line) {
             start = mid + 1;
        } else {
            return mid;
        }
    }
    if (code_line <= entries[mid].code_line) {
        return mid;
    } else {
        return mid + 1;
    }
}
static PyCodeObject *__pyx_find_code_object(int code_line) {
    PyCodeObject* code_object;
    int pos;
    if (unlikely(!code_line) || unlikely(!__pyx_code_cache.entries)) {
        return NULL;
    }
    pos = __pyx_bisect_code_objects(__pyx_code_cache.entries, __pyx_code_cache.count, code_line);
    if (unlikely(pos >= __pyx_code_cache.count) || unlikely(__pyx_code_cache.entries[pos].code_line != code_line)) {
        return NULL;
    }
    code_object = __pyx_code_cache.entries[pos].code_object;
    Py_INCREF(code_object);
    return code_object;
}
static void __pyx_insert_code_object(int code_line, PyCodeObject* code_object) {
    int pos, i;
    __Pyx_CodeObjectCacheEntry* entries = __pyx_code_cache.entries;
    if (unlikely(!code_line)) {
        return;
    }
    if (unlikely(!entries)) {
        entries = (__Pyx_CodeObjectCacheEntry*)PyMem_Malloc(64*sizeof(__Pyx_CodeObjectCacheEntry));
        if (likely(entries)) {
            __pyx_code_cache.entries = entries;
            __pyx_code_cache.max_count = 64;
            __pyx_code_cache.count = 1;
            entries[0].code_line = code_line;
            entries[0].code_object = code_object;
            Py_INCREF(code_object);
        }
        return;
    }
    pos = __pyx_bisect_code_objects(__pyx_code_cache.entries, __pyx_code_cache.count, code_line);
    if ((pos < __pyx_code_cache.count) && unlikely(__pyx_code_cache.entries[pos].code_line == code_line)) {
        PyCodeObject* tmp = entries[pos].code_object;
        entries[pos].code_object = code_object;
        Py_DECREF(tmp);
        return;
    }
    if (__pyx_code_cache.count == __pyx_code_cache.max_count) {
        int new_max = __pyx_code_cache.max_count + 64;
        entries = (__Pyx_CodeObjectCacheEntry*)PyMem_Realloc(
            __pyx_code_cache.entries, (size_t)new_max*sizeof(__Pyx_CodeObjectCacheEntry));
        if (unlikely(!entries)) {
            return;
        }
        __pyx_code_cache.entries = entries;
        __pyx_code_cache.max_count = new_max;
    }
    for (i=__pyx_code_cache.count; i>pos; i--) {
        entries[i] = entries[i-1];
    }
    entries[pos].code_line = code_line;
    entries[pos].code_object = code_object;
    __pyx_code_cache.count++;
    Py_INCREF(code_object);
}

/* AddTraceback */
#include "compile.h"
#include "frameobject.h"
#include "traceback.h"
static PyCodeObject* __Pyx_CreateCodeObjectForTraceback(
            const char *funcname, int c_line,
            int py_line, const char *filename) {
    PyCodeObject *py_code = 0;
    PyObject *py_srcfile = 0;
    PyObject *py_funcname = 0;
    #if PY_MAJOR_VERSION < 3
    py_srcfile = PyString_FromString(filename);
    #else
    py_srcfile = PyUnicode_FromString(filename);
    #endif
    if (!py_srcfile) goto bad;
    if (c_line) {
        #if PY_MAJOR_VERSION < 3
        py_funcname = PyString_FromFormat( "%s (%s:%d)", funcname, __pyx_cfilenm, c_line);
        #else
        py_funcname = PyUnicode_FromFormat( "%s (%s:%d)", funcname, __pyx_cfilenm, c_line);
        #endif
    }
    else {
        #if PY_MAJOR_VERSION < 3
        py_funcname = PyString_FromString(funcname);
        #else
        py_funcname = PyUnicode_FromString(funcname);
        #endif
    }
    if (!py_funcname) goto bad;
    py_code = __Pyx_PyCode_New(
        0,
        0,
        0,
        0,
        0,
        __pyx_empty_bytes, /*PyObject *code,*/
        __pyx_empty_tuple, /*PyObject *consts,*/
        __pyx_empty_tuple, /*PyObject *names,*/
        __pyx_empty_tuple, /*PyObject *varnames,*/
        __pyx_empty_tuple, /*PyObject *freevars,*/
        __pyx_empty_tuple, /*PyObject *cellvars,*/
        py_srcfile,   /*PyObject *filename,*/
        py_funcname,  /*PyObject *name,*/
        py_line,
        __pyx_empty_bytes  /*PyObject *lnotab*/
    );
    Py_DECREF(py_srcfile);
    Py_DECREF(py_funcname);
    return py_code;
bad:
    Py_XDECREF(py_srcfile);
    Py_XDECREF(py_funcname);
    return NULL;
}
static void __Pyx_AddTraceback(const char *funcname, int c_line,
                               int py_line, const char *filename) {
    PyCodeObject *py_code = 0;
    PyFrameObject *py_frame = 0;
    PyThreadState *tstate = __Pyx_PyThreadState_Current;
    if (c_line) {
        c_line = __Pyx_CLineForTraceback(tstate, c_line);
    }
    py_code = __pyx_find_code_object(c_line ? -c_line : py_line);
    if (!py_code) {
        py_code = __Pyx_CreateCodeObjectForTraceback(
            funcname, c_line, py_line, filename);
        if (!py_code) goto bad;
        __pyx_insert_code_object(c_line ? -c_line : py_line, py_code);
    }
    py_frame = PyFrame_New(
        tstate,            /*PyThreadState *tstate,*/
        py_code,           /*PyCodeObject *code,*/
        __pyx_d,    /*PyObject *globals,*/
        0                  /*PyObject *locals*/
    );
    if (!py_frame) goto bad;
    __Pyx_PyFrame_SetLineNumber(py_frame, py_line);
    PyTraceBack_Here(py_frame);
bad:
    Py_XDECREF(py_code);
    Py_XDECREF(py_frame);
}

/* CIntFromPyVerify */
#define __PYX_VERIFY_RETURN_INT(target_type, func_type, func_value)\
    __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, 0)
#define __PYX_VERIFY_RETURN_INT_EXC(target_type, func_type, func_value)\
    __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, 1)
#define __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, exc)\
    {\
        func_type value = func_value;\
        if (sizeof(target_type) < sizeof(func_type)) {\
            if (unlikely(value != (func_type) (target_type) value)) {\
                func_type zero = 0;\
                if (exc && unlikely(value == (func_type)-1 && PyErr_Occurred()))\
                    return (target_type) -1;\
                if (is_unsigned && unlikely(value < zero))\
                    goto raise_neg_overflow;\
                else\
                    goto raise_overflow;\
            }\
        }\
        return (target_type) value;\
    }

/* CIntToPy */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_int(int value) {
    const int neg_one = (int) ((int) 0 - (int) 1), const_zero = (int) 0;
    const int is_unsigned = neg_one > const_zero;
    if (is_unsigned) {
        if (sizeof(int) < sizeof(long)) {
            return PyInt_FromLong((long) value);
        } else if (sizeof(int) <= sizeof(unsigned long)) {
            return PyLong_FromUnsignedLong((unsigned long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(int) <= sizeof(unsigned PY_LONG_LONG)) {
            return PyLong_FromUnsignedLongLong((unsigned PY_LONG_LONG) value);
#endif
        }
    } else {
        if (sizeof(int) <= sizeof(long)) {
            return PyInt_FromLong((long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(int) <= sizeof(PY_LONG_LONG)) {
            return PyLong_FromLongLong((PY_LONG_LONG) value);
#endif
        }
    }
    {
        int one = 1; int little = (int)*(unsigned char *)&one;
        unsigned char *bytes = (unsigned char *)&value;
        return _PyLong_FromByteArray(bytes, sizeof(int),
                                     little, !is_unsigned);
    }
}

/* UnicodeAsUCS4 */
static CYTHON_INLINE Py_UCS4 __Pyx_PyUnicode_AsPy_UCS4(PyObject* x) {
   Py_ssize_t length;
   #if CYTHON_PEP393_ENABLED
   length = PyUnicode_GET_LENGTH(x);
   if (likely(length == 1)) {
       return PyUnicode_READ_CHAR(x, 0);
   }
   #else
   length = PyUnicode_GET_SIZE(x);
   if (likely(length == 1)) {
       return PyUnicode_AS_UNICODE(x)[0];
   }
   #if Py_UNICODE_SIZE == 2
   else if (PyUnicode_GET_SIZE(x) == 2) {
       Py_UCS4 high_val = PyUnicode_AS_UNICODE(x)[0];
       if (high_val >= 0xD800 && high_val <= 0xDBFF) {
           Py_UCS4 low_val = PyUnicode_AS_UNICODE(x)[1];
           if (low_val >= 0xDC00 && low_val <= 0xDFFF) {
               return 0x10000 + (((high_val & ((1<<10)-1)) << 10) | (low_val & ((1<<10)-1)));
           }
       }
   }
   #endif
   #endif
   PyErr_Format(PyExc_ValueError,
                "only single character unicode strings can be converted to Py_UCS4, "
                "got length %" CYTHON_FORMAT_SSIZE_T "d", length);
   return (Py_UCS4)-1;
}

/* CIntToPy */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_long(long value) {
    const long neg_one = (long) ((long) 0 - (long) 1), const_zero = (long) 0;
    const int is_unsigned = neg_one > const_zero;
    if (is_unsigned) {
        if (sizeof(long) < sizeof(long)) {
            return PyInt_FromLong((long) value);
        } else if (sizeof(long) <= sizeof(unsigned long)) {
            return PyLong_FromUnsignedLong((unsigned long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(long) <= sizeof(unsigned PY_LONG_LONG)) {
            return PyLong_FromUnsignedLongLong((unsigned PY_LONG_LONG) value);
#endif
        }
    } else {
        if (sizeof(long) <= sizeof(long)) {
            return PyInt_FromLong((long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(long) <= sizeof(PY_LONG_LONG)) {
            return PyLong_FromLongLong((PY_LONG_LONG) value);
#endif
        }
    }
    {
        int one = 1; int little = (int)*(unsigned char *)&one;
        unsigned char *bytes = (unsigned char *)&value;
        return _PyLong_FromByteArray(bytes, sizeof(long),
                                     little, !is_unsigned);
    }
}

/* CIntFromPy */
static CYTHON_INLINE long __Pyx_PyInt_As_long(PyObject *x) {
    const long neg_one = (long) ((long) 0 - (long) 1), const_zero = (long) 0;
    const int is_unsigned = neg_one > const_zero;
#if PY_MAJOR_VERSION < 3
    if (likely(PyInt_Check(x))) {
        if (sizeof(long) < sizeof(long)) {
            __PYX_VERIFY_RETURN_INT(long, long, PyInt_AS_LONG(x))
        } else {
            long val = PyInt_AS_LONG(x);
            if (is_unsigned && unlikely(val < 0)) {
                goto raise_neg_overflow;
            }
            return (long) val;
        }
    } else
#endif
    if (likely(PyLong_Check(x))) {
        if (is_unsigned) {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (long) 0;
                case  1: __PYX_VERIFY_RETURN_INT(long, digit, digits[0])
                case 2:
                    if (8 * sizeof(long) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 2 * PyLong_SHIFT) {
                            return (long) (((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(long) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 3 * PyLong_SHIFT) {
                            return (long) (((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(long) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 4 * PyLong_SHIFT) {
                            return (long) (((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
            }
#endif
#if CYTHON_COMPILING_IN_CPYTHON
            if (unlikely(Py_SIZE(x) < 0)) {
                goto raise_neg_overflow;
            }
#else
            {
                int result = PyObject_RichCompareBool(x, Py_False, Py_LT);
                if (unlikely(result < 0))
                    return (long) -1;
                if (unlikely(result == 1))
                    goto raise_neg_overflow;
            }
#endif
            if (sizeof(long) <= sizeof(unsigned long)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, unsigned long, PyLong_AsUnsignedLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(long) <= sizeof(unsigned PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))
#endif
            }
        } else {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (long) 0;
                case -1: __PYX_VERIFY_RETURN_INT(long, sdigit, (sdigit) (-(sdigit)digits[0]))
                case  1: __PYX_VERIFY_RETURN_INT(long,  digit, +digits[0])
                case -2:
                    if (8 * sizeof(long) - 1 > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 2:
                    if (8 * sizeof(long) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                            return (long) ((((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case -3:
                    if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(long) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                            return (long) ((((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case -4:
                    if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(long) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {
                            return (long) ((((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
            }
#endif
            if (sizeof(long) <= sizeof(long)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, long, PyLong_AsLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(long) <= sizeof(PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, PY_LONG_LONG, PyLong_AsLongLong(x))
#endif
            }
        }
        {
#if CYTHON_COMPILING_IN_PYPY && !defined(_PyLong_AsByteArray)
            PyErr_SetString(PyExc_RuntimeError,
                            "_PyLong_AsByteArray() not available in PyPy, cannot convert large numbers");
#else
            long val;
            PyObject *v = __Pyx_PyNumber_IntOrLong(x);
 #if PY_MAJOR_VERSION < 3
            if (likely(v) && !PyLong_Check(v)) {
                PyObject *tmp = v;
                v = PyNumber_Long(tmp);
                Py_DECREF(tmp);
            }
 #endif
            if (likely(v)) {
                int one = 1; int is_little = (int)*(unsigned char *)&one;
                unsigned char *bytes = (unsigned char *)&val;
                int ret = _PyLong_AsByteArray((PyLongObject *)v,
                                              bytes, sizeof(val),
                                              is_little, !is_unsigned);
                Py_DECREF(v);
                if (likely(!ret))
                    return val;
            }
#endif
            return (long) -1;
        }
    } else {
        long val;
        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);
        if (!tmp) return (long) -1;
        val = __Pyx_PyInt_As_long(tmp);
        Py_DECREF(tmp);
        return val;
    }
raise_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "value too large to convert to long");
    return (long) -1;
raise_neg_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "can't convert negative value to long");
    return (long) -1;
}

/* CIntFromPy */
static CYTHON_INLINE int __Pyx_PyInt_As_int(PyObject *x) {
    const int neg_one = (int) ((int) 0 - (int) 1), const_zero = (int) 0;
    const int is_unsigned = neg_one > const_zero;
#if PY_MAJOR_VERSION < 3
    if (likely(PyInt_Check(x))) {
        if (sizeof(int) < sizeof(long)) {
            __PYX_VERIFY_RETURN_INT(int, long, PyInt_AS_LONG(x))
        } else {
            long val = PyInt_AS_LONG(x);
            if (is_unsigned && unlikely(val < 0)) {
                goto raise_neg_overflow;
            }
            return (int) val;
        }
    } else
#endif
    if (likely(PyLong_Check(x))) {
        if (is_unsigned) {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (int) 0;
                case  1: __PYX_VERIFY_RETURN_INT(int, digit, digits[0])
                case 2:
                    if (8 * sizeof(int) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 2 * PyLong_SHIFT) {
                            return (int) (((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(int) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 3 * PyLong_SHIFT) {
                            return (int) (((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(int) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 4 * PyLong_SHIFT) {
                            return (int) (((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
            }
#endif
#if CYTHON_COMPILING_IN_CPYTHON
            if (unlikely(Py_SIZE(x) < 0)) {
                goto raise_neg_overflow;
            }
#else
            {
                int result = PyObject_RichCompareBool(x, Py_False, Py_LT);
                if (unlikely(result < 0))
                    return (int) -1;
                if (unlikely(result == 1))
                    goto raise_neg_overflow;
            }
#endif
            if (sizeof(int) <= sizeof(unsigned long)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, unsigned long, PyLong_AsUnsignedLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(int) <= sizeof(unsigned PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))
#endif
            }
        } else {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (int) 0;
                case -1: __PYX_VERIFY_RETURN_INT(int, sdigit, (sdigit) (-(sdigit)digits[0]))
                case  1: __PYX_VERIFY_RETURN_INT(int,  digit, +digits[0])
                case -2:
                    if (8 * sizeof(int) - 1 > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 2:
                    if (8 * sizeof(int) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                            return (int) ((((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case -3:
                    if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(int) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                            return (int) ((((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case -4:
                    if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 4 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(int) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 4 * PyLong_SHIFT) {
                            return (int) ((((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
            }
#endif
            if (sizeof(int) <= sizeof(long)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, long, PyLong_AsLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(int) <= sizeof(PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, PY_LONG_LONG, PyLong_AsLongLong(x))
#endif
            }
        }
        {
#if CYTHON_COMPILING_IN_PYPY && !defined(_PyLong_AsByteArray)
            PyErr_SetString(PyExc_RuntimeError,
                            "_PyLong_AsByteArray() not available in PyPy, cannot convert large numbers");
#else
            int val;
            PyObject *v = __Pyx_PyNumber_IntOrLong(x);
 #if PY_MAJOR_VERSION < 3
            if (likely(v) && !PyLong_Check(v)) {
                PyObject *tmp = v;
                v = PyNumber_Long(tmp);
                Py_DECREF(tmp);
            }
 #endif
            if (likely(v)) {
                int one = 1; int is_little = (int)*(unsigned char *)&one;
                unsigned char *bytes = (unsigned char *)&val;
                int ret = _PyLong_AsByteArray((PyLongObject *)v,
                                              bytes, sizeof(val),
                                              is_little, !is_unsigned);
                Py_DECREF(v);
                if (likely(!ret))
                    return val;
            }
#endif
            return (int) -1;
        }
    } else {
        int val;
        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);
        if (!tmp) return (int) -1;
        val = __Pyx_PyInt_As_int(tmp);
        Py_DECREF(tmp);
        return val;
    }
raise_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "value too large to convert to int");
    return (int) -1;
raise_neg_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "can't convert negative value to int");
    return (int) -1;
}

/* ObjectAsUCS4 */
static Py_UCS4 __Pyx__PyObject_AsPy_UCS4_raise_error(long ival) {
   if (ival < 0) {
       if (!PyErr_Occurred())
           PyErr_SetString(PyExc_OverflowError,
                           "cannot convert negative value to Py_UCS4");
   } else {
       PyErr_SetString(PyExc_OverflowError,
                       "value too large to convert to Py_UCS4");
   }
   return (Py_UCS4)-1;
}
static Py_UCS4 __Pyx__PyObject_AsPy_UCS4(PyObject* x) {
   long ival;
   ival = __Pyx_PyInt_As_long(x);
   if (unlikely(!__Pyx_is_valid_index(ival, 1114111 + 1))) {
       return __Pyx__PyObject_AsPy_UCS4_raise_error(ival);
   }
   return (Py_UCS4)ival;
}

/* FastTypeChecks */
#if CYTHON_COMPILING_IN_CPYTHON
static int __Pyx_InBases(PyTypeObject *a, PyTypeObject *b) {
    while (a) {
        a = a->tp_base;
        if (a == b)
            return 1;
    }
    return b == &PyBaseObject_Type;
}
static CYTHON_INLINE int __Pyx_IsSubtype(PyTypeObject *a, PyTypeObject *b) {
    PyObject *mro;
    if (a == b) return 1;
    mro = a->tp_mro;
    if (likely(mro)) {
        Py_ssize_t i, n;
        n = PyTuple_GET_SIZE(mro);
        for (i = 0; i < n; i++) {
            if (PyTuple_GET_ITEM(mro, i) == (PyObject *)b)
                return 1;
        }
        return 0;
    }
    return __Pyx_InBases(a, b);
}
#if PY_MAJOR_VERSION == 2
static int __Pyx_inner_PyErr_GivenExceptionMatches2(PyObject *err, PyObject* exc_type1, PyObject* exc_type2) {
    PyObject *exception, *value, *tb;
    int res;
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    __Pyx_ErrFetch(&exception, &value, &tb);
    res = exc_type1 ? PyObject_IsSubclass(err, exc_type1) : 0;
    if (unlikely(res == -1)) {
        PyErr_WriteUnraisable(err);
        res = 0;
    }
    if (!res) {
        res = PyObject_IsSubclass(err, exc_type2);
        if (unlikely(res == -1)) {
            PyErr_WriteUnraisable(err);
            res = 0;
        }
    }
    __Pyx_ErrRestore(exception, value, tb);
    return res;
}
#else
static CYTHON_INLINE int __Pyx_inner_PyErr_GivenExceptionMatches2(PyObject *err, PyObject* exc_type1, PyObject *exc_type2) {
    int res = exc_type1 ? __Pyx_IsSubtype((PyTypeObject*)err, (PyTypeObject*)exc_type1) : 0;
    if (!res) {
        res = __Pyx_IsSubtype((PyTypeObject*)err, (PyTypeObject*)exc_type2);
    }
    return res;
}
#endif
static int __Pyx_PyErr_GivenExceptionMatchesTuple(PyObject *exc_type, PyObject *tuple) {
    Py_ssize_t i, n;
    assert(PyExceptionClass_Check(exc_type));
    n = PyTuple_GET_SIZE(tuple);
#if PY_MAJOR_VERSION >= 3
    for (i=0; i<n; i++) {
        if (exc_type == PyTuple_GET_ITEM(tuple, i)) return 1;
    }
#endif
    for (i=0; i<n; i++) {
        PyObject *t = PyTuple_GET_ITEM(tuple, i);
        #if PY_MAJOR_VERSION < 3
        if (likely(exc_type == t)) return 1;
        #endif
        if (likely(PyExceptionClass_Check(t))) {
            if (__Pyx_inner_PyErr_GivenExceptionMatches2(exc_type, NULL, t)) return 1;
        } else {
        }
    }
    return 0;
}
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches(PyObject *err, PyObject* exc_type) {
    if (likely(err == exc_type)) return 1;
    if (likely(PyExceptionClass_Check(err))) {
        if (likely(PyExceptionClass_Check(exc_type))) {
            return __Pyx_inner_PyErr_GivenExceptionMatches2(err, NULL, exc_type);
        } else if (likely(PyTuple_Check(exc_type))) {
            return __Pyx_PyErr_GivenExceptionMatchesTuple(err, exc_type);
        } else {
        }
    }
    return PyErr_GivenExceptionMatches(err, exc_type);
}
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches2(PyObject *err, PyObject *exc_type1, PyObject *exc_type2) {
    assert(PyExceptionClass_Check(exc_type1));
    assert(PyExceptionClass_Check(exc_type2));
    if (likely(err == exc_type1 || err == exc_type2)) return 1;
    if (likely(PyExceptionClass_Check(err))) {
        return __Pyx_inner_PyErr_GivenExceptionMatches2(err, exc_type1, exc_type2);
    }
    return (PyErr_GivenExceptionMatches(err, exc_type1) || PyErr_GivenExceptionMatches(err, exc_type2));
}
#endif

/* CheckBinaryVersion */
static int __Pyx_check_binary_version(void) {
    char ctversion[4], rtversion[4];
    PyOS_snprintf(ctversion, 4, "%d.%d", PY_MAJOR_VERSION, PY_MINOR_VERSION);
    PyOS_snprintf(rtversion, 4, "%s", Py_GetVersion());
    if (ctversion[0] != rtversion[0] || ctversion[2] != rtversion[2]) {
        char message[200];
        PyOS_snprintf(message, sizeof(message),
                      "compiletime version %s of module '%.100s' "
                      "does not match runtime version %s",
                      ctversion, __Pyx_MODULE_NAME, rtversion);
        return PyErr_WarnEx(NULL, message, 1);
    }
    return 0;
}

/* VoidPtrImport */
#ifndef __PYX_HAVE_RT_ImportVoidPtr
#define __PYX_HAVE_RT_ImportVoidPtr
static int __Pyx_ImportVoidPtr(PyObject *module, const char *name, void **p, const char *sig) {
    PyObject *d = 0;
    PyObject *cobj = 0;
    d = PyObject_GetAttrString(module, (char *)"__pyx_capi__");
    if (!d)
        goto bad;
    cobj = PyDict_GetItemString(d, name);
    if (!cobj) {
        PyErr_Format(PyExc_ImportError,
            "%.200s does not export expected C variable %.200s",
                PyModule_GetName(module), name);
        goto bad;
    }
#if PY_VERSION_HEX >= 0x02070000
    if (!PyCapsule_IsValid(cobj, sig)) {
        PyErr_Format(PyExc_TypeError,
            "C variable %.200s.%.200s has wrong signature (expected %.500s, got %.500s)",
             PyModule_GetName(module), name, sig, PyCapsule_GetName(cobj));
        goto bad;
    }
    *p = PyCapsule_GetPointer(cobj, sig);
#else
    {const char *desc, *s1, *s2;
    desc = (const char *)PyCObject_GetDesc(cobj);
    if (!desc)
        goto bad;
    s1 = desc; s2 = sig;
    while (*s1 != '\0' && *s1 == *s2) { s1++; s2++; }
    if (*s1 != *s2) {
        PyErr_Format(PyExc_TypeError,
            "C variable %.200s.%.200s has wrong signature (expected %.500s, got %.500s)",
             PyModule_GetName(module), name, sig, desc);
        goto bad;
    }
    *p = PyCObject_AsVoidPtr(cobj);}
#endif
    if (!(*p))
        goto bad;
    Py_DECREF(d);
    return 0;
bad:
    Py_XDECREF(d);
    return -1;
}
#endif

/* FunctionImport */
#ifndef __PYX_HAVE_RT_ImportFunction
#define __PYX_HAVE_RT_ImportFunction
static int __Pyx_ImportFunction(PyObject *module, const char *funcname, void (**f)(void), const char *sig) {
    PyObject *d = 0;
    PyObject *cobj = 0;
    union {
        void (*fp)(void);
        void *p;
    } tmp;
    d = PyObject_GetAttrString(module, (char *)"__pyx_capi__");
    if (!d)
        goto bad;
    cobj = PyDict_GetItemString(d, funcname);
    if (!cobj) {
        PyErr_Format(PyExc_ImportError,
            "%.200s does not export expected C function %.200s",
                PyModule_GetName(module), funcname);
        goto bad;
    }
#if PY_VERSION_HEX >= 0x02070000
    if (!PyCapsule_IsValid(cobj, sig)) {
        PyErr_Format(PyExc_TypeError,
            "C function %.200s.%.200s has wrong signature (expected %.500s, got %.500s)",
             PyModule_GetName(module), funcname, sig, PyCapsule_GetName(cobj));
        goto bad;
    }
    tmp.p = PyCapsule_GetPointer(cobj, sig);
#else
    {const char *desc, *s1, *s2;
    desc = (const char *)PyCObject_GetDesc(cobj);
    if (!desc)
        goto bad;
    s1 = desc; s2 = sig;
    while (*s1 != '\0' && *s1 == *s2) { s1++; s2++; }
    if (*s1 != *s2) {
        PyErr_Format(PyExc_TypeError,
            "C function %.200s.%.200s has wrong signature (expected %.500s, got %.500s)",
             PyModule_GetName(module), funcname, sig, desc);
        goto bad;
    }
    tmp.p = PyCObject_AsVoidPtr(cobj);}
#endif
    *f = tmp.fp;
    if (!(*f))
        goto bad;
    Py_DECREF(d);
    return 0;
bad:
    Py_XDECREF(d);
    return -1;
}
#endif

/* InitStrings */
static int __Pyx_InitStrings(__Pyx_StringTabEntry *t) {
    while (t->p) {
        #if PY_MAJOR_VERSION < 3
        if (t->is_unicode) {
            *t->p = PyUnicode_DecodeUTF8(t->s, t->n - 1, NULL);
        } else if (t->intern) {
            *t->p = PyString_InternFromString(t->s);
        } else {
            *t->p = PyString_FromStringAndSize(t->s, t->n - 1);
        }
        #else
        if (t->is_unicode | t->is_str) {
            if (t->intern) {
                *t->p = PyUnicode_InternFromString(t->s);
            } else if (t->encoding) {
                *t->p = PyUnicode_Decode(t->s, t->n - 1, t->encoding, NULL);
            } else {
                *t->p = PyUnicode_FromStringAndSize(t->s, t->n - 1);
            }
        } else {
            *t->p = PyBytes_FromStringAndSize(t->s, t->n - 1);
        }
        #endif
        if (!*t->p)
            return -1;
        if (PyObject_Hash(*t->p) == -1)
            return -1;
        ++t;
    }
    return 0;
}

static CYTHON_INLINE PyObject* __Pyx_PyUnicode_FromString(const char* c_str) {
    return __Pyx_PyUnicode_FromStringAndSize(c_str, (Py_ssize_t)strlen(c_str));
}
static CYTHON_INLINE const char* __Pyx_PyObject_AsString(PyObject* o) {
    Py_ssize_t ignore;
    return __Pyx_PyObject_AsStringAndSize(o, &ignore);
}
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
#if !CYTHON_PEP393_ENABLED
static const char* __Pyx_PyUnicode_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
    char* defenc_c;
    PyObject* defenc = _PyUnicode_AsDefaultEncodedString(o, NULL);
    if (!defenc) return NULL;
    defenc_c = PyBytes_AS_STRING(defenc);
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
    {
        char* end = defenc_c + PyBytes_GET_SIZE(defenc);
        char* c;
        for (c = defenc_c; c < end; c++) {
            if ((unsigned char) (*c) >= 128) {
                PyUnicode_AsASCIIString(o);
                return NULL;
            }
        }
    }
#endif
    *length = PyBytes_GET_SIZE(defenc);
    return defenc_c;
}
#else
static CYTHON_INLINE const char* __Pyx_PyUnicode_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
    if (unlikely(__Pyx_PyUnicode_READY(o) == -1)) return NULL;
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
    if (likely(PyUnicode_IS_ASCII(o))) {
        *length = PyUnicode_GET_LENGTH(o);
        return PyUnicode_AsUTF8(o);
    } else {
        PyUnicode_AsASCIIString(o);
        return NULL;
    }
#else
    return PyUnicode_AsUTF8AndSize(o, length);
#endif
}
#endif
#endif
static CYTHON_INLINE const char* __Pyx_PyObject_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
    if (
#if PY_MAJOR_VERSION < 3 && __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
            __Pyx_sys_getdefaultencoding_not_ascii &&
#endif
            PyUnicode_Check(o)) {
        return __Pyx_PyUnicode_AsStringAndSize(o, length);
    } else
#endif
#if (!CYTHON_COMPILING_IN_PYPY) || (defined(PyByteArray_AS_STRING) && defined(PyByteArray_GET_SIZE))
    if (PyByteArray_Check(o)) {
        *length = PyByteArray_GET_SIZE(o);
        return PyByteArray_AS_STRING(o);
    } else
#endif
    {
        char* result;
        int r = PyBytes_AsStringAndSize(o, &result, length);
        if (unlikely(r < 0)) {
            return NULL;
        } else {
            return result;
        }
    }
}
static CYTHON_INLINE int __Pyx_PyObject_IsTrue(PyObject* x) {
   int is_true = x == Py_True;
   if (is_true | (x == Py_False) | (x == Py_None)) return is_true;
   else return PyObject_IsTrue(x);
}
static CYTHON_INLINE int __Pyx_PyObject_IsTrueAndDecref(PyObject* x) {
    int retval;
    if (unlikely(!x)) return -1;
    retval = __Pyx_PyObject_IsTrue(x);
    Py_DECREF(x);
    return retval;
}
static PyObject* __Pyx_PyNumber_IntOrLongWrongResultType(PyObject* result, const char* type_name) {
#if PY_MAJOR_VERSION >= 3
    if (PyLong_Check(result)) {
        if (PyErr_WarnFormat(PyExc_DeprecationWarning, 1,
                "__int__ returned non-int (type %.200s).  "
                "The ability to return an instance of a strict subclass of int "
                "is deprecated, and may be removed in a future version of Python.",
                Py_TYPE(result)->tp_name)) {
            Py_DECREF(result);
            return NULL;
        }
        return result;
    }
#endif
    PyErr_Format(PyExc_TypeError,
                 "__%.4s__ returned non-%.4s (type %.200s)",
                 type_name, type_name, Py_TYPE(result)->tp_name);
    Py_DECREF(result);
    return NULL;
}
static CYTHON_INLINE PyObject* __Pyx_PyNumber_IntOrLong(PyObject* x) {
#if CYTHON_USE_TYPE_SLOTS
  PyNumberMethods *m;
#endif
  const char *name = NULL;
  PyObject *res = NULL;
#if PY_MAJOR_VERSION < 3
  if (likely(PyInt_Check(x) || PyLong_Check(x)))
#else
  if (likely(PyLong_Check(x)))
#endif
    return __Pyx_NewRef(x);
#if CYTHON_USE_TYPE_SLOTS
  m = Py_TYPE(x)->tp_as_number;
  #if PY_MAJOR_VERSION < 3
  if (m && m->nb_int) {
    name = "int";
    res = m->nb_int(x);
  }
  else if (m && m->nb_long) {
    name = "long";
    res = m->nb_long(x);
  }
  #else
  if (likely(m && m->nb_int)) {
    name = "int";
    res = m->nb_int(x);
  }
  #endif
#else
  if (!PyBytes_CheckExact(x) && !PyUnicode_CheckExact(x)) {
    res = PyNumber_Int(x);
  }
#endif
  if (likely(res)) {
#if PY_MAJOR_VERSION < 3
    if (unlikely(!PyInt_Check(res) && !PyLong_Check(res))) {
#else
    if (unlikely(!PyLong_CheckExact(res))) {
#endif
        return __Pyx_PyNumber_IntOrLongWrongResultType(res, name);
    }
  }
  else if (!PyErr_Occurred()) {
    PyErr_SetString(PyExc_TypeError,
                    "an integer is required");
  }
  return res;
}
static CYTHON_INLINE Py_ssize_t __Pyx_PyIndex_AsSsize_t(PyObject* b) {
  Py_ssize_t ival;
  PyObject *x;
#if PY_MAJOR_VERSION < 3
  if (likely(PyInt_CheckExact(b))) {
    if (sizeof(Py_ssize_t) >= sizeof(long))
        return PyInt_AS_LONG(b);
    else
        return PyInt_AsSsize_t(b);
  }
#endif
  if (likely(PyLong_CheckExact(b))) {
    #if CYTHON_USE_PYLONG_INTERNALS
    const digit* digits = ((PyLongObject*)b)->ob_digit;
    const Py_ssize_t size = Py_SIZE(b);
    if (likely(__Pyx_sst_abs(size) <= 1)) {
        ival = likely(size) ? digits[0] : 0;
        if (size == -1) ival = -ival;
        return ival;
    } else {
      switch (size) {
         case 2:
           if (8 * sizeof(Py_ssize_t) > 2 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -2:
           if (8 * sizeof(Py_ssize_t) > 2 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case 3:
           if (8 * sizeof(Py_ssize_t) > 3 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -3:
           if (8 * sizeof(Py_ssize_t) > 3 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case 4:
           if (8 * sizeof(Py_ssize_t) > 4 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -4:
           if (8 * sizeof(Py_ssize_t) > 4 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
      }
    }
    #endif
    return PyLong_AsSsize_t(b);
  }
  x = PyNumber_Index(b);
  if (!x) return -1;
  ival = PyInt_AsSsize_t(x);
  Py_DECREF(x);
  return ival;
}
static CYTHON_INLINE PyObject * __Pyx_PyBool_FromLong(long b) {
  return b ? __Pyx_NewRef(Py_True) : __Pyx_NewRef(Py_False);
}
static CYTHON_INLINE PyObject * __Pyx_PyInt_FromSize_t(size_t ival) {
    return PyInt_FromSize_t(ival);
}


#endif /* Py_PYTHON_H */
