"""
    pygments.lexers._asy_builtins
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    This file contains the asy-function names and asy-variable names of
    Asymptote.

    Do not edit the ASYFUNCNAME and ASYVARNAME sets by hand.
    TODO: perl/python script in Asymptote SVN similar to asy-list.pl but only
    for function and variable names.

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

ASYFUNCNAME = {
    'AND',
    'Arc',
    'ArcArrow',
    'ArcArrows',
    'Arrow',
    'Arrows',
    'Automatic',
    'AvantGarde',
    'BBox',
    'B<PERSON>ainbow',
    'BWRainbow2',
    'Bar',
    'Bars',
    'BeginArcArrow',
    'BeginArrow',
    'BeginBar',
    'BeginDotMargin',
    'BeginMargin',
    'BeginPenMargin',
    'Blank',
    'Bookman',
    'Bottom',
    'BottomTop',
    'Bounds',
    'Break',
    'Broken',
    'BrokenLog',
    'Ceil',
    'Circle',
    'CircleBarIntervalMarker',
    'Cos',
    'Courier',
    'CrossIntervalMarker',
    'DefaultFormat',
    'DefaultLogFormat',
    'Degrees',
    'Dir',
    'DotMargin',
    'DotMargins',
    'Dotted',
    'Draw',
    'Drawline',
    'Embed',
    'EndArcArrow',
    'EndArrow',
    'EndBar',
    'EndDotMargin',
    'EndMargin',
    'EndPenMargin',
    'Fill',
    'FillDraw',
    'Floor',
    'Format',
    'Full',
    'Gaussian',
    'Gaussrand',
    'Gaussrandpair',
    'Gradient',
    'Grayscale',
    'Helvetica',
    'Hermite',
    'HookHead',
    'InOutTicks',
    'InTicks',
    'J',
    'Label',
    'Landscape',
    'Left',
    'LeftRight',
    'LeftTicks',
    'Legend',
    'Linear',
    'Link',
    'Log',
    'LogFormat',
    'Margin',
    'Margins',
    'Mark',
    'MidArcArrow',
    'MidArrow',
    'NOT',
    'NewCenturySchoolBook',
    'NoBox',
    'NoMargin',
    'NoModifier',
    'NoTicks',
    'NoTicks3',
    'NoZero',
    'NoZeroFormat',
    'None',
    'OR',
    'OmitFormat',
    'OmitTick',
    'OutTicks',
    'Ox',
    'Oy',
    'Palatino',
    'PaletteTicks',
    'Pen',
    'PenMargin',
    'PenMargins',
    'Pentype',
    'Portrait',
    'RadialShade',
    'Rainbow',
    'Range',
    'Relative',
    'Right',
    'RightTicks',
    'Rotate',
    'Round',
    'SQR',
    'Scale',
    'ScaleX',
    'ScaleY',
    'ScaleZ',
    'Seascape',
    'Shift',
    'Sin',
    'Slant',
    'Spline',
    'StickIntervalMarker',
    'Straight',
    'Symbol',
    'Tan',
    'TeXify',
    'Ticks',
    'Ticks3',
    'TildeIntervalMarker',
    'TimesRoman',
    'Top',
    'TrueMargin',
    'UnFill',
    'UpsideDown',
    'Wheel',
    'X',
    'XEquals',
    'XOR',
    'XY',
    'XYEquals',
    'XYZero',
    'XYgrid',
    'XZEquals',
    'XZZero',
    'XZero',
    'XZgrid',
    'Y',
    'YEquals',
    'YXgrid',
    'YZ',
    'YZEquals',
    'YZZero',
    'YZero',
    'YZgrid',
    'Z',
    'ZX',
    'ZXgrid',
    'ZYgrid',
    'ZapfChancery',
    'ZapfDingbats',
    '_cputime',
    '_draw',
    '_eval',
    '_image',
    '_labelpath',
    '_projection',
    '_strokepath',
    '_texpath',
    'aCos',
    'aSin',
    'aTan',
    'abort',
    'abs',
    'accel',
    'acos',
    'acosh',
    'acot',
    'acsc',
    'add',
    'addArrow',
    'addMargins',
    'addSaveFunction',
    'addnode',
    'addnodes',
    'addpenarc',
    'addpenline',
    'addseg',
    'adjust',
    'alias',
    'align',
    'all',
    'altitude',
    'angabscissa',
    'angle',
    'angpoint',
    'animate',
    'annotate',
    'anticomplementary',
    'antipedal',
    'apply',
    'approximate',
    'arc',
    'arcarrowsize',
    'arccircle',
    'arcdir',
    'arcfromcenter',
    'arcfromfocus',
    'arclength',
    'arcnodesnumber',
    'arcpoint',
    'arcsubtended',
    'arcsubtendedcenter',
    'arctime',
    'arctopath',
    'array',
    'arrow',
    'arrow2',
    'arrowbase',
    'arrowbasepoints',
    'arrowsize',
    'asec',
    'asin',
    'asinh',
    'ask',
    'assert',
    'asy',
    'asycode',
    'asydir',
    'asyfigure',
    'asyfilecode',
    'asyinclude',
    'asywrite',
    'atan',
    'atan2',
    'atanh',
    'atbreakpoint',
    'atexit',
    'atime',
    'attach',
    'attract',
    'atupdate',
    'autoformat',
    'autoscale',
    'autoscale3',
    'axes',
    'axes3',
    'axialshade',
    'axis',
    'axiscoverage',
    'azimuth',
    'babel',
    'background',
    'bangles',
    'bar',
    'barmarksize',
    'barsize',
    'basealign',
    'baseline',
    'bbox',
    'beep',
    'begin',
    'beginclip',
    'begingroup',
    'beginpoint',
    'between',
    'bevel',
    'bezier',
    'bezierP',
    'bezierPP',
    'bezierPPP',
    'bezulate',
    'bibliography',
    'bibliographystyle',
    'binarytree',
    'binarytreeNode',
    'binomial',
    'binput',
    'bins',
    'bisector',
    'bisectorpoint',
    'blend',
    'boutput',
    'box',
    'bqe',
    'breakpoint',
    'breakpoints',
    'brick',
    'buildRestoreDefaults',
    'buildRestoreThunk',
    'buildcycle',
    'bulletcolor',
    'canonical',
    'canonicalcartesiansystem',
    'cartesiansystem',
    'case1',
    'case2',
    'case3',
    'cbrt',
    'cd',
    'ceil',
    'center',
    'centerToFocus',
    'centroid',
    'cevian',
    'change2',
    'changecoordsys',
    'checkSegment',
    'checkconditionlength',
    'checker',
    'checklengths',
    'checkposition',
    'checktriangle',
    'choose',
    'circle',
    'circlebarframe',
    'circlemarkradius',
    'circlenodesnumber',
    'circumcenter',
    'circumcircle',
    'clamped',
    'clear',
    'clip',
    'clipdraw',
    'close',
    'cmyk',
    'code',
    'colatitude',
    'collect',
    'collinear',
    'color',
    'colorless',
    'colors',
    'colorspace',
    'comma',
    'compassmark',
    'complement',
    'complementary',
    'concat',
    'concurrent',
    'cone',
    'conic',
    'conicnodesnumber',
    'conictype',
    'conj',
    'connect',
    'containmentTree',
    'contains',
    'contour',
    'contour3',
    'controlSpecifier',
    'convert',
    'coordinates',
    'coordsys',
    'copy',
    'cos',
    'cosh',
    'cot',
    'countIntersections',
    'cputime',
    'crop',
    'cropcode',
    'cross',
    'crossframe',
    'crosshatch',
    'crossmarksize',
    'csc',
    'cubicroots',
    'curabscissa',
    'curlSpecifier',
    'curpoint',
    'currentarrow',
    'currentexitfunction',
    'currentmomarrow',
    'currentpolarconicroutine',
    'curve',
    'cut',
    'cutafter',
    'cutbefore',
    'cyclic',
    'cylinder',
    'debugger',
    'deconstruct',
    'defaultdir',
    'defaultformat',
    'defaultpen',
    'defined',
    'degenerate',
    'degrees',
    'delete',
    'deletepreamble',
    'determinant',
    'diagonal',
    'diamond',
    'diffdiv',
    'dir',
    'dirSpecifier',
    'dirtime',
    'display',
    'distance',
    'divisors',
    'do_overpaint',
    'dot',
    'dotframe',
    'dotsize',
    'downcase',
    'draw',
    'drawAll',
    'drawDoubleLine',
    'drawFermion',
    'drawGhost',
    'drawGluon',
    'drawMomArrow',
    'drawPhoton',
    'drawScalar',
    'drawVertex',
    'drawVertexBox',
    'drawVertexBoxO',
    'drawVertexBoxX',
    'drawVertexO',
    'drawVertexOX',
    'drawVertexTriangle',
    'drawVertexTriangleO',
    'drawVertexX',
    'drawarrow',
    'drawarrow2',
    'drawline',
    'drawtick',
    'duplicate',
    'elle',
    'ellipse',
    'ellipsenodesnumber',
    'embed',
    'embed3',
    'empty',
    'enclose',
    'end',
    'endScript',
    'endclip',
    'endgroup',
    'endl',
    'endpoint',
    'endpoints',
    'eof',
    'eol',
    'equation',
    'equations',
    'erase',
    'erasestep',
    'erf',
    'erfc',
    'error',
    'errorbar',
    'errorbars',
    'eval',
    'excenter',
    'excircle',
    'exit',
    'exitXasyMode',
    'exitfunction',
    'exp',
    'expfactors',
    'expi',
    'expm1',
    'exradius',
    'extend',
    'extension',
    'extouch',
    'fabs',
    'factorial',
    'fermat',
    'fft',
    'fhorner',
    'figure',
    'file',
    'filecode',
    'fill',
    'filldraw',
    'filloutside',
    'fillrule',
    'filltype',
    'find',
    'finite',
    'finiteDifferenceJacobian',
    'firstcut',
    'firstframe',
    'fit',
    'fit2',
    'fixedscaling',
    'floor',
    'flush',
    'fmdefaults',
    'fmod',
    'focusToCenter',
    'font',
    'fontcommand',
    'fontsize',
    'foot',
    'format',
    'frac',
    'frequency',
    'fromCenter',
    'fromFocus',
    'fspline',
    'functionshade',
    'gamma',
    'generate_random_backtrace',
    'generateticks',
    'gergonne',
    'getc',
    'getint',
    'getpair',
    'getreal',
    'getstring',
    'gettriple',
    'gluon',
    'gouraudshade',
    'graph',
    'graphic',
    'gray',
    'grestore',
    'grid',
    'grid3',
    'gsave',
    'halfbox',
    'hatch',
    'hdiffdiv',
    'hermite',
    'hex',
    'histogram',
    'history',
    'hline',
    'hprojection',
    'hsv',
    'hyperbola',
    'hyperbolanodesnumber',
    'hyperlink',
    'hypot',
    'identity',
    'image',
    'incenter',
    'incentral',
    'incircle',
    'increasing',
    'incrementposition',
    'indexedTransform',
    'indexedfigure',
    'initXasyMode',
    'initdefaults',
    'input',
    'inradius',
    'insert',
    'inside',
    'integrate',
    'interactive',
    'interior',
    'interp',
    'interpolate',
    'intersect',
    'intersection',
    'intersectionpoint',
    'intersectionpoints',
    'intersections',
    'intouch',
    'inverse',
    'inversion',
    'invisible',
    'is3D',
    'isDuplicate',
    'isogonal',
    'isogonalconjugate',
    'isotomic',
    'isotomicconjugate',
    'isparabola',
    'italic',
    'item',
    'key',
    'kurtosis',
    'kurtosisexcess',
    'label',
    'labelaxis',
    'labelmargin',
    'labelpath',
    'labels',
    'labeltick',
    'labelx',
    'labelx3',
    'labely',
    'labely3',
    'labelz',
    'labelz3',
    'lastcut',
    'latex',
    'latitude',
    'latticeshade',
    'layer',
    'layout',
    'ldexp',
    'leastsquares',
    'legend',
    'legenditem',
    'length',
    'lift',
    'light',
    'limits',
    'line',
    'linear',
    'linecap',
    'lineinversion',
    'linejoin',
    'linemargin',
    'lineskip',
    'linetype',
    'linewidth',
    'link',
    'list',
    'lm_enorm',
    'lm_evaluate_default',
    'lm_lmdif',
    'lm_lmpar',
    'lm_minimize',
    'lm_print_default',
    'lm_print_quiet',
    'lm_qrfac',
    'lm_qrsolv',
    'locale',
    'locate',
    'locatefile',
    'location',
    'log',
    'log10',
    'log1p',
    'logaxiscoverage',
    'longitude',
    'lookup',
    'magnetize',
    'makeNode',
    'makedraw',
    'makepen',
    'map',
    'margin',
    'markangle',
    'markangleradius',
    'markanglespace',
    'markarc',
    'marker',
    'markinterval',
    'marknodes',
    'markrightangle',
    'markuniform',
    'mass',
    'masscenter',
    'massformat',
    'math',
    'max',
    'max3',
    'maxbezier',
    'maxbound',
    'maxcoords',
    'maxlength',
    'maxratio',
    'maxtimes',
    'mean',
    'medial',
    'median',
    'midpoint',
    'min',
    'min3',
    'minbezier',
    'minbound',
    'minipage',
    'minratio',
    'mintimes',
    'miterlimit',
    'momArrowPath',
    'momarrowsize',
    'monotonic',
    'multifigure',
    'nativeformat',
    'natural',
    'needshipout',
    'newl',
    'newpage',
    'newslide',
    'newton',
    'newtree',
    'nextframe',
    'nextnormal',
    'nextpage',
    'nib',
    'nodabscissa',
    'none',
    'norm',
    'normalvideo',
    'notaknot',
    'nowarn',
    'numberpage',
    'nurb',
    'object',
    'offset',
    'onpath',
    'opacity',
    'opposite',
    'orientation',
    'orig_circlenodesnumber',
    'orig_circlenodesnumber1',
    'orig_draw',
    'orig_ellipsenodesnumber',
    'orig_ellipsenodesnumber1',
    'orig_hyperbolanodesnumber',
    'orig_parabolanodesnumber',
    'origin',
    'orthic',
    'orthocentercenter',
    'outformat',
    'outline',
    'outprefix',
    'output',
    'overloadedMessage',
    'overwrite',
    'pack',
    'pad',
    'pairs',
    'palette',
    'parabola',
    'parabolanodesnumber',
    'parallel',
    'partialsum',
    'path',
    'path3',
    'pattern',
    'pause',
    'pdf',
    'pedal',
    'periodic',
    'perp',
    'perpendicular',
    'perpendicularmark',
    'phantom',
    'phi1',
    'phi2',
    'phi3',
    'photon',
    'piecewisestraight',
    'point',
    'polar',
    'polarconicroutine',
    'polargraph',
    'polygon',
    'postcontrol',
    'postscript',
    'pow10',
    'ppoint',
    'prc',
    'prc0',
    'precision',
    'precontrol',
    'prepend',
    'print_random_addresses',
    'project',
    'projection',
    'purge',
    'pwhermite',
    'quadrant',
    'quadraticroots',
    'quantize',
    'quarticroots',
    'quotient',
    'radialshade',
    'radians',
    'radicalcenter',
    'radicalline',
    'radius',
    'rand',
    'randompath',
    'rd',
    'readline',
    'realmult',
    'realquarticroots',
    'rectangle',
    'rectangular',
    'rectify',
    'reflect',
    'relabscissa',
    'relative',
    'relativedistance',
    'reldir',
    'relpoint',
    'reltime',
    'remainder',
    'remark',
    'removeDuplicates',
    'rename',
    'replace',
    'report',
    'resetdefaultpen',
    'restore',
    'restoredefaults',
    'reverse',
    'reversevideo',
    'rf',
    'rfind',
    'rgb',
    'rgba',
    'rgbint',
    'rms',
    'rotate',
    'rotateO',
    'rotation',
    'round',
    'roundbox',
    'roundedpath',
    'roundrectangle',
    'samecoordsys',
    'sameside',
    'sample',
    'save',
    'savedefaults',
    'saveline',
    'scale',
    'scale3',
    'scaleO',
    'scaleT',
    'scaleless',
    'scientific',
    'search',
    'searchtree',
    'sec',
    'secondaryX',
    'secondaryY',
    'seconds',
    'section',
    'sector',
    'seek',
    'seekeof',
    'segment',
    'sequence',
    'setpens',
    'sgn',
    'sgnd',
    'sharpangle',
    'sharpdegrees',
    'shift',
    'shiftless',
    'shipout',
    'shipout3',
    'show',
    'side',
    'simeq',
    'simpson',
    'sin',
    'single',
    'sinh',
    'size',
    'size3',
    'skewness',
    'skip',
    'slant',
    'sleep',
    'slope',
    'slopefield',
    'solve',
    'solveBVP',
    'sort',
    'sourceline',
    'sphere',
    'split',
    'sqrt',
    'square',
    'srand',
    'standardizecoordsys',
    'startScript',
    'startTrembling',
    'stdev',
    'step',
    'stickframe',
    'stickmarksize',
    'stickmarkspace',
    'stop',
    'straight',
    'straightness',
    'string',
    'stripdirectory',
    'stripextension',
    'stripfile',
    'strokepath',
    'subdivide',
    'subitem',
    'subpath',
    'substr',
    'sum',
    'surface',
    'symmedial',
    'symmedian',
    'system',
    'tab',
    'tableau',
    'tan',
    'tangent',
    'tangential',
    'tangents',
    'tanh',
    'tell',
    'tensionSpecifier',
    'tensorshade',
    'tex',
    'texcolor',
    'texify',
    'texpath',
    'texpreamble',
    'texreset',
    'texshipout',
    'texsize',
    'textpath',
    'thick',
    'thin',
    'tick',
    'tickMax',
    'tickMax3',
    'tickMin',
    'tickMin3',
    'ticklabelshift',
    'ticklocate',
    'tildeframe',
    'tildemarksize',
    'tile',
    'tiling',
    'time',
    'times',
    'title',
    'titlepage',
    'topbox',
    'transform',
    'transformation',
    'transpose',
    'tremble',
    'trembleFuzz',
    'tremble_circlenodesnumber',
    'tremble_circlenodesnumber1',
    'tremble_draw',
    'tremble_ellipsenodesnumber',
    'tremble_ellipsenodesnumber1',
    'tremble_hyperbolanodesnumber',
    'tremble_marknodes',
    'tremble_markuniform',
    'tremble_parabolanodesnumber',
    'triangle',
    'triangleAbc',
    'triangleabc',
    'triangulate',
    'tricoef',
    'tridiagonal',
    'trilinear',
    'trim',
    'trueMagnetize',
    'truepoint',
    'tube',
    'uncycle',
    'unfill',
    'uniform',
    'unit',
    'unitrand',
    'unitsize',
    'unityroot',
    'unstraighten',
    'upcase',
    'updatefunction',
    'uperiodic',
    'upscale',
    'uptodate',
    'usepackage',
    'usersetting',
    'usetypescript',
    'usleep',
    'value',
    'variance',
    'variancebiased',
    'vbox',
    'vector',
    'vectorfield',
    'verbatim',
    'view',
    'vline',
    'vperiodic',
    'vprojection',
    'warn',
    'warning',
    'windingnumber',
    'write',
    'xaxis',
    'xaxis3',
    'xaxis3At',
    'xaxisAt',
    'xequals',
    'xinput',
    'xlimits',
    'xoutput',
    'xpart',
    'xscale',
    'xscaleO',
    'xtick',
    'xtick3',
    'xtrans',
    'yaxis',
    'yaxis3',
    'yaxis3At',
    'yaxisAt',
    'yequals',
    'ylimits',
    'ypart',
    'yscale',
    'yscaleO',
    'ytick',
    'ytick3',
    'ytrans',
    'zaxis3',
    'zaxis3At',
    'zero',
    'zero3',
    'zlimits',
    'zpart',
    'ztick',
    'ztick3',
    'ztrans'
}

ASYVARNAME = {
    'AliceBlue',
    'Align',
    'Allow',
    'AntiqueWhite',
    'Apricot',
    'Aqua',
    'Aquamarine',
    'Aspect',
    'Azure',
    'BeginPoint',
    'Beige',
    'Bisque',
    'Bittersweet',
    'Black',
    'BlanchedAlmond',
    'Blue',
    'BlueGreen',
    'BlueViolet',
    'Both',
    'Break',
    'BrickRed',
    'Brown',
    'BurlyWood',
    'BurntOrange',
    'CCW',
    'CW',
    'CadetBlue',
    'CarnationPink',
    'Center',
    'Centered',
    'Cerulean',
    'Chartreuse',
    'Chocolate',
    'Coeff',
    'Coral',
    'CornflowerBlue',
    'Cornsilk',
    'Crimson',
    'Crop',
    'Cyan',
    'Dandelion',
    'DarkBlue',
    'DarkCyan',
    'DarkGoldenrod',
    'DarkGray',
    'DarkGreen',
    'DarkKhaki',
    'DarkMagenta',
    'DarkOliveGreen',
    'DarkOrange',
    'DarkOrchid',
    'DarkRed',
    'DarkSalmon',
    'DarkSeaGreen',
    'DarkSlateBlue',
    'DarkSlateGray',
    'DarkTurquoise',
    'DarkViolet',
    'DeepPink',
    'DeepSkyBlue',
    'DefaultHead',
    'DimGray',
    'DodgerBlue',
    'Dotted',
    'Draw',
    'E',
    'ENE',
    'EPS',
    'ESE',
    'E_Euler',
    'E_PC',
    'E_RK2',
    'E_RK3BS',
    'Emerald',
    'EndPoint',
    'Euler',
    'Fill',
    'FillDraw',
    'FireBrick',
    'FloralWhite',
    'ForestGreen',
    'Fuchsia',
    'Gainsboro',
    'GhostWhite',
    'Gold',
    'Goldenrod',
    'Gray',
    'Green',
    'GreenYellow',
    'Honeydew',
    'HookHead',
    'Horizontal',
    'HotPink',
    'I',
    'IgnoreAspect',
    'IndianRed',
    'Indigo',
    'Ivory',
    'JOIN_IN',
    'JOIN_OUT',
    'JungleGreen',
    'Khaki',
    'LM_DWARF',
    'LM_MACHEP',
    'LM_SQRT_DWARF',
    'LM_SQRT_GIANT',
    'LM_USERTOL',
    'Label',
    'Lavender',
    'LavenderBlush',
    'LawnGreen',
    'LeftJustified',
    'LeftSide',
    'LemonChiffon',
    'LightBlue',
    'LightCoral',
    'LightCyan',
    'LightGoldenrodYellow',
    'LightGreen',
    'LightGrey',
    'LightPink',
    'LightSalmon',
    'LightSeaGreen',
    'LightSkyBlue',
    'LightSlateGray',
    'LightSteelBlue',
    'LightYellow',
    'Lime',
    'LimeGreen',
    'Linear',
    'Linen',
    'Log',
    'Logarithmic',
    'Magenta',
    'Mahogany',
    'Mark',
    'MarkFill',
    'Maroon',
    'Max',
    'MediumAquamarine',
    'MediumBlue',
    'MediumOrchid',
    'MediumPurple',
    'MediumSeaGreen',
    'MediumSlateBlue',
    'MediumSpringGreen',
    'MediumTurquoise',
    'MediumVioletRed',
    'Melon',
    'MidPoint',
    'MidnightBlue',
    'Min',
    'MintCream',
    'MistyRose',
    'Moccasin',
    'Move',
    'MoveQuiet',
    'Mulberry',
    'N',
    'NE',
    'NNE',
    'NNW',
    'NW',
    'NavajoWhite',
    'Navy',
    'NavyBlue',
    'NoAlign',
    'NoCrop',
    'NoFill',
    'NoSide',
    'OldLace',
    'Olive',
    'OliveDrab',
    'OliveGreen',
    'Orange',
    'OrangeRed',
    'Orchid',
    'Ox',
    'Oy',
    'PC',
    'PaleGoldenrod',
    'PaleGreen',
    'PaleTurquoise',
    'PaleVioletRed',
    'PapayaWhip',
    'Peach',
    'PeachPuff',
    'Periwinkle',
    'Peru',
    'PineGreen',
    'Pink',
    'Plum',
    'PowderBlue',
    'ProcessBlue',
    'Purple',
    'RK2',
    'RK3',
    'RK3BS',
    'RK4',
    'RK5',
    'RK5DP',
    'RK5F',
    'RawSienna',
    'Red',
    'RedOrange',
    'RedViolet',
    'Rhodamine',
    'RightJustified',
    'RightSide',
    'RosyBrown',
    'RoyalBlue',
    'RoyalPurple',
    'RubineRed',
    'S',
    'SE',
    'SSE',
    'SSW',
    'SW',
    'SaddleBrown',
    'Salmon',
    'SandyBrown',
    'SeaGreen',
    'Seashell',
    'Sepia',
    'Sienna',
    'Silver',
    'SimpleHead',
    'SkyBlue',
    'SlateBlue',
    'SlateGray',
    'Snow',
    'SpringGreen',
    'SteelBlue',
    'Suppress',
    'SuppressQuiet',
    'Tan',
    'TeXHead',
    'Teal',
    'TealBlue',
    'Thistle',
    'Ticksize',
    'Tomato',
    'Turquoise',
    'UnFill',
    'VERSION',
    'Value',
    'Vertical',
    'Violet',
    'VioletRed',
    'W',
    'WNW',
    'WSW',
    'Wheat',
    'White',
    'WhiteSmoke',
    'WildStrawberry',
    'XYAlign',
    'YAlign',
    'Yellow',
    'YellowGreen',
    'YellowOrange',
    'addpenarc',
    'addpenline',
    'align',
    'allowstepping',
    'angularsystem',
    'animationdelay',
    'appendsuffix',
    'arcarrowangle',
    'arcarrowfactor',
    'arrow2sizelimit',
    'arrowangle',
    'arrowbarb',
    'arrowdir',
    'arrowfactor',
    'arrowhookfactor',
    'arrowlength',
    'arrowsizelimit',
    'arrowtexfactor',
    'authorpen',
    'axis',
    'axiscoverage',
    'axislabelfactor',
    'background',
    'backgroundcolor',
    'backgroundpen',
    'barfactor',
    'barmarksizefactor',
    'basealign',
    'baselinetemplate',
    'beveljoin',
    'bigvertexpen',
    'bigvertexsize',
    'black',
    'blue',
    'bm',
    'bottom',
    'bp',
    'brown',
    'bullet',
    'byfoci',
    'byvertices',
    'camerafactor',
    'chartreuse',
    'circlemarkradiusfactor',
    'circlenodesnumberfactor',
    'circleprecision',
    'circlescale',
    'cm',
    'codefile',
    'codepen',
    'codeskip',
    'colorPen',
    'coloredNodes',
    'coloredSegments',
    'conditionlength',
    'conicnodesfactor',
    'count',
    'cputimeformat',
    'crossmarksizefactor',
    'currentcoordsys',
    'currentlight',
    'currentpatterns',
    'currentpen',
    'currentpicture',
    'currentposition',
    'currentprojection',
    'curvilinearsystem',
    'cuttings',
    'cyan',
    'darkblue',
    'darkbrown',
    'darkcyan',
    'darkgray',
    'darkgreen',
    'darkgrey',
    'darkmagenta',
    'darkolive',
    'darkred',
    'dashdotted',
    'dashed',
    'datepen',
    'dateskip',
    'debuggerlines',
    'debugging',
    'deepblue',
    'deepcyan',
    'deepgray',
    'deepgreen',
    'deepgrey',
    'deepmagenta',
    'deepred',
    'default',
    'defaultControl',
    'defaultS',
    'defaultbackpen',
    'defaultcoordsys',
    'defaultfilename',
    'defaultformat',
    'defaultmassformat',
    'defaultpen',
    'diagnostics',
    'differentlengths',
    'dot',
    'dotfactor',
    'dotframe',
    'dotted',
    'doublelinepen',
    'doublelinespacing',
    'down',
    'duplicateFuzz',
    'ellipsenodesnumberfactor',
    'eps',
    'epsgeo',
    'epsilon',
    'evenodd',
    'extendcap',
    'fermionpen',
    'figureborder',
    'figuremattpen',
    'firstnode',
    'firststep',
    'foregroundcolor',
    'fuchsia',
    'fuzz',
    'gapfactor',
    'ghostpen',
    'gluonamplitude',
    'gluonpen',
    'gluonratio',
    'gray',
    'green',
    'grey',
    'hatchepsilon',
    'havepagenumber',
    'heavyblue',
    'heavycyan',
    'heavygray',
    'heavygreen',
    'heavygrey',
    'heavymagenta',
    'heavyred',
    'hline',
    'hwratio',
    'hyperbolanodesnumberfactor',
    'identity4',
    'ignore',
    'inXasyMode',
    'inch',
    'inches',
    'includegraphicscommand',
    'inf',
    'infinity',
    'institutionpen',
    'intMax',
    'intMin',
    'invert',
    'invisible',
    'itempen',
    'itemskip',
    'itemstep',
    'labelmargin',
    'landscape',
    'lastnode',
    'left',
    'legendhskip',
    'legendlinelength',
    'legendmargin',
    'legendmarkersize',
    'legendmaxrelativewidth',
    'legendvskip',
    'lightblue',
    'lightcyan',
    'lightgray',
    'lightgreen',
    'lightgrey',
    'lightmagenta',
    'lightolive',
    'lightred',
    'lightyellow',
    'linemargin',
    'lm_infmsg',
    'lm_shortmsg',
    'longdashdotted',
    'longdashed',
    'magenta',
    'magneticPoints',
    'magneticRadius',
    'mantissaBits',
    'markangleradius',
    'markangleradiusfactor',
    'markanglespace',
    'markanglespacefactor',
    'mediumblue',
    'mediumcyan',
    'mediumgray',
    'mediumgreen',
    'mediumgrey',
    'mediummagenta',
    'mediumred',
    'mediumyellow',
    'middle',
    'minDistDefault',
    'minblockheight',
    'minblockwidth',
    'mincirclediameter',
    'minipagemargin',
    'minipagewidth',
    'minvertexangle',
    'miterjoin',
    'mm',
    'momarrowfactor',
    'momarrowlength',
    'momarrowmargin',
    'momarrowoffset',
    'momarrowpen',
    'monoPen',
    'morepoints',
    'nCircle',
    'newbulletcolor',
    'ngraph',
    'nil',
    'nmesh',
    'nobasealign',
    'nodeMarginDefault',
    'nodesystem',
    'nomarker',
    'nopoint',
    'noprimary',
    'nullpath',
    'nullpen',
    'numarray',
    'ocgindex',
    'oldbulletcolor',
    'olive',
    'orange',
    'origin',
    'overpaint',
    'page',
    'pageheight',
    'pagemargin',
    'pagenumberalign',
    'pagenumberpen',
    'pagenumberposition',
    'pagewidth',
    'paleblue',
    'palecyan',
    'palegray',
    'palegreen',
    'palegrey',
    'palemagenta',
    'palered',
    'paleyellow',
    'parabolanodesnumberfactor',
    'perpfactor',
    'phi',
    'photonamplitude',
    'photonpen',
    'photonratio',
    'pi',
    'pink',
    'plain',
    'plus',
    'preamblenodes',
    'pt',
    'purple',
    'r3',
    'r4a',
    'r4b',
    'randMax',
    'realDigits',
    'realEpsilon',
    'realMax',
    'realMin',
    'red',
    'relativesystem',
    'reverse',
    'right',
    'roundcap',
    'roundjoin',
    'royalblue',
    'salmon',
    'saveFunctions',
    'scalarpen',
    'sequencereal',
    'settings',
    'shipped',
    'signedtrailingzero',
    'solid',
    'springgreen',
    'sqrtEpsilon',
    'squarecap',
    'squarepen',
    'startposition',
    'stdin',
    'stdout',
    'stepfactor',
    'stepfraction',
    'steppagenumberpen',
    'stepping',
    'stickframe',
    'stickmarksizefactor',
    'stickmarkspacefactor',
    'textpen',
    'ticksize',
    'tildeframe',
    'tildemarksizefactor',
    'tinv',
    'titlealign',
    'titlepagepen',
    'titlepageposition',
    'titlepen',
    'titleskip',
    'top',
    'trailingzero',
    'treeLevelStep',
    'treeMinNodeWidth',
    'treeNodeStep',
    'trembleAngle',
    'trembleFrequency',
    'trembleRandom',
    'tremblingMode',
    'undefined',
    'unitcircle',
    'unitsquare',
    'up',
    'urlpen',
    'urlskip',
    'version',
    'vertexpen',
    'vertexsize',
    'viewportmargin',
    'viewportsize',
    'vline',
    'white',
    'wye',
    'xformStack',
    'yellow',
    'ylabelwidth',
    'zerotickfuzz',
    'zerowinding'
}
