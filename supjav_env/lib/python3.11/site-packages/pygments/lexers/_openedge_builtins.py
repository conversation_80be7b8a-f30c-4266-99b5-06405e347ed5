"""
    pygments.lexers._openedge_builtins
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    Builtin list for the OpenEdgeLexer.

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see L<PERSON><PERSON><PERSON> for details.
"""

OPENEDGEKEYWORDS = (
    'ABS',
    'ABSO',
    'ABSOL',
    'ABSOLU',
    'ABSOLUT',
    'ABSOLUTE',
    'ABSTRACT',
    'ACCELERATOR',
    'ACCUM',
    'ACCUMU',
    'ACCUMUL',
    'ACCUMULA',
    'ACCUMULAT',
    'ACCUMULATE',
    'ACTIVE-FORM',
    'ACTIVE-WINDOW',
    'ADD',
    'ADD-BUFFER',
    'ADD-CALC-COLUMN',
    'ADD-COLUMNS-FROM',
    'ADD-EVENTS-PROCEDURE',
    'ADD-FIELDS-FROM',
    'ADD-FIRST',
    'ADD-INDEX-FIELD',
    'ADD-LAST',
    'ADD-LIKE-COLUMN',
    'ADD-LIKE-FIELD',
    'ADD-LIKE-INDEX',
    'ADD-NEW-FIELD',
    'ADD-NEW-INDEX',
    'ADD-SCHEMA-LOCATION',
    'ADD-SUPER-PROCEDURE',
    'ADM-DATA',
    'ADVISE',
    'ALERT-BOX',
    'ALIAS',
    'ALL',
    'ALLOW-COLUMN-SEARCHING',
    'ALLOW-REPLICATION',
    'ALTER',
    'ALWAYS-ON-TOP',
    'AMBIG',
    'AMBIGU',
    'AMBIGUO',
    'AMBIGUOU',
    'AMBIGUOUS',
    'ANALYZ',
    'ANALYZE',
    'AND',
    'ANSI-ONLY',
    'ANY',
    'ANYWHERE',
    'APPEND',
    'APPL-ALERT',
    'APPL-ALERT-',
    'APPL-ALERT-B',
    'APPL-ALERT-BO',
    'APPL-ALERT-BOX',
    'APPL-ALERT-BOXE',
    'APPL-ALERT-BOXES',
    'APPL-CONTEXT-ID',
    'APPLICATION',
    'APPLY',
    'APPSERVER-INFO',
    'APPSERVER-PASSWORD',
    'APPSERVER-USERID',
    'ARRAY-MESSAGE',
    'AS',
    'ASC',
    'ASCE',
    'ASCEN',
    'ASCEND',
    'ASCENDI',
    'ASCENDIN',
    'ASCENDING',
    'ASK-OVERWRITE',
    'ASSEMBLY',
    'ASSIGN',
    'ASYNC-REQUEST-COUNT',
    'ASYNC-REQUEST-HANDLE',
    'ASYNCHRONOUS',
    'AT',
    'ATTACHED-PAIRLIST',
    'ATTR',
    'ATTR-SPACE',
    'ATTRI',
    'ATTRIB',
    'ATTRIBU',
    'ATTRIBUT',
    'AUDIT-CONTROL',
    'AUDIT-ENABLED',
    'AUDIT-EVENT-CONTEXT',
    'AUDIT-POLICY',
    'AUTHENTICATION-FAILED',
    'AUTHORIZATION',
    'AUTO-COMP',
    'AUTO-COMPL',
    'AUTO-COMPLE',
    'AUTO-COMPLET',
    'AUTO-COMPLETI',
    'AUTO-COMPLETIO',
    'AUTO-COMPLETION',
    'AUTO-END-KEY',
    'AUTO-ENDKEY',
    'AUTO-GO',
    'AUTO-IND',
    'AUTO-INDE',
    'AUTO-INDEN',
    'AUTO-INDENT',
    'AUTO-RESIZE',
    'AUTO-RET',
    'AUTO-RETU',
    'AUTO-RETUR',
    'AUTO-RETURN',
    'AUTO-SYNCHRONIZE',
    'AUTO-Z',
    'AUTO-ZA',
    'AUTO-ZAP',
    'AUTOMATIC',
    'AVAIL',
    'AVAILA',
    'AVAILAB',
    'AVAILABL',
    'AVAILABLE',
    'AVAILABLE-FORMATS',
    'AVE',
    'AVER',
    'AVERA',
    'AVERAG',
    'AVERAGE',
    'AVG',
    'BACK',
    'BACKG',
    'BACKGR',
    'BACKGRO',
    'BACKGROU',
    'BACKGROUN',
    'BACKGROUND',
    'BACKWARD',
    'BACKWARDS',
    'BASE64-DECODE',
    'BASE64-ENCODE',
    'BASE-ADE',
    'BASE-KEY',
    'BATCH',
    'BATCH-',
    'BATCH-M',
    'BATCH-MO',
    'BATCH-MOD',
    'BATCH-MODE',
    'BATCH-SIZE',
    'BEFORE-H',
    'BEFORE-HI',
    'BEFORE-HID',
    'BEFORE-HIDE',
    'BEGIN-EVENT-GROUP',
    'BEGINS',
    'BELL',
    'BETWEEN',
    'BGC',
    'BGCO',
    'BGCOL',
    'BGCOLO',
    'BGCOLOR',
    'BIG-ENDIAN',
    'BINARY',
    'BIND',
    'BIND-WHERE',
    'BLANK',
    'BLOCK-ITERATION-DISPLAY',
    'BLOCK-LEVEL',
    'BORDER-B',
    'BORDER-BO',
    'BORDER-BOT',
    'BORDER-BOTT',
    'BORDER-BOTTO',
    'BORDER-BOTTOM-CHARS',
    'BORDER-BOTTOM-P',
    'BORDER-BOTTOM-PI',
    'BORDER-BOTTOM-PIX',
    'BORDER-BOTTOM-PIXE',
    'BORDER-BOTTOM-PIXEL',
    'BORDER-BOTTOM-PIXELS',
    'BORDER-L',
    'BORDER-LE',
    'BORDER-LEF',
    'BORDER-LEFT',
    'BORDER-LEFT-',
    'BORDER-LEFT-C',
    'BORDER-LEFT-CH',
    'BORDER-LEFT-CHA',
    'BORDER-LEFT-CHAR',
    'BORDER-LEFT-CHARS',
    'BORDER-LEFT-P',
    'BORDER-LEFT-PI',
    'BORDER-LEFT-PIX',
    'BORDER-LEFT-PIXE',
    'BORDER-LEFT-PIXEL',
    'BORDER-LEFT-PIXELS',
    'BORDER-R',
    'BORDER-RI',
    'BORDER-RIG',
    'BORDER-RIGH',
    'BORDER-RIGHT',
    'BORDER-RIGHT-',
    'BORDER-RIGHT-C',
    'BORDER-RIGHT-CH',
    'BORDER-RIGHT-CHA',
    'BORDER-RIGHT-CHAR',
    'BORDER-RIGHT-CHARS',
    'BORDER-RIGHT-P',
    'BORDER-RIGHT-PI',
    'BORDER-RIGHT-PIX',
    'BORDER-RIGHT-PIXE',
    'BORDER-RIGHT-PIXEL',
    'BORDER-RIGHT-PIXELS',
    'BORDER-T',
    'BORDER-TO',
    'BORDER-TOP',
    'BORDER-TOP-',
    'BORDER-TOP-C',
    'BORDER-TOP-CH',
    'BORDER-TOP-CHA',
    'BORDER-TOP-CHAR',
    'BORDER-TOP-CHARS',
    'BORDER-TOP-P',
    'BORDER-TOP-PI',
    'BORDER-TOP-PIX',
    'BORDER-TOP-PIXE',
    'BORDER-TOP-PIXEL',
    'BORDER-TOP-PIXELS',
    'BOX',
    'BOX-SELECT',
    'BOX-SELECTA',
    'BOX-SELECTAB',
    'BOX-SELECTABL',
    'BOX-SELECTABLE',
    'BREAK',
    'BROWSE',
    'BUFFER',
    'BUFFER-CHARS',
    'BUFFER-COMPARE',
    'BUFFER-COPY',
    'BUFFER-CREATE',
    'BUFFER-DELETE',
    'BUFFER-FIELD',
    'BUFFER-HANDLE',
    'BUFFER-LINES',
    'BUFFER-NAME',
    'BUFFER-PARTITION-ID',
    'BUFFER-RELEASE',
    'BUFFER-VALUE',
    'BUTTON',
    'BUTTONS',
    'BY',
    'BY-POINTER',
    'BY-VARIANT-POINTER',
    'CACHE',
    'CACHE-SIZE',
    'CALL',
    'CALL-NAME',
    'CALL-TYPE',
    'CAN-CREATE',
    'CAN-DELETE',
    'CAN-DO',
    'CAN-DO-DOMAIN-SUPPORT',
    'CAN-FIND',
    'CAN-QUERY',
    'CAN-READ',
    'CAN-SET',
    'CAN-WRITE',
    'CANCEL-BREAK',
    'CANCEL-BUTTON',
    'CAPS',
    'CAREFUL-PAINT',
    'CASE',
    'CASE-SEN',
    'CASE-SENS',
    'CASE-SENSI',
    'CASE-SENSIT',
    'CASE-SENSITI',
    'CASE-SENSITIV',
    'CASE-SENSITIVE',
    'CAST',
    'CATCH',
    'CDECL',
    'CENTER',
    'CENTERE',
    'CENTERED',
    'CHAINED',
    'CHARACTER',
    'CHARACTER_LENGTH',
    'CHARSET',
    'CHECK',
    'CHECKED',
    'CHOOSE',
    'CHR',
    'CLASS',
    'CLASS-TYPE',
    'CLEAR',
    'CLEAR-APPL-CONTEXT',
    'CLEAR-LOG',
    'CLEAR-SELECT',
    'CLEAR-SELECTI',
    'CLEAR-SELECTIO',
    'CLEAR-SELECTION',
    'CLEAR-SORT-ARROW',
    'CLEAR-SORT-ARROWS',
    'CLIENT-CONNECTION-ID',
    'CLIENT-PRINCIPAL',
    'CLIENT-TTY',
    'CLIENT-TYPE',
    'CLIENT-WORKSTATION',
    'CLIPBOARD',
    'CLOSE',
    'CLOSE-LOG',
    'CODE',
    'CODEBASE-LOCATOR',
    'CODEPAGE',
    'CODEPAGE-CONVERT',
    'COL',
    'COL-OF',
    'COLLATE',
    'COLON',
    'COLON-ALIGN',
    'COLON-ALIGNE',
    'COLON-ALIGNED',
    'COLOR',
    'COLOR-TABLE',
    'COLU',
    'COLUM',
    'COLUMN',
    'COLUMN-BGCOLOR',
    'COLUMN-DCOLOR',
    'COLUMN-FGCOLOR',
    'COLUMN-FONT',
    'COLUMN-LAB',
    'COLUMN-LABE',
    'COLUMN-LABEL',
    'COLUMN-MOVABLE',
    'COLUMN-OF',
    'COLUMN-PFCOLOR',
    'COLUMN-READ-ONLY',
    'COLUMN-RESIZABLE',
    'COLUMN-SCROLLING',
    'COLUMNS',
    'COM-HANDLE',
    'COM-SELF',
    'COMBO-BOX',
    'COMMAND',
    'COMPARES',
    'COMPILE',
    'COMPILER',
    'COMPLETE',
    'CONFIG-NAME',
    'CONNECT',
    'CONNECTED',
    'CONSTRUCTOR',
    'CONTAINS',
    'CONTENTS',
    'CONTEXT',
    'CONTEXT-HELP',
    'CONTEXT-HELP-FILE',
    'CONTEXT-HELP-ID',
    'CONTEXT-POPUP',
    'CONTROL',
    'CONTROL-BOX',
    'CONTROL-FRAME',
    'CONVERT',
    'CONVERT-3D-COLORS',
    'CONVERT-TO-OFFS',
    'CONVERT-TO-OFFSE',
    'CONVERT-TO-OFFSET',
    'COPY-DATASET',
    'COPY-LOB',
    'COPY-SAX-ATTRIBUTES',
    'COPY-TEMP-TABLE',
    'COUNT',
    'COUNT-OF',
    'CPCASE',
    'CPCOLL',
    'CPINTERNAL',
    'CPLOG',
    'CPPRINT',
    'CPRCODEIN',
    'CPRCODEOUT',
    'CPSTREAM',
    'CPTERM',
    'CRC-VALUE',
    'CREATE',
    'CREATE-LIKE',
    'CREATE-LIKE-SEQUENTIAL',
    'CREATE-NODE-NAMESPACE',
    'CREATE-RESULT-LIST-ENTRY',
    'CREATE-TEST-FILE',
    'CURRENT',
    'CURRENT-CHANGED',
    'CURRENT-COLUMN',
    'CURRENT-ENV',
    'CURRENT-ENVI',
    'CURRENT-ENVIR',
    'CURRENT-ENVIRO',
    'CURRENT-ENVIRON',
    'CURRENT-ENVIRONM',
    'CURRENT-ENVIRONME',
    'CURRENT-ENVIRONMEN',
    'CURRENT-ENVIRONMENT',
    'CURRENT-ITERATION',
    'CURRENT-LANG',
    'CURRENT-LANGU',
    'CURRENT-LANGUA',
    'CURRENT-LANGUAG',
    'CURRENT-LANGUAGE',
    'CURRENT-QUERY',
    'CURRENT-REQUEST-INFO',
    'CURRENT-RESPONSE-INFO',
    'CURRENT-RESULT-ROW',
    'CURRENT-ROW-MODIFIED',
    'CURRENT-VALUE',
    'CURRENT-WINDOW',
    'CURRENT_DATE',
    'CURS',
    'CURSO',
    'CURSOR',
    'CURSOR-CHAR',
    'CURSOR-LINE',
    'CURSOR-OFFSET',
    'DATA-BIND',
    'DATA-ENTRY-RET',
    'DATA-ENTRY-RETU',
    'DATA-ENTRY-RETUR',
    'DATA-ENTRY-RETURN',
    'DATA-REL',
    'DATA-RELA',
    'DATA-RELAT',
    'DATA-RELATI',
    'DATA-RELATIO',
    'DATA-RELATION',
    'DATA-SOURCE',
    'DATA-SOURCE-COMPLETE-MAP',
    'DATA-SOURCE-MODIFIED',
    'DATA-SOURCE-ROWID',
    'DATA-T',
    'DATA-TY',
    'DATA-TYP',
    'DATA-TYPE',
    'DATABASE',
    'DATASERVERS',
    'DATASET',
    'DATASET-HANDLE',
    'DATE',
    'DATE-F',
    'DATE-FO',
    'DATE-FOR',
    'DATE-FORM',
    'DATE-FORMA',
    'DATE-FORMAT',
    'DAY',
    'DB-CONTEXT',
    'DB-REFERENCES',
    'DBCODEPAGE',
    'DBCOLLATION',
    'DBNAME',
    'DBPARAM',
    'DBREST',
    'DBRESTR',
    'DBRESTRI',
    'DBRESTRIC',
    'DBRESTRICT',
    'DBRESTRICTI',
    'DBRESTRICTIO',
    'DBRESTRICTION',
    'DBRESTRICTIONS',
    'DBTASKID',
    'DBTYPE',
    'DBVERS',
    'DBVERSI',
    'DBVERSIO',
    'DBVERSION',
    'DCOLOR',
    'DDE',
    'DDE-ERROR',
    'DDE-I',
    'DDE-ID',
    'DDE-ITEM',
    'DDE-NAME',
    'DDE-TOPIC',
    'DEBLANK',
    'DEBU',
    'DEBUG',
    'DEBUG-ALERT',
    'DEBUG-LIST',
    'DEBUGGER',
    'DECIMAL',
    'DECIMALS',
    'DECLARE',
    'DECLARE-NAMESPACE',
    'DECRYPT',
    'DEFAULT',
    'DEFAULT-B',
    'DEFAULT-BU',
    'DEFAULT-BUFFER-HANDLE',
    'DEFAULT-BUT',
    'DEFAULT-BUTT',
    'DEFAULT-BUTTO',
    'DEFAULT-BUTTON',
    'DEFAULT-COMMIT',
    'DEFAULT-EX',
    'DEFAULT-EXT',
    'DEFAULT-EXTE',
    'DEFAULT-EXTEN',
    'DEFAULT-EXTENS',
    'DEFAULT-EXTENSI',
    'DEFAULT-EXTENSIO',
    'DEFAULT-EXTENSION',
    'DEFAULT-NOXL',
    'DEFAULT-NOXLA',
    'DEFAULT-NOXLAT',
    'DEFAULT-NOXLATE',
    'DEFAULT-VALUE',
    'DEFAULT-WINDOW',
    'DEFINE',
    'DEFINE-USER-EVENT-MANAGER',
    'DEFINED',
    'DEL',
    'DELE',
    'DELEGATE',
    'DELET',
    'DELETE PROCEDURE',
    'DELETE',
    'DELETE-CHAR',
    'DELETE-CHARA',
    'DELETE-CHARAC',
    'DELETE-CHARACT',
    'DELETE-CHARACTE',
    'DELETE-CHARACTER',
    'DELETE-CURRENT-ROW',
    'DELETE-LINE',
    'DELETE-RESULT-LIST-ENTRY',
    'DELETE-SELECTED-ROW',
    'DELETE-SELECTED-ROWS',
    'DELIMITER',
    'DESC',
    'DESCE',
    'DESCEN',
    'DESCEND',
    'DESCENDI',
    'DESCENDIN',
    'DESCENDING',
    'DESELECT-FOCUSED-ROW',
    'DESELECT-ROWS',
    'DESELECT-SELECTED-ROW',
    'DESELECTION',
    'DESTRUCTOR',
    'DIALOG-BOX',
    'DICT',
    'DICTI',
    'DICTIO',
    'DICTION',
    'DICTIONA',
    'DICTIONAR',
    'DICTIONARY',
    'DIR',
    'DISABLE',
    'DISABLE-AUTO-ZAP',
    'DISABLE-DUMP-TRIGGERS',
    'DISABLE-LOAD-TRIGGERS',
    'DISABLED',
    'DISCON',
    'DISCONN',
    'DISCONNE',
    'DISCONNEC',
    'DISCONNECT',
    'DISP',
    'DISPL',
    'DISPLA',
    'DISPLAY',
    'DISPLAY-MESSAGE',
    'DISPLAY-T',
    'DISPLAY-TY',
    'DISPLAY-TYP',
    'DISPLAY-TYPE',
    'DISTINCT',
    'DO',
    'DOMAIN-DESCRIPTION',
    'DOMAIN-NAME',
    'DOMAIN-TYPE',
    'DOS',
    'DOUBLE',
    'DOWN',
    'DRAG-ENABLED',
    'DROP',
    'DROP-DOWN',
    'DROP-DOWN-LIST',
    'DROP-FILE-NOTIFY',
    'DROP-TARGET',
    'DS-CLOSE-CURSOR',
    'DSLOG-MANAGER',
    'DUMP',
    'DYNAMIC',
    'DYNAMIC-ENUM',
    'DYNAMIC-FUNCTION',
    'DYNAMIC-INVOKE',
    'EACH',
    'ECHO',
    'EDGE',
    'EDGE-',
    'EDGE-C',
    'EDGE-CH',
    'EDGE-CHA',
    'EDGE-CHAR',
    'EDGE-CHARS',
    'EDGE-P',
    'EDGE-PI',
    'EDGE-PIX',
    'EDGE-PIXE',
    'EDGE-PIXEL',
    'EDGE-PIXELS',
    'EDIT-CAN-PASTE',
    'EDIT-CAN-UNDO',
    'EDIT-CLEAR',
    'EDIT-COPY',
    'EDIT-CUT',
    'EDIT-PASTE',
    'EDIT-UNDO',
    'EDITING',
    'EDITOR',
    'ELSE',
    'EMPTY',
    'EMPTY-TEMP-TABLE',
    'ENABLE',
    'ENABLED-FIELDS',
    'ENCODE',
    'ENCRYPT',
    'ENCRYPT-AUDIT-MAC-KEY',
    'ENCRYPTION-SALT',
    'END',
    'END-DOCUMENT',
    'END-ELEMENT',
    'END-EVENT-GROUP',
    'END-FILE-DROP',
    'END-KEY',
    'END-MOVE',
    'END-RESIZE',
    'END-ROW-RESIZE',
    'END-USER-PROMPT',
    'ENDKEY',
    'ENTERED',
    'ENTITY-EXPANSION-LIMIT',
    'ENTRY',
    'ENUM',
    'EQ',
    'ERROR',
    'ERROR-COL',
    'ERROR-COLU',
    'ERROR-COLUM',
    'ERROR-COLUMN',
    'ERROR-ROW',
    'ERROR-STACK-TRACE',
    'ERROR-STAT',
    'ERROR-STATU',
    'ERROR-STATUS',
    'ESCAPE',
    'ETIME',
    'EVENT',
    'EVENT-GROUP-ID',
    'EVENT-PROCEDURE',
    'EVENT-PROCEDURE-CONTEXT',
    'EVENT-T',
    'EVENT-TY',
    'EVENT-TYP',
    'EVENT-TYPE',
    'EVENTS',
    'EXCEPT',
    'EXCLUSIVE',
    'EXCLUSIVE-',
    'EXCLUSIVE-ID',
    'EXCLUSIVE-L',
    'EXCLUSIVE-LO',
    'EXCLUSIVE-LOC',
    'EXCLUSIVE-LOCK',
    'EXCLUSIVE-WEB-USER',
    'EXECUTE',
    'EXISTS',
    'EXP',
    'EXPAND',
    'EXPANDABLE',
    'EXPLICIT',
    'EXPORT',
    'EXPORT-PRINCIPAL',
    'EXTENDED',
    'EXTENT',
    'EXTERNAL',
    'FALSE',
    'FETCH',
    'FETCH-SELECTED-ROW',
    'FGC',
    'FGCO',
    'FGCOL',
    'FGCOLO',
    'FGCOLOR',
    'FIELD',
    'FIELDS',
    'FILE',
    'FILE-CREATE-DATE',
    'FILE-CREATE-TIME',
    'FILE-INFO',
    'FILE-INFOR',
    'FILE-INFORM',
    'FILE-INFORMA',
    'FILE-INFORMAT',
    'FILE-INFORMATI',
    'FILE-INFORMATIO',
    'FILE-INFORMATION',
    'FILE-MOD-DATE',
    'FILE-MOD-TIME',
    'FILE-NAME',
    'FILE-OFF',
    'FILE-OFFS',
    'FILE-OFFSE',
    'FILE-OFFSET',
    'FILE-SIZE',
    'FILE-TYPE',
    'FILENAME',
    'FILL',
    'FILL-IN',
    'FILLED',
    'FILTERS',
    'FINAL',
    'FINALLY',
    'FIND',
    'FIND-BY-ROWID',
    'FIND-CASE-SENSITIVE',
    'FIND-CURRENT',
    'FIND-FIRST',
    'FIND-GLOBAL',
    'FIND-LAST',
    'FIND-NEXT-OCCURRENCE',
    'FIND-PREV-OCCURRENCE',
    'FIND-SELECT',
    'FIND-UNIQUE',
    'FIND-WRAP-AROUND',
    'FINDER',
    'FIRST',
    'FIRST-ASYNCH-REQUEST',
    'FIRST-CHILD',
    'FIRST-COLUMN',
    'FIRST-FORM',
    'FIRST-OBJECT',
    'FIRST-OF',
    'FIRST-PROC',
    'FIRST-PROCE',
    'FIRST-PROCED',
    'FIRST-PROCEDU',
    'FIRST-PROCEDUR',
    'FIRST-PROCEDURE',
    'FIRST-SERVER',
    'FIRST-TAB-I',
    'FIRST-TAB-IT',
    'FIRST-TAB-ITE',
    'FIRST-TAB-ITEM',
    'FIT-LAST-COLUMN',
    'FIXED-ONLY',
    'FLAT-BUTTON',
    'FLOAT',
    'FOCUS',
    'FOCUSED-ROW',
    'FOCUSED-ROW-SELECTED',
    'FONT',
    'FONT-TABLE',
    'FOR',
    'FORCE-FILE',
    'FORE',
    'FOREG',
    'FOREGR',
    'FOREGRO',
    'FOREGROU',
    'FOREGROUN',
    'FOREGROUND',
    'FORM INPUT',
    'FORM',
    'FORM-LONG-INPUT',
    'FORMA',
    'FORMAT',
    'FORMATTE',
    'FORMATTED',
    'FORWARD',
    'FORWARDS',
    'FRAGMEN',
    'FRAGMENT',
    'FRAM',
    'FRAME',
    'FRAME-COL',
    'FRAME-DB',
    'FRAME-DOWN',
    'FRAME-FIELD',
    'FRAME-FILE',
    'FRAME-INDE',
    'FRAME-INDEX',
    'FRAME-LINE',
    'FRAME-NAME',
    'FRAME-ROW',
    'FRAME-SPA',
    'FRAME-SPAC',
    'FRAME-SPACI',
    'FRAME-SPACIN',
    'FRAME-SPACING',
    'FRAME-VAL',
    'FRAME-VALU',
    'FRAME-VALUE',
    'FRAME-X',
    'FRAME-Y',
    'FREQUENCY',
    'FROM',
    'FROM-C',
    'FROM-CH',
    'FROM-CHA',
    'FROM-CHAR',
    'FROM-CHARS',
    'FROM-CUR',
    'FROM-CURR',
    'FROM-CURRE',
    'FROM-CURREN',
    'FROM-CURRENT',
    'FROM-P',
    'FROM-PI',
    'FROM-PIX',
    'FROM-PIXE',
    'FROM-PIXEL',
    'FROM-PIXELS',
    'FULL-HEIGHT',
    'FULL-HEIGHT-',
    'FULL-HEIGHT-C',
    'FULL-HEIGHT-CH',
    'FULL-HEIGHT-CHA',
    'FULL-HEIGHT-CHAR',
    'FULL-HEIGHT-CHARS',
    'FULL-HEIGHT-P',
    'FULL-HEIGHT-PI',
    'FULL-HEIGHT-PIX',
    'FULL-HEIGHT-PIXE',
    'FULL-HEIGHT-PIXEL',
    'FULL-HEIGHT-PIXELS',
    'FULL-PATHN',
    'FULL-PATHNA',
    'FULL-PATHNAM',
    'FULL-PATHNAME',
    'FULL-WIDTH',
    'FULL-WIDTH-',
    'FULL-WIDTH-C',
    'FULL-WIDTH-CH',
    'FULL-WIDTH-CHA',
    'FULL-WIDTH-CHAR',
    'FULL-WIDTH-CHARS',
    'FULL-WIDTH-P',
    'FULL-WIDTH-PI',
    'FULL-WIDTH-PIX',
    'FULL-WIDTH-PIXE',
    'FULL-WIDTH-PIXEL',
    'FULL-WIDTH-PIXELS',
    'FUNCTION',
    'FUNCTION-CALL-TYPE',
    'GATEWAY',
    'GATEWAYS',
    'GE',
    'GENERATE-MD5',
    'GENERATE-PBE-KEY',
    'GENERATE-PBE-SALT',
    'GENERATE-RANDOM-KEY',
    'GENERATE-UUID',
    'GET',
    'GET-ATTR-CALL-TYPE',
    'GET-ATTRIBUTE-NODE',
    'GET-BINARY-DATA',
    'GET-BLUE',
    'GET-BLUE-',
    'GET-BLUE-V',
    'GET-BLUE-VA',
    'GET-BLUE-VAL',
    'GET-BLUE-VALU',
    'GET-BLUE-VALUE',
    'GET-BROWSE-COLUMN',
    'GET-BUFFER-HANDLE',
    'GET-BYTE',
    'GET-CALLBACK-PROC-CONTEXT',
    'GET-CALLBACK-PROC-NAME',
    'GET-CGI-LIST',
    'GET-CGI-LONG-VALUE',
    'GET-CGI-VALUE',
    'GET-CLASS',
    'GET-CODEPAGES',
    'GET-COLLATIONS',
    'GET-CONFIG-VALUE',
    'GET-CURRENT',
    'GET-DOUBLE',
    'GET-DROPPED-FILE',
    'GET-DYNAMIC',
    'GET-ERROR-COLUMN',
    'GET-ERROR-ROW',
    'GET-FILE',
    'GET-FILE-NAME',
    'GET-FILE-OFFSE',
    'GET-FILE-OFFSET',
    'GET-FIRST',
    'GET-FLOAT',
    'GET-GREEN',
    'GET-GREEN-',
    'GET-GREEN-V',
    'GET-GREEN-VA',
    'GET-GREEN-VAL',
    'GET-GREEN-VALU',
    'GET-GREEN-VALUE',
    'GET-INDEX-BY-NAMESPACE-NAME',
    'GET-INDEX-BY-QNAME',
    'GET-INT64',
    'GET-ITERATION',
    'GET-KEY-VAL',
    'GET-KEY-VALU',
    'GET-KEY-VALUE',
    'GET-LAST',
    'GET-LOCALNAME-BY-INDEX',
    'GET-LONG',
    'GET-MESSAGE',
    'GET-NEXT',
    'GET-NUMBER',
    'GET-POINTER-VALUE',
    'GET-PREV',
    'GET-PRINTERS',
    'GET-PROPERTY',
    'GET-QNAME-BY-INDEX',
    'GET-RED',
    'GET-RED-',
    'GET-RED-V',
    'GET-RED-VA',
    'GET-RED-VAL',
    'GET-RED-VALU',
    'GET-RED-VALUE',
    'GET-REPOSITIONED-ROW',
    'GET-RGB-VALUE',
    'GET-SELECTED',
    'GET-SELECTED-',
    'GET-SELECTED-W',
    'GET-SELECTED-WI',
    'GET-SELECTED-WID',
    'GET-SELECTED-WIDG',
    'GET-SELECTED-WIDGE',
    'GET-SELECTED-WIDGET',
    'GET-SHORT',
    'GET-SIGNATURE',
    'GET-SIZE',
    'GET-STRING',
    'GET-TAB-ITEM',
    'GET-TEXT-HEIGHT',
    'GET-TEXT-HEIGHT-',
    'GET-TEXT-HEIGHT-C',
    'GET-TEXT-HEIGHT-CH',
    'GET-TEXT-HEIGHT-CHA',
    'GET-TEXT-HEIGHT-CHAR',
    'GET-TEXT-HEIGHT-CHARS',
    'GET-TEXT-HEIGHT-P',
    'GET-TEXT-HEIGHT-PI',
    'GET-TEXT-HEIGHT-PIX',
    'GET-TEXT-HEIGHT-PIXE',
    'GET-TEXT-HEIGHT-PIXEL',
    'GET-TEXT-HEIGHT-PIXELS',
    'GET-TEXT-WIDTH',
    'GET-TEXT-WIDTH-',
    'GET-TEXT-WIDTH-C',
    'GET-TEXT-WIDTH-CH',
    'GET-TEXT-WIDTH-CHA',
    'GET-TEXT-WIDTH-CHAR',
    'GET-TEXT-WIDTH-CHARS',
    'GET-TEXT-WIDTH-P',
    'GET-TEXT-WIDTH-PI',
    'GET-TEXT-WIDTH-PIX',
    'GET-TEXT-WIDTH-PIXE',
    'GET-TEXT-WIDTH-PIXEL',
    'GET-TEXT-WIDTH-PIXELS',
    'GET-TYPE-BY-INDEX',
    'GET-TYPE-BY-NAMESPACE-NAME',
    'GET-TYPE-BY-QNAME',
    'GET-UNSIGNED-LONG',
    'GET-UNSIGNED-SHORT',
    'GET-URI-BY-INDEX',
    'GET-VALUE-BY-INDEX',
    'GET-VALUE-BY-NAMESPACE-NAME',
    'GET-VALUE-BY-QNAME',
    'GET-WAIT-STATE',
    'GETBYTE',
    'GLOBAL',
    'GO-ON',
    'GO-PEND',
    'GO-PENDI',
    'GO-PENDIN',
    'GO-PENDING',
    'GRANT',
    'GRAPHIC-E',
    'GRAPHIC-ED',
    'GRAPHIC-EDG',
    'GRAPHIC-EDGE',
    'GRID-FACTOR-H',
    'GRID-FACTOR-HO',
    'GRID-FACTOR-HOR',
    'GRID-FACTOR-HORI',
    'GRID-FACTOR-HORIZ',
    'GRID-FACTOR-HORIZO',
    'GRID-FACTOR-HORIZON',
    'GRID-FACTOR-HORIZONT',
    'GRID-FACTOR-HORIZONTA',
    'GRID-FACTOR-HORIZONTAL',
    'GRID-FACTOR-V',
    'GRID-FACTOR-VE',
    'GRID-FACTOR-VER',
    'GRID-FACTOR-VERT',
    'GRID-FACTOR-VERTI',
    'GRID-FACTOR-VERTIC',
    'GRID-FACTOR-VERTICA',
    'GRID-FACTOR-VERTICAL',
    'GRID-SNAP',
    'GRID-UNIT-HEIGHT',
    'GRID-UNIT-HEIGHT-',
    'GRID-UNIT-HEIGHT-C',
    'GRID-UNIT-HEIGHT-CH',
    'GRID-UNIT-HEIGHT-CHA',
    'GRID-UNIT-HEIGHT-CHARS',
    'GRID-UNIT-HEIGHT-P',
    'GRID-UNIT-HEIGHT-PI',
    'GRID-UNIT-HEIGHT-PIX',
    'GRID-UNIT-HEIGHT-PIXE',
    'GRID-UNIT-HEIGHT-PIXEL',
    'GRID-UNIT-HEIGHT-PIXELS',
    'GRID-UNIT-WIDTH',
    'GRID-UNIT-WIDTH-',
    'GRID-UNIT-WIDTH-C',
    'GRID-UNIT-WIDTH-CH',
    'GRID-UNIT-WIDTH-CHA',
    'GRID-UNIT-WIDTH-CHAR',
    'GRID-UNIT-WIDTH-CHARS',
    'GRID-UNIT-WIDTH-P',
    'GRID-UNIT-WIDTH-PI',
    'GRID-UNIT-WIDTH-PIX',
    'GRID-UNIT-WIDTH-PIXE',
    'GRID-UNIT-WIDTH-PIXEL',
    'GRID-UNIT-WIDTH-PIXELS',
    'GRID-VISIBLE',
    'GROUP',
    'GT',
    'GUID',
    'HANDLE',
    'HANDLER',
    'HAS-RECORDS',
    'HAVING',
    'HEADER',
    'HEIGHT',
    'HEIGHT-',
    'HEIGHT-C',
    'HEIGHT-CH',
    'HEIGHT-CHA',
    'HEIGHT-CHAR',
    'HEIGHT-CHARS',
    'HEIGHT-P',
    'HEIGHT-PI',
    'HEIGHT-PIX',
    'HEIGHT-PIXE',
    'HEIGHT-PIXEL',
    'HEIGHT-PIXELS',
    'HELP',
    'HEX-DECODE',
    'HEX-ENCODE',
    'HIDDEN',
    'HIDE',
    'HORI',
    'HORIZ',
    'HORIZO',
    'HORIZON',
    'HORIZONT',
    'HORIZONTA',
    'HORIZONTAL',
    'HOST-BYTE-ORDER',
    'HTML-CHARSET',
    'HTML-END-OF-LINE',
    'HTML-END-OF-PAGE',
    'HTML-FRAME-BEGIN',
    'HTML-FRAME-END',
    'HTML-HEADER-BEGIN',
    'HTML-HEADER-END',
    'HTML-TITLE-BEGIN',
    'HTML-TITLE-END',
    'HWND',
    'ICON',
    'IF',
    'IMAGE',
    'IMAGE-DOWN',
    'IMAGE-INSENSITIVE',
    'IMAGE-SIZE',
    'IMAGE-SIZE-C',
    'IMAGE-SIZE-CH',
    'IMAGE-SIZE-CHA',
    'IMAGE-SIZE-CHAR',
    'IMAGE-SIZE-CHARS',
    'IMAGE-SIZE-P',
    'IMAGE-SIZE-PI',
    'IMAGE-SIZE-PIX',
    'IMAGE-SIZE-PIXE',
    'IMAGE-SIZE-PIXEL',
    'IMAGE-SIZE-PIXELS',
    'IMAGE-UP',
    'IMMEDIATE-DISPLAY',
    'IMPLEMENTS',
    'IMPORT',
    'IMPORT-PRINCIPAL',
    'IN',
    'IN-HANDLE',
    'INCREMENT-EXCLUSIVE-ID',
    'INDEX',
    'INDEX-HINT',
    'INDEX-INFORMATION',
    'INDEXED-REPOSITION',
    'INDICATOR',
    'INFO',
    'INFOR',
    'INFORM',
    'INFORMA',
    'INFORMAT',
    'INFORMATI',
    'INFORMATIO',
    'INFORMATION',
    'INHERIT-BGC',
    'INHERIT-BGCO',
    'INHERIT-BGCOL',
    'INHERIT-BGCOLO',
    'INHERIT-BGCOLOR',
    'INHERIT-FGC',
    'INHERIT-FGCO',
    'INHERIT-FGCOL',
    'INHERIT-FGCOLO',
    'INHERIT-FGCOLOR',
    'INHERITS',
    'INIT',
    'INITI',
    'INITIA',
    'INITIAL',
    'INITIAL-DIR',
    'INITIAL-FILTER',
    'INITIALIZE-DOCUMENT-TYPE',
    'INITIATE',
    'INNER-CHARS',
    'INNER-LINES',
    'INPUT',
    'INPUT-O',
    'INPUT-OU',
    'INPUT-OUT',
    'INPUT-OUTP',
    'INPUT-OUTPU',
    'INPUT-OUTPUT',
    'INPUT-VALUE',
    'INSERT',
    'INSERT-ATTRIBUTE',
    'INSERT-B',
    'INSERT-BA',
    'INSERT-BAC',
    'INSERT-BACK',
    'INSERT-BACKT',
    'INSERT-BACKTA',
    'INSERT-BACKTAB',
    'INSERT-FILE',
    'INSERT-ROW',
    'INSERT-STRING',
    'INSERT-T',
    'INSERT-TA',
    'INSERT-TAB',
    'INT64',
    'INT',
    'INTEGER',
    'INTERFACE',
    'INTERNAL-ENTRIES',
    'INTO',
    'INVOKE',
    'IS',
    'IS-ATTR',
    'IS-ATTR-',
    'IS-ATTR-S',
    'IS-ATTR-SP',
    'IS-ATTR-SPA',
    'IS-ATTR-SPAC',
    'IS-ATTR-SPACE',
    'IS-CLASS',
    'IS-JSON',
    'IS-LEAD-BYTE',
    'IS-OPEN',
    'IS-PARAMETER-SET',
    'IS-PARTITIONED',
    'IS-ROW-SELECTED',
    'IS-SELECTED',
    'IS-XML',
    'ITEM',
    'ITEMS-PER-ROW',
    'JOIN',
    'JOIN-BY-SQLDB',
    'KBLABEL',
    'KEEP-CONNECTION-OPEN',
    'KEEP-FRAME-Z',
    'KEEP-FRAME-Z-',
    'KEEP-FRAME-Z-O',
    'KEEP-FRAME-Z-OR',
    'KEEP-FRAME-Z-ORD',
    'KEEP-FRAME-Z-ORDE',
    'KEEP-FRAME-Z-ORDER',
    'KEEP-MESSAGES',
    'KEEP-SECURITY-CACHE',
    'KEEP-TAB-ORDER',
    'KEY',
    'KEY-CODE',
    'KEY-FUNC',
    'KEY-FUNCT',
    'KEY-FUNCTI',
    'KEY-FUNCTIO',
    'KEY-FUNCTION',
    'KEY-LABEL',
    'KEYCODE',
    'KEYFUNC',
    'KEYFUNCT',
    'KEYFUNCTI',
    'KEYFUNCTIO',
    'KEYFUNCTION',
    'KEYLABEL',
    'KEYS',
    'KEYWORD',
    'KEYWORD-ALL',
    'LABEL',
    'LABEL-BGC',
    'LABEL-BGCO',
    'LABEL-BGCOL',
    'LABEL-BGCOLO',
    'LABEL-BGCOLOR',
    'LABEL-DC',
    'LABEL-DCO',
    'LABEL-DCOL',
    'LABEL-DCOLO',
    'LABEL-DCOLOR',
    'LABEL-FGC',
    'LABEL-FGCO',
    'LABEL-FGCOL',
    'LABEL-FGCOLO',
    'LABEL-FGCOLOR',
    'LABEL-FONT',
    'LABEL-PFC',
    'LABEL-PFCO',
    'LABEL-PFCOL',
    'LABEL-PFCOLO',
    'LABEL-PFCOLOR',
    'LABELS',
    'LABELS-HAVE-COLONS',
    'LANDSCAPE',
    'LANGUAGE',
    'LANGUAGES',
    'LARGE',
    'LARGE-TO-SMALL',
    'LAST',
    'LAST-ASYNCH-REQUEST',
    'LAST-BATCH',
    'LAST-CHILD',
    'LAST-EVEN',
    'LAST-EVENT',
    'LAST-FORM',
    'LAST-KEY',
    'LAST-OBJECT',
    'LAST-OF',
    'LAST-PROCE',
    'LAST-PROCED',
    'LAST-PROCEDU',
    'LAST-PROCEDUR',
    'LAST-PROCEDURE',
    'LAST-SERVER',
    'LAST-TAB-I',
    'LAST-TAB-IT',
    'LAST-TAB-ITE',
    'LAST-TAB-ITEM',
    'LASTKEY',
    'LC',
    'LDBNAME',
    'LE',
    'LEAVE',
    'LEFT-ALIGN',
    'LEFT-ALIGNE',
    'LEFT-ALIGNED',
    'LEFT-TRIM',
    'LENGTH',
    'LIBRARY',
    'LIKE',
    'LIKE-SEQUENTIAL',
    'LINE',
    'LINE-COUNT',
    'LINE-COUNTE',
    'LINE-COUNTER',
    'LIST-EVENTS',
    'LIST-ITEM-PAIRS',
    'LIST-ITEMS',
    'LIST-PROPERTY-NAMES',
    'LIST-QUERY-ATTRS',
    'LIST-SET-ATTRS',
    'LIST-WIDGETS',
    'LISTI',
    'LISTIN',
    'LISTING',
    'LITERAL-QUESTION',
    'LITTLE-ENDIAN',
    'LOAD',
    'LOAD-DOMAINS',
    'LOAD-ICON',
    'LOAD-IMAGE',
    'LOAD-IMAGE-DOWN',
    'LOAD-IMAGE-INSENSITIVE',
    'LOAD-IMAGE-UP',
    'LOAD-MOUSE-P',
    'LOAD-MOUSE-PO',
    'LOAD-MOUSE-POI',
    'LOAD-MOUSE-POIN',
    'LOAD-MOUSE-POINT',
    'LOAD-MOUSE-POINTE',
    'LOAD-MOUSE-POINTER',
    'LOAD-PICTURE',
    'LOAD-SMALL-ICON',
    'LOCAL-NAME',
    'LOCAL-VERSION-INFO',
    'LOCATOR-COLUMN-NUMBER',
    'LOCATOR-LINE-NUMBER',
    'LOCATOR-PUBLIC-ID',
    'LOCATOR-SYSTEM-ID',
    'LOCATOR-TYPE',
    'LOCK-REGISTRATION',
    'LOCKED',
    'LOG',
    'LOG-AUDIT-EVENT',
    'LOG-MANAGER',
    'LOGICAL',
    'LOGIN-EXPIRATION-TIMESTAMP',
    'LOGIN-HOST',
    'LOGIN-STATE',
    'LOGOUT',
    'LONGCHAR',
    'LOOKAHEAD',
    'LOOKUP',
    'LT',
    'MACHINE-CLASS',
    'MANDATORY',
    'MANUAL-HIGHLIGHT',
    'MAP',
    'MARGIN-EXTRA',
    'MARGIN-HEIGHT',
    'MARGIN-HEIGHT-',
    'MARGIN-HEIGHT-C',
    'MARGIN-HEIGHT-CH',
    'MARGIN-HEIGHT-CHA',
    'MARGIN-HEIGHT-CHAR',
    'MARGIN-HEIGHT-CHARS',
    'MARGIN-HEIGHT-P',
    'MARGIN-HEIGHT-PI',
    'MARGIN-HEIGHT-PIX',
    'MARGIN-HEIGHT-PIXE',
    'MARGIN-HEIGHT-PIXEL',
    'MARGIN-HEIGHT-PIXELS',
    'MARGIN-WIDTH',
    'MARGIN-WIDTH-',
    'MARGIN-WIDTH-C',
    'MARGIN-WIDTH-CH',
    'MARGIN-WIDTH-CHA',
    'MARGIN-WIDTH-CHAR',
    'MARGIN-WIDTH-CHARS',
    'MARGIN-WIDTH-P',
    'MARGIN-WIDTH-PI',
    'MARGIN-WIDTH-PIX',
    'MARGIN-WIDTH-PIXE',
    'MARGIN-WIDTH-PIXEL',
    'MARGIN-WIDTH-PIXELS',
    'MARK-NEW',
    'MARK-ROW-STATE',
    'MATCHES',
    'MAX',
    'MAX-BUTTON',
    'MAX-CHARS',
    'MAX-DATA-GUESS',
    'MAX-HEIGHT',
    'MAX-HEIGHT-C',
    'MAX-HEIGHT-CH',
    'MAX-HEIGHT-CHA',
    'MAX-HEIGHT-CHAR',
    'MAX-HEIGHT-CHARS',
    'MAX-HEIGHT-P',
    'MAX-HEIGHT-PI',
    'MAX-HEIGHT-PIX',
    'MAX-HEIGHT-PIXE',
    'MAX-HEIGHT-PIXEL',
    'MAX-HEIGHT-PIXELS',
    'MAX-ROWS',
    'MAX-SIZE',
    'MAX-VAL',
    'MAX-VALU',
    'MAX-VALUE',
    'MAX-WIDTH',
    'MAX-WIDTH-',
    'MAX-WIDTH-C',
    'MAX-WIDTH-CH',
    'MAX-WIDTH-CHA',
    'MAX-WIDTH-CHAR',
    'MAX-WIDTH-CHARS',
    'MAX-WIDTH-P',
    'MAX-WIDTH-PI',
    'MAX-WIDTH-PIX',
    'MAX-WIDTH-PIXE',
    'MAX-WIDTH-PIXEL',
    'MAX-WIDTH-PIXELS',
    'MAXI',
    'MAXIM',
    'MAXIMIZE',
    'MAXIMU',
    'MAXIMUM',
    'MAXIMUM-LEVEL',
    'MD5-DIGEST',
    'MEMBER',
    'MEMPTR-TO-NODE-VALUE',
    'MENU',
    'MENU-BAR',
    'MENU-ITEM',
    'MENU-K',
    'MENU-KE',
    'MENU-KEY',
    'MENU-M',
    'MENU-MO',
    'MENU-MOU',
    'MENU-MOUS',
    'MENU-MOUSE',
    'MENUBAR',
    'MERGE-BY-FIELD',
    'MESSAGE',
    'MESSAGE-AREA',
    'MESSAGE-AREA-FONT',
    'MESSAGE-LINES',
    'METHOD',
    'MIN',
    'MIN-BUTTON',
    'MIN-COLUMN-WIDTH-C',
    'MIN-COLUMN-WIDTH-CH',
    'MIN-COLUMN-WIDTH-CHA',
    'MIN-COLUMN-WIDTH-CHAR',
    'MIN-COLUMN-WIDTH-CHARS',
    'MIN-COLUMN-WIDTH-P',
    'MIN-COLUMN-WIDTH-PI',
    'MIN-COLUMN-WIDTH-PIX',
    'MIN-COLUMN-WIDTH-PIXE',
    'MIN-COLUMN-WIDTH-PIXEL',
    'MIN-COLUMN-WIDTH-PIXELS',
    'MIN-HEIGHT',
    'MIN-HEIGHT-',
    'MIN-HEIGHT-C',
    'MIN-HEIGHT-CH',
    'MIN-HEIGHT-CHA',
    'MIN-HEIGHT-CHAR',
    'MIN-HEIGHT-CHARS',
    'MIN-HEIGHT-P',
    'MIN-HEIGHT-PI',
    'MIN-HEIGHT-PIX',
    'MIN-HEIGHT-PIXE',
    'MIN-HEIGHT-PIXEL',
    'MIN-HEIGHT-PIXELS',
    'MIN-SIZE',
    'MIN-VAL',
    'MIN-VALU',
    'MIN-VALUE',
    'MIN-WIDTH',
    'MIN-WIDTH-',
    'MIN-WIDTH-C',
    'MIN-WIDTH-CH',
    'MIN-WIDTH-CHA',
    'MIN-WIDTH-CHAR',
    'MIN-WIDTH-CHARS',
    'MIN-WIDTH-P',
    'MIN-WIDTH-PI',
    'MIN-WIDTH-PIX',
    'MIN-WIDTH-PIXE',
    'MIN-WIDTH-PIXEL',
    'MIN-WIDTH-PIXELS',
    'MINI',
    'MINIM',
    'MINIMU',
    'MINIMUM',
    'MOD',
    'MODIFIED',
    'MODU',
    'MODUL',
    'MODULO',
    'MONTH',
    'MOUSE',
    'MOUSE-P',
    'MOUSE-PO',
    'MOUSE-POI',
    'MOUSE-POIN',
    'MOUSE-POINT',
    'MOUSE-POINTE',
    'MOUSE-POINTER',
    'MOVABLE',
    'MOVE-AFTER',
    'MOVE-AFTER-',
    'MOVE-AFTER-T',
    'MOVE-AFTER-TA',
    'MOVE-AFTER-TAB',
    'MOVE-AFTER-TAB-',
    'MOVE-AFTER-TAB-I',
    'MOVE-AFTER-TAB-IT',
    'MOVE-AFTER-TAB-ITE',
    'MOVE-AFTER-TAB-ITEM',
    'MOVE-BEFOR',
    'MOVE-BEFORE',
    'MOVE-BEFORE-',
    'MOVE-BEFORE-T',
    'MOVE-BEFORE-TA',
    'MOVE-BEFORE-TAB',
    'MOVE-BEFORE-TAB-',
    'MOVE-BEFORE-TAB-I',
    'MOVE-BEFORE-TAB-IT',
    'MOVE-BEFORE-TAB-ITE',
    'MOVE-BEFORE-TAB-ITEM',
    'MOVE-COL',
    'MOVE-COLU',
    'MOVE-COLUM',
    'MOVE-COLUMN',
    'MOVE-TO-B',
    'MOVE-TO-BO',
    'MOVE-TO-BOT',
    'MOVE-TO-BOTT',
    'MOVE-TO-BOTTO',
    'MOVE-TO-BOTTOM',
    'MOVE-TO-EOF',
    'MOVE-TO-T',
    'MOVE-TO-TO',
    'MOVE-TO-TOP',
    'MPE',
    'MTIME',
    'MULTI-COMPILE',
    'MULTIPLE',
    'MULTIPLE-KEY',
    'MULTITASKING-INTERVAL',
    'MUST-EXIST',
    'NAME',
    'NAMESPACE-PREFIX',
    'NAMESPACE-URI',
    'NATIVE',
    'NE',
    'NEEDS-APPSERVER-PROMPT',
    'NEEDS-PROMPT',
    'NEW',
    'NEW-INSTANCE',
    'NEW-ROW',
    'NEXT',
    'NEXT-COLUMN',
    'NEXT-PROMPT',
    'NEXT-ROWID',
    'NEXT-SIBLING',
    'NEXT-TAB-I',
    'NEXT-TAB-IT',
    'NEXT-TAB-ITE',
    'NEXT-TAB-ITEM',
    'NEXT-VALUE',
    'NO',
    'NO-APPLY',
    'NO-ARRAY-MESSAGE',
    'NO-ASSIGN',
    'NO-ATTR',
    'NO-ATTR-',
    'NO-ATTR-L',
    'NO-ATTR-LI',
    'NO-ATTR-LIS',
    'NO-ATTR-LIST',
    'NO-ATTR-S',
    'NO-ATTR-SP',
    'NO-ATTR-SPA',
    'NO-ATTR-SPAC',
    'NO-ATTR-SPACE',
    'NO-AUTO-VALIDATE',
    'NO-BIND-WHERE',
    'NO-BOX',
    'NO-CONSOLE',
    'NO-CONVERT',
    'NO-CONVERT-3D-COLORS',
    'NO-CURRENT-VALUE',
    'NO-DEBUG',
    'NO-DRAG',
    'NO-ECHO',
    'NO-EMPTY-SPACE',
    'NO-ERROR',
    'NO-F',
    'NO-FI',
    'NO-FIL',
    'NO-FILL',
    'NO-FOCUS',
    'NO-HELP',
    'NO-HIDE',
    'NO-INDEX-HINT',
    'NO-INHERIT-BGC',
    'NO-INHERIT-BGCO',
    'NO-INHERIT-BGCOLOR',
    'NO-INHERIT-FGC',
    'NO-INHERIT-FGCO',
    'NO-INHERIT-FGCOL',
    'NO-INHERIT-FGCOLO',
    'NO-INHERIT-FGCOLOR',
    'NO-JOIN-BY-SQLDB',
    'NO-LABE',
    'NO-LABELS',
    'NO-LOBS',
    'NO-LOCK',
    'NO-LOOKAHEAD',
    'NO-MAP',
    'NO-MES',
    'NO-MESS',
    'NO-MESSA',
    'NO-MESSAG',
    'NO-MESSAGE',
    'NO-PAUSE',
    'NO-PREFE',
    'NO-PREFET',
    'NO-PREFETC',
    'NO-PREFETCH',
    'NO-ROW-MARKERS',
    'NO-SCROLLBAR-VERTICAL',
    'NO-SEPARATE-CONNECTION',
    'NO-SEPARATORS',
    'NO-TAB-STOP',
    'NO-UND',
    'NO-UNDE',
    'NO-UNDER',
    'NO-UNDERL',
    'NO-UNDERLI',
    'NO-UNDERLIN',
    'NO-UNDERLINE',
    'NO-UNDO',
    'NO-VAL',
    'NO-VALI',
    'NO-VALID',
    'NO-VALIDA',
    'NO-VALIDAT',
    'NO-VALIDATE',
    'NO-WAIT',
    'NO-WORD-WRAP',
    'NODE-VALUE-TO-MEMPTR',
    'NONAMESPACE-SCHEMA-LOCATION',
    'NONE',
    'NORMALIZE',
    'NOT',
    'NOT-ACTIVE',
    'NOW',
    'NULL',
    'NUM-ALI',
    'NUM-ALIA',
    'NUM-ALIAS',
    'NUM-ALIASE',
    'NUM-ALIASES',
    'NUM-BUFFERS',
    'NUM-BUT',
    'NUM-BUTT',
    'NUM-BUTTO',
    'NUM-BUTTON',
    'NUM-BUTTONS',
    'NUM-COL',
    'NUM-COLU',
    'NUM-COLUM',
    'NUM-COLUMN',
    'NUM-COLUMNS',
    'NUM-COPIES',
    'NUM-DBS',
    'NUM-DROPPED-FILES',
    'NUM-ENTRIES',
    'NUM-FIELDS',
    'NUM-FORMATS',
    'NUM-ITEMS',
    'NUM-ITERATIONS',
    'NUM-LINES',
    'NUM-LOCKED-COL',
    'NUM-LOCKED-COLU',
    'NUM-LOCKED-COLUM',
    'NUM-LOCKED-COLUMN',
    'NUM-LOCKED-COLUMNS',
    'NUM-MESSAGES',
    'NUM-PARAMETERS',
    'NUM-REFERENCES',
    'NUM-REPLACED',
    'NUM-RESULTS',
    'NUM-SELECTED',
    'NUM-SELECTED-',
    'NUM-SELECTED-ROWS',
    'NUM-SELECTED-W',
    'NUM-SELECTED-WI',
    'NUM-SELECTED-WID',
    'NUM-SELECTED-WIDG',
    'NUM-SELECTED-WIDGE',
    'NUM-SELECTED-WIDGET',
    'NUM-SELECTED-WIDGETS',
    'NUM-TABS',
    'NUM-TO-RETAIN',
    'NUM-VISIBLE-COLUMNS',
    'NUMERIC',
    'NUMERIC-F',
    'NUMERIC-FO',
    'NUMERIC-FOR',
    'NUMERIC-FORM',
    'NUMERIC-FORMA',
    'NUMERIC-FORMAT',
    'OCTET-LENGTH',
    'OF',
    'OFF',
    'OK',
    'OK-CANCEL',
    'OLD',
    'ON',
    'ON-FRAME',
    'ON-FRAME-',
    'ON-FRAME-B',
    'ON-FRAME-BO',
    'ON-FRAME-BOR',
    'ON-FRAME-BORD',
    'ON-FRAME-BORDE',
    'ON-FRAME-BORDER',
    'OPEN',
    'OPSYS',
    'OPTION',
    'OR',
    'ORDERED-JOIN',
    'ORDINAL',
    'OS-APPEND',
    'OS-COMMAND',
    'OS-COPY',
    'OS-CREATE-DIR',
    'OS-DELETE',
    'OS-DIR',
    'OS-DRIVE',
    'OS-DRIVES',
    'OS-ERROR',
    'OS-GETENV',
    'OS-RENAME',
    'OTHERWISE',
    'OUTPUT',
    'OVERLAY',
    'OVERRIDE',
    'OWNER',
    'PAGE',
    'PAGE-BOT',
    'PAGE-BOTT',
    'PAGE-BOTTO',
    'PAGE-BOTTOM',
    'PAGE-NUM',
    'PAGE-NUMB',
    'PAGE-NUMBE',
    'PAGE-NUMBER',
    'PAGE-SIZE',
    'PAGE-TOP',
    'PAGE-WID',
    'PAGE-WIDT',
    'PAGE-WIDTH',
    'PAGED',
    'PARAM',
    'PARAME',
    'PARAMET',
    'PARAMETE',
    'PARAMETER',
    'PARENT',
    'PARSE-STATUS',
    'PARTIAL-KEY',
    'PASCAL',
    'PASSWORD-FIELD',
    'PATHNAME',
    'PAUSE',
    'PBE-HASH-ALG',
    'PBE-HASH-ALGO',
    'PBE-HASH-ALGOR',
    'PBE-HASH-ALGORI',
    'PBE-HASH-ALGORIT',
    'PBE-HASH-ALGORITH',
    'PBE-HASH-ALGORITHM',
    'PBE-KEY-ROUNDS',
    'PDBNAME',
    'PERSIST',
    'PERSISTE',
    'PERSISTEN',
    'PERSISTENT',
    'PERSISTENT-CACHE-DISABLED',
    'PFC',
    'PFCO',
    'PFCOL',
    'PFCOLO',
    'PFCOLOR',
    'PIXELS',
    'PIXELS-PER-COL',
    'PIXELS-PER-COLU',
    'PIXELS-PER-COLUM',
    'PIXELS-PER-COLUMN',
    'PIXELS-PER-ROW',
    'POPUP-M',
    'POPUP-ME',
    'POPUP-MEN',
    'POPUP-MENU',
    'POPUP-O',
    'POPUP-ON',
    'POPUP-ONL',
    'POPUP-ONLY',
    'PORTRAIT',
    'POSITION',
    'PRECISION',
    'PREFER-DATASET',
    'PREPARE-STRING',
    'PREPARED',
    'PREPROC',
    'PREPROCE',
    'PREPROCES',
    'PREPROCESS',
    'PRESEL',
    'PRESELE',
    'PRESELEC',
    'PRESELECT',
    'PREV',
    'PREV-COLUMN',
    'PREV-SIBLING',
    'PREV-TAB-I',
    'PREV-TAB-IT',
    'PREV-TAB-ITE',
    'PREV-TAB-ITEM',
    'PRIMARY',
    'PRINTER',
    'PRINTER-CONTROL-HANDLE',
    'PRINTER-HDC',
    'PRINTER-NAME',
    'PRINTER-PORT',
    'PRINTER-SETUP',
    'PRIVATE',
    'PRIVATE-D',
    'PRIVATE-DA',
    'PRIVATE-DAT',
    'PRIVATE-DATA',
    'PRIVILEGES',
    'PROC-HA',
    'PROC-HAN',
    'PROC-HAND',
    'PROC-HANDL',
    'PROC-HANDLE',
    'PROC-ST',
    'PROC-STA',
    'PROC-STAT',
    'PROC-STATU',
    'PROC-STATUS',
    'PROC-TEXT',
    'PROC-TEXT-BUFFER',
    'PROCE',
    'PROCED',
    'PROCEDU',
    'PROCEDUR',
    'PROCEDURE',
    'PROCEDURE-CALL-TYPE',
    'PROCEDURE-TYPE',
    'PROCESS',
    'PROFILER',
    'PROGRAM-NAME',
    'PROGRESS',
    'PROGRESS-S',
    'PROGRESS-SO',
    'PROGRESS-SOU',
    'PROGRESS-SOUR',
    'PROGRESS-SOURC',
    'PROGRESS-SOURCE',
    'PROMPT',
    'PROMPT-F',
    'PROMPT-FO',
    'PROMPT-FOR',
    'PROMSGS',
    'PROPATH',
    'PROPERTY',
    'PROTECTED',
    'PROVERS',
    'PROVERSI',
    'PROVERSIO',
    'PROVERSION',
    'PROXY',
    'PROXY-PASSWORD',
    'PROXY-USERID',
    'PUBLIC',
    'PUBLIC-ID',
    'PUBLISH',
    'PUBLISHED-EVENTS',
    'PUT',
    'PUT-BYTE',
    'PUT-DOUBLE',
    'PUT-FLOAT',
    'PUT-INT64',
    'PUT-KEY-VAL',
    'PUT-KEY-VALU',
    'PUT-KEY-VALUE',
    'PUT-LONG',
    'PUT-SHORT',
    'PUT-STRING',
    'PUT-UNSIGNED-LONG',
    'PUTBYTE',
    'QUERY',
    'QUERY-CLOSE',
    'QUERY-OFF-END',
    'QUERY-OPEN',
    'QUERY-PREPARE',
    'QUERY-TUNING',
    'QUESTION',
    'QUIT',
    'QUOTER',
    'R-INDEX',
    'RADIO-BUTTONS',
    'RADIO-SET',
    'RANDOM',
    'RAW',
    'RAW-TRANSFER',
    'RCODE-INFO',
    'RCODE-INFOR',
    'RCODE-INFORM',
    'RCODE-INFORMA',
    'RCODE-INFORMAT',
    'RCODE-INFORMATI',
    'RCODE-INFORMATIO',
    'RCODE-INFORMATION',
    'READ-AVAILABLE',
    'READ-EXACT-NUM',
    'READ-FILE',
    'READ-JSON',
    'READ-ONLY',
    'READ-XML',
    'READ-XMLSCHEMA',
    'READKEY',
    'REAL',
    'RECID',
    'RECORD-LENGTH',
    'RECT',
    'RECTA',
    'RECTAN',
    'RECTANG',
    'RECTANGL',
    'RECTANGLE',
    'RECURSIVE',
    'REFERENCE-ONLY',
    'REFRESH',
    'REFRESH-AUDIT-POLICY',
    'REFRESHABLE',
    'REGISTER-DOMAIN',
    'RELEASE',
    'REMOTE',
    'REMOVE-EVENTS-PROCEDURE',
    'REMOVE-SUPER-PROCEDURE',
    'REPEAT',
    'REPLACE',
    'REPLACE-SELECTION-TEXT',
    'REPOSITION',
    'REPOSITION-BACKWARD',
    'REPOSITION-FORWARD',
    'REPOSITION-MODE',
    'REPOSITION-TO-ROW',
    'REPOSITION-TO-ROWID',
    'REQUEST',
    'REQUEST-INFO',
    'RESET',
    'RESIZA',
    'RESIZAB',
    'RESIZABL',
    'RESIZABLE',
    'RESIZE',
    'RESPONSE-INFO',
    'RESTART-ROW',
    'RESTART-ROWID',
    'RETAIN',
    'RETAIN-SHAPE',
    'RETRY',
    'RETRY-CANCEL',
    'RETURN',
    'RETURN-ALIGN',
    'RETURN-ALIGNE',
    'RETURN-INS',
    'RETURN-INSE',
    'RETURN-INSER',
    'RETURN-INSERT',
    'RETURN-INSERTE',
    'RETURN-INSERTED',
    'RETURN-TO-START-DI',
    'RETURN-TO-START-DIR',
    'RETURN-VAL',
    'RETURN-VALU',
    'RETURN-VALUE',
    'RETURN-VALUE-DATA-TYPE',
    'RETURNS',
    'REVERSE-FROM',
    'REVERT',
    'REVOKE',
    'RGB-VALUE',
    'RIGHT-ALIGNED',
    'RIGHT-TRIM',
    'ROLES',
    'ROUND',
    'ROUTINE-LEVEL',
    'ROW',
    'ROW-HEIGHT-CHARS',
    'ROW-HEIGHT-PIXELS',
    'ROW-MARKERS',
    'ROW-OF',
    'ROW-RESIZABLE',
    'ROWID',
    'RULE',
    'RUN',
    'RUN-PROCEDURE',
    'SAVE CACHE',
    'SAVE',
    'SAVE-AS',
    'SAVE-FILE',
    'SAX-COMPLE',
    'SAX-COMPLET',
    'SAX-COMPLETE',
    'SAX-PARSE',
    'SAX-PARSE-FIRST',
    'SAX-PARSE-NEXT',
    'SAX-PARSER-ERROR',
    'SAX-RUNNING',
    'SAX-UNINITIALIZED',
    'SAX-WRITE-BEGIN',
    'SAX-WRITE-COMPLETE',
    'SAX-WRITE-CONTENT',
    'SAX-WRITE-ELEMENT',
    'SAX-WRITE-ERROR',
    'SAX-WRITE-IDLE',
    'SAX-WRITE-TAG',
    'SAX-WRITER',
    'SCHEMA',
    'SCHEMA-LOCATION',
    'SCHEMA-MARSHAL',
    'SCHEMA-PATH',
    'SCREEN',
    'SCREEN-IO',
    'SCREEN-LINES',
    'SCREEN-VAL',
    'SCREEN-VALU',
    'SCREEN-VALUE',
    'SCROLL',
    'SCROLL-BARS',
    'SCROLL-DELTA',
    'SCROLL-OFFSET',
    'SCROLL-TO-CURRENT-ROW',
    'SCROLL-TO-I',
    'SCROLL-TO-IT',
    'SCROLL-TO-ITE',
    'SCROLL-TO-ITEM',
    'SCROLL-TO-SELECTED-ROW',
    'SCROLLABLE',
    'SCROLLBAR-H',
    'SCROLLBAR-HO',
    'SCROLLBAR-HOR',
    'SCROLLBAR-HORI',
    'SCROLLBAR-HORIZ',
    'SCROLLBAR-HORIZO',
    'SCROLLBAR-HORIZON',
    'SCROLLBAR-HORIZONT',
    'SCROLLBAR-HORIZONTA',
    'SCROLLBAR-HORIZONTAL',
    'SCROLLBAR-V',
    'SCROLLBAR-VE',
    'SCROLLBAR-VER',
    'SCROLLBAR-VERT',
    'SCROLLBAR-VERTI',
    'SCROLLBAR-VERTIC',
    'SCROLLBAR-VERTICA',
    'SCROLLBAR-VERTICAL',
    'SCROLLED-ROW-POS',
    'SCROLLED-ROW-POSI',
    'SCROLLED-ROW-POSIT',
    'SCROLLED-ROW-POSITI',
    'SCROLLED-ROW-POSITIO',
    'SCROLLED-ROW-POSITION',
    'SCROLLING',
    'SDBNAME',
    'SEAL',
    'SEAL-TIMESTAMP',
    'SEARCH',
    'SEARCH-SELF',
    'SEARCH-TARGET',
    'SECTION',
    'SECURITY-POLICY',
    'SEEK',
    'SELECT',
    'SELECT-ALL',
    'SELECT-FOCUSED-ROW',
    'SELECT-NEXT-ROW',
    'SELECT-PREV-ROW',
    'SELECT-ROW',
    'SELECTABLE',
    'SELECTED',
    'SELECTION',
    'SELECTION-END',
    'SELECTION-LIST',
    'SELECTION-START',
    'SELECTION-TEXT',
    'SELF',
    'SEND',
    'SEND-SQL-STATEMENT',
    'SENSITIVE',
    'SEPARATE-CONNECTION',
    'SEPARATOR-FGCOLOR',
    'SEPARATORS',
    'SERIALIZABLE',
    'SERIALIZE-HIDDEN',
    'SERIALIZE-NAME',
    'SERVER',
    'SERVER-CONNECTION-BOUND',
    'SERVER-CONNECTION-BOUND-REQUEST',
    'SERVER-CONNECTION-CONTEXT',
    'SERVER-CONNECTION-ID',
    'SERVER-OPERATING-MODE',
    'SESSION',
    'SESSION-ID',
    'SET',
    'SET-APPL-CONTEXT',
    'SET-ATTR-CALL-TYPE',
    'SET-ATTRIBUTE-NODE',
    'SET-BLUE',
    'SET-BLUE-',
    'SET-BLUE-V',
    'SET-BLUE-VA',
    'SET-BLUE-VAL',
    'SET-BLUE-VALU',
    'SET-BLUE-VALUE',
    'SET-BREAK',
    'SET-BUFFERS',
    'SET-CALLBACK',
    'SET-CLIENT',
    'SET-COMMIT',
    'SET-CONTENTS',
    'SET-CURRENT-VALUE',
    'SET-DB-CLIENT',
    'SET-DYNAMIC',
    'SET-EVENT-MANAGER-OPTION',
    'SET-GREEN',
    'SET-GREEN-',
    'SET-GREEN-V',
    'SET-GREEN-VA',
    'SET-GREEN-VAL',
    'SET-GREEN-VALU',
    'SET-GREEN-VALUE',
    'SET-INPUT-SOURCE',
    'SET-OPTION',
    'SET-OUTPUT-DESTINATION',
    'SET-PARAMETER',
    'SET-POINTER-VALUE',
    'SET-PROPERTY',
    'SET-RED',
    'SET-RED-',
    'SET-RED-V',
    'SET-RED-VA',
    'SET-RED-VAL',
    'SET-RED-VALU',
    'SET-RED-VALUE',
    'SET-REPOSITIONED-ROW',
    'SET-RGB-VALUE',
    'SET-ROLLBACK',
    'SET-SELECTION',
    'SET-SIZE',
    'SET-SORT-ARROW',
    'SET-WAIT-STATE',
    'SETUSER',
    'SETUSERI',
    'SETUSERID',
    'SHA1-DIGEST',
    'SHARE',
    'SHARE-',
    'SHARE-L',
    'SHARE-LO',
    'SHARE-LOC',
    'SHARE-LOCK',
    'SHARED',
    'SHOW-IN-TASKBAR',
    'SHOW-STAT',
    'SHOW-STATS',
    'SIDE-LAB',
    'SIDE-LABE',
    'SIDE-LABEL',
    'SIDE-LABEL-H',
    'SIDE-LABEL-HA',
    'SIDE-LABEL-HAN',
    'SIDE-LABEL-HAND',
    'SIDE-LABEL-HANDL',
    'SIDE-LABEL-HANDLE',
    'SIDE-LABELS',
    'SIGNATURE',
    'SILENT',
    'SIMPLE',
    'SINGLE',
    'SINGLE-RUN',
    'SINGLETON',
    'SIZE',
    'SIZE-C',
    'SIZE-CH',
    'SIZE-CHA',
    'SIZE-CHAR',
    'SIZE-CHARS',
    'SIZE-P',
    'SIZE-PI',
    'SIZE-PIX',
    'SIZE-PIXE',
    'SIZE-PIXEL',
    'SIZE-PIXELS',
    'SKIP',
    'SKIP-DELETED-RECORD',
    'SLIDER',
    'SMALL-ICON',
    'SMALL-TITLE',
    'SMALLINT',
    'SOME',
    'SORT',
    'SORT-ASCENDING',
    'SORT-NUMBER',
    'SOURCE',
    'SOURCE-PROCEDURE',
    'SPACE',
    'SQL',
    'SQRT',
    'SSL-SERVER-NAME',
    'STANDALONE',
    'START',
    'START-DOCUMENT',
    'START-ELEMENT',
    'START-MOVE',
    'START-RESIZE',
    'START-ROW-RESIZE',
    'STATE-DETAIL',
    'STATIC',
    'STATUS',
    'STATUS-AREA',
    'STATUS-AREA-FONT',
    'STDCALL',
    'STOP',
    'STOP-AFTER',
    'STOP-PARSING',
    'STOPPE',
    'STOPPED',
    'STORED-PROC',
    'STORED-PROCE',
    'STORED-PROCED',
    'STORED-PROCEDU',
    'STORED-PROCEDUR',
    'STORED-PROCEDURE',
    'STREAM',
    'STREAM-HANDLE',
    'STREAM-IO',
    'STRETCH-TO-FIT',
    'STRICT',
    'STRICT-ENTITY-RESOLUTION',
    'STRING',
    'STRING-VALUE',
    'STRING-XREF',
    'SUB-AVE',
    'SUB-AVER',
    'SUB-AVERA',
    'SUB-AVERAG',
    'SUB-AVERAGE',
    'SUB-COUNT',
    'SUB-MAXIMUM',
    'SUB-MENU',
    'SUB-MIN',
    'SUB-MINIMUM',
    'SUB-TOTAL',
    'SUBSCRIBE',
    'SUBST',
    'SUBSTI',
    'SUBSTIT',
    'SUBSTITU',
    'SUBSTITUT',
    'SUBSTITUTE',
    'SUBSTR',
    'SUBSTRI',
    'SUBSTRIN',
    'SUBSTRING',
    'SUBTYPE',
    'SUM',
    'SUM-MAX',
    'SUM-MAXI',
    'SUM-MAXIM',
    'SUM-MAXIMU',
    'SUPER',
    'SUPER-PROCEDURES',
    'SUPPRESS-NAMESPACE-PROCESSING',
    'SUPPRESS-W',
    'SUPPRESS-WA',
    'SUPPRESS-WAR',
    'SUPPRESS-WARN',
    'SUPPRESS-WARNI',
    'SUPPRESS-WARNIN',
    'SUPPRESS-WARNING',
    'SUPPRESS-WARNINGS',
    'SYMMETRIC-ENCRYPTION-ALGORITHM',
    'SYMMETRIC-ENCRYPTION-IV',
    'SYMMETRIC-ENCRYPTION-KEY',
    'SYMMETRIC-SUPPORT',
    'SYSTEM-ALERT',
    'SYSTEM-ALERT-',
    'SYSTEM-ALERT-B',
    'SYSTEM-ALERT-BO',
    'SYSTEM-ALERT-BOX',
    'SYSTEM-ALERT-BOXE',
    'SYSTEM-ALERT-BOXES',
    'SYSTEM-DIALOG',
    'SYSTEM-HELP',
    'SYSTEM-ID',
    'TAB-POSITION',
    'TAB-STOP',
    'TABLE',
    'TABLE-HANDLE',
    'TABLE-NUMBER',
    'TABLE-SCAN',
    'TARGET',
    'TARGET-PROCEDURE',
    'TEMP-DIR',
    'TEMP-DIRE',
    'TEMP-DIREC',
    'TEMP-DIRECT',
    'TEMP-DIRECTO',
    'TEMP-DIRECTOR',
    'TEMP-DIRECTORY',
    'TEMP-TABLE',
    'TEMP-TABLE-PREPARE',
    'TERM',
    'TERMI',
    'TERMIN',
    'TERMINA',
    'TERMINAL',
    'TERMINATE',
    'TEXT',
    'TEXT-CURSOR',
    'TEXT-SEG-GROW',
    'TEXT-SELECTED',
    'THEN',
    'THIS-OBJECT',
    'THIS-PROCEDURE',
    'THREAD-SAFE',
    'THREE-D',
    'THROUGH',
    'THROW',
    'THRU',
    'TIC-MARKS',
    'TIME',
    'TIME-SOURCE',
    'TITLE',
    'TITLE-BGC',
    'TITLE-BGCO',
    'TITLE-BGCOL',
    'TITLE-BGCOLO',
    'TITLE-BGCOLOR',
    'TITLE-DC',
    'TITLE-DCO',
    'TITLE-DCOL',
    'TITLE-DCOLO',
    'TITLE-DCOLOR',
    'TITLE-FGC',
    'TITLE-FGCO',
    'TITLE-FGCOL',
    'TITLE-FGCOLO',
    'TITLE-FGCOLOR',
    'TITLE-FO',
    'TITLE-FON',
    'TITLE-FONT',
    'TO',
    'TO-ROWID',
    'TODAY',
    'TOGGLE-BOX',
    'TOOLTIP',
    'TOOLTIPS',
    'TOP-NAV-QUERY',
    'TOP-ONLY',
    'TOPIC',
    'TOTAL',
    'TRAILING',
    'TRANS',
    'TRANS-INIT-PROCEDURE',
    'TRANSACTION',
    'TRANSACTION-MODE',
    'TRANSPARENT',
    'TRIGGER',
    'TRIGGERS',
    'TRIM',
    'TRUE',
    'TRUNC',
    'TRUNCA',
    'TRUNCAT',
    'TRUNCATE',
    'TYPE',
    'TYPE-OF',
    'UNBOX',
    'UNBUFF',
    'UNBUFFE',
    'UNBUFFER',
    'UNBUFFERE',
    'UNBUFFERED',
    'UNDERL',
    'UNDERLI',
    'UNDERLIN',
    'UNDERLINE',
    'UNDO',
    'UNFORM',
    'UNFORMA',
    'UNFORMAT',
    'UNFORMATT',
    'UNFORMATTE',
    'UNFORMATTED',
    'UNION',
    'UNIQUE',
    'UNIQUE-ID',
    'UNIQUE-MATCH',
    'UNIX',
    'UNLESS-HIDDEN',
    'UNLOAD',
    'UNSIGNED-LONG',
    'UNSUBSCRIBE',
    'UP',
    'UPDATE',
    'UPDATE-ATTRIBUTE',
    'URL',
    'URL-DECODE',
    'URL-ENCODE',
    'URL-PASSWORD',
    'URL-USERID',
    'USE',
    'USE-DICT-EXPS',
    'USE-FILENAME',
    'USE-INDEX',
    'USE-REVVIDEO',
    'USE-TEXT',
    'USE-UNDERLINE',
    'USE-WIDGET-POOL',
    'USER',
    'USER-ID',
    'USERID',
    'USING',
    'V6DISPLAY',
    'V6FRAME',
    'VALID-EVENT',
    'VALID-HANDLE',
    'VALID-OBJECT',
    'VALIDATE',
    'VALIDATE-EXPRESSION',
    'VALIDATE-MESSAGE',
    'VALIDATE-SEAL',
    'VALIDATION-ENABLED',
    'VALUE',
    'VALUE-CHANGED',
    'VALUES',
    'VAR',
    'VARI',
    'VARIA',
    'VARIAB',
    'VARIABL',
    'VARIABLE',
    'VERBOSE',
    'VERSION',
    'VERT',
    'VERTI',
    'VERTIC',
    'VERTICA',
    'VERTICAL',
    'VIEW',
    'VIEW-AS',
    'VIEW-FIRST-COLUMN-ON-REOPEN',
    'VIRTUAL-HEIGHT',
    'VIRTUAL-HEIGHT-',
    'VIRTUAL-HEIGHT-C',
    'VIRTUAL-HEIGHT-CH',
    'VIRTUAL-HEIGHT-CHA',
    'VIRTUAL-HEIGHT-CHAR',
    'VIRTUAL-HEIGHT-CHARS',
    'VIRTUAL-HEIGHT-P',
    'VIRTUAL-HEIGHT-PI',
    'VIRTUAL-HEIGHT-PIX',
    'VIRTUAL-HEIGHT-PIXE',
    'VIRTUAL-HEIGHT-PIXEL',
    'VIRTUAL-HEIGHT-PIXELS',
    'VIRTUAL-WIDTH',
    'VIRTUAL-WIDTH-',
    'VIRTUAL-WIDTH-C',
    'VIRTUAL-WIDTH-CH',
    'VIRTUAL-WIDTH-CHA',
    'VIRTUAL-WIDTH-CHAR',
    'VIRTUAL-WIDTH-CHARS',
    'VIRTUAL-WIDTH-P',
    'VIRTUAL-WIDTH-PI',
    'VIRTUAL-WIDTH-PIX',
    'VIRTUAL-WIDTH-PIXE',
    'VIRTUAL-WIDTH-PIXEL',
    'VIRTUAL-WIDTH-PIXELS',
    'VISIBLE',
    'VOID',
    'WAIT',
    'WAIT-FOR',
    'WARNING',
    'WEB-CONTEXT',
    'WEEKDAY',
    'WHEN',
    'WHERE',
    'WHILE',
    'WIDGET',
    'WIDGET-E',
    'WIDGET-EN',
    'WIDGET-ENT',
    'WIDGET-ENTE',
    'WIDGET-ENTER',
    'WIDGET-ID',
    'WIDGET-L',
    'WIDGET-LE',
    'WIDGET-LEA',
    'WIDGET-LEAV',
    'WIDGET-LEAVE',
    'WIDGET-POOL',
    'WIDTH',
    'WIDTH-',
    'WIDTH-C',
    'WIDTH-CH',
    'WIDTH-CHA',
    'WIDTH-CHAR',
    'WIDTH-CHARS',
    'WIDTH-P',
    'WIDTH-PI',
    'WIDTH-PIX',
    'WIDTH-PIXE',
    'WIDTH-PIXEL',
    'WIDTH-PIXELS',
    'WINDOW',
    'WINDOW-MAXIM',
    'WINDOW-MAXIMI',
    'WINDOW-MAXIMIZ',
    'WINDOW-MAXIMIZE',
    'WINDOW-MAXIMIZED',
    'WINDOW-MINIM',
    'WINDOW-MINIMI',
    'WINDOW-MINIMIZ',
    'WINDOW-MINIMIZE',
    'WINDOW-MINIMIZED',
    'WINDOW-NAME',
    'WINDOW-NORMAL',
    'WINDOW-STA',
    'WINDOW-STAT',
    'WINDOW-STATE',
    'WINDOW-SYSTEM',
    'WITH',
    'WORD-INDEX',
    'WORD-WRAP',
    'WORK-AREA-HEIGHT-PIXELS',
    'WORK-AREA-WIDTH-PIXELS',
    'WORK-AREA-X',
    'WORK-AREA-Y',
    'WORK-TAB',
    'WORK-TABL',
    'WORK-TABLE',
    'WORKFILE',
    'WRITE',
    'WRITE-CDATA',
    'WRITE-CHARACTERS',
    'WRITE-COMMENT',
    'WRITE-DATA-ELEMENT',
    'WRITE-EMPTY-ELEMENT',
    'WRITE-ENTITY-REF',
    'WRITE-EXTERNAL-DTD',
    'WRITE-FRAGMENT',
    'WRITE-JSON',
    'WRITE-MESSAGE',
    'WRITE-PROCESSING-INSTRUCTION',
    'WRITE-STATUS',
    'WRITE-XML',
    'WRITE-XMLSCHEMA',
    'X',
    'X-OF',
    'XCODE',
    'XML-DATA-TYPE',
    'XML-ENTITY-EXPANSION-LIMIT',
    'XML-NODE-TYPE',
    'XML-SCHEMA-PATH',
    'XML-STRICT-ENTITY-RESOLUTION',
    'XML-SUPPRESS-NAMESPACE-PROCESSING',
    'XREF',
    'XREF-XML',
    'Y',
    'Y-OF',
    'YEAR',
    'YEAR-OFFSET',
    'YES',
    'YES-NO',
    'YES-NO-CANCEL'
)
