# -*- coding: UTF-8 -*-
# -- FILE GENERATED BY: convert_i18n_yaml.py with i18n.yml
# pylint: disable=line-too-long

languages = \
{'ar': {'and': [u'*', u'\u0648'],
        'background': [u'\u0627\u0644\u062e\u0644\u0641\u064a\u0629'],
        'but': [u'*', u'\u0644\u0643\u0646'],
        'examples': [u'\u0627\u0645\u062b\u0644\u0629'],
        'feature': [u'\u062e\u0627\u0635\u064a\u0629'],
        'given': [u'*', u'\u0628\u0641\u0631\u0636'],
        'name': [u'Arabic'],
        'native': [u'\u0627\u0644\u0639\u0631\u0628\u064a\u0629'],
        'scenario': [u'\u0633\u064a\u0646\u0627\u0631\u064a\u0648'],
        'scenario_outline': [u'\u0633\u064a\u0646\u0627\u0631\u064a\u0648 \u0645\u062e\u0637\u0637'],
        'then': [u'*', u'\u0627\u0630\u0627\u064b', u'\u062b\u0645'],
        'when': [u'*',
                 u'\u0645\u062a\u0649',
                 u'\u0639\u0646\u062f\u0645\u0627']},
 'bg': {'and': [u'*', u'\u0418'],
        'background': [u'\u041f\u0440\u0435\u0434\u0438\u0441\u0442\u043e\u0440\u0438\u044f'],
        'but': [u'*', u'\u041d\u043e'],
        'examples': [u'\u041f\u0440\u0438\u043c\u0435\u0440\u0438'],
        'feature': [u'\u0424\u0443\u043d\u043a\u0446\u0438\u043e\u043d\u0430\u043b\u043d\u043e\u0441\u0442'],
        'given': [u'*', u'\u0414\u0430\u0434\u0435\u043d\u043e'],
        'name': [u'Bulgarian'],
        'native': [u'\u0431\u044a\u043b\u0433\u0430\u0440\u0441\u043a\u0438'],
        'scenario': [u'\u0421\u0446\u0435\u043d\u0430\u0440\u0438\u0439'],
        'scenario_outline': [u'\u0420\u0430\u043c\u043a\u0430 \u043d\u0430 \u0441\u0446\u0435\u043d\u0430\u0440\u0438\u0439'],
        'then': [u'*', u'\u0422\u043e'],
        'when': [u'*', u'\u041a\u043e\u0433\u0430\u0442\u043e']},
 'ca': {'and': [u'*', u'I'],
        'background': [u'Rerefons', u'Antecedents'],
        'but': [u'*', u'Per\xf2'],
        'examples': [u'Exemples'],
        'feature': [u'Caracter\xedstica', u'Funcionalitat'],
        'given': [u'*', u'Donat', u'Donada', u'At\xe8s', u'Atesa'],
        'name': [u'Catalan'],
        'native': [u'catal\xe0'],
        'scenario': [u'Escenari'],
        'scenario_outline': [u"Esquema de l'escenari"],
        'then': [u'*', u'Aleshores', u'Cal'],
        'when': [u'*', u'Quan']},
 'cs': {'and': [u'*', u'A', u'A tak\xe9'],
        'background': [u'Pozad\xed', u'Kontext'],
        'but': [u'*', u'Ale'],
        'examples': [u'P\u0159\xedklady'],
        'feature': [u'Po\u017eadavek'],
        'given': [u'*', u'Pokud', u'Za p\u0159edpokladu'],
        'name': [u'Czech'],
        'native': [u'\u010cesky'],
        'scenario': [u'Sc\xe9n\xe1\u0159'],
        'scenario_outline': [u'N\xe1\u010drt Sc\xe9n\xe1\u0159e',
                             u'Osnova sc\xe9n\xe1\u0159e'],
        'then': [u'*', u'Pak'],
        'when': [u'*', u'Kdy\u017e']},
 'cy-GB': {'and': [u'*', u'A'],
           'background': [u'Cefndir'],
           'but': [u'*', u'Ond'],
           'examples': [u'Enghreifftiau'],
           'feature': [u'Arwedd'],
           'given': [u'*', u'Anrhegedig a'],
           'name': [u'Welsh'],
           'native': [u'Cymraeg'],
           'scenario': [u'Scenario'],
           'scenario_outline': [u'Scenario Amlinellol'],
           'then': [u'*', u'Yna'],
           'when': [u'*', u'Pryd']},
 'da': {'and': [u'*', u'Og'],
        'background': [u'Baggrund'],
        'but': [u'*', u'Men'],
        'examples': [u'Eksempler'],
        'feature': [u'Egenskab'],
        'given': [u'*', u'Givet'],
        'name': [u'Danish'],
        'native': [u'dansk'],
        'scenario': [u'Scenarie'],
        'scenario_outline': [u'Abstrakt Scenario'],
        'then': [u'*', u'S\xe5'],
        'when': [u'*', u'N\xe5r']},
 'de': {'and': [u'*', u'Und'],
        'background': [u'Grundlage'],
        'but': [u'*', u'Aber'],
        'examples': [u'Beispiele'],
        'feature': [u'Funktionalit\xe4t'],
        'given': [u'*', u'Angenommen', u'Gegeben sei'],
        'name': [u'German'],
        'native': [u'Deutsch'],
        'scenario': [u'Szenario'],
        'scenario_outline': [u'Szenariogrundriss'],
        'then': [u'*', u'Dann'],
        'when': [u'*', u'Wenn']},
 'en': {'and': [u'*', u'And'],
        'background': [u'Background'],
        'but': [u'*', u'But'],
        'examples': [u'Examples', u'Scenarios'],
        'feature': [u'Feature'],
        'given': [u'*', u'Given'],
        'name': [u'English'],
        'native': [u'English'],
        'scenario': [u'Scenario'],
        'scenario_outline': [u'Scenario Outline', u'Scenario Template'],
        'then': [u'*', u'Then'],
        'when': [u'*', u'When']},
 'en-Scouse': {'and': [u'*', u'An'],
               'background': [u'Dis is what went down'],
               'but': [u'*', u'Buh'],
               'examples': [u'Examples'],
               'feature': [u'Feature'],
               'given': [u'*', u'Givun', u'Youse know when youse got'],
               'name': [u'Scouse'],
               'native': [u'Scouse'],
               'scenario': [u'The thing of it is'],
               'scenario_outline': [u'Wharrimean is'],
               'then': [u'*', u'Dun', u'Den youse gotta'],
               'when': [u'*', u'Wun', u'Youse know like when']},
 'en-au': {'and': [u'*', u'N'],
           'background': [u'Background'],
           'but': [u'*', u'Cept'],
           'examples': [u'Cobber'],
           'feature': [u'Crikey'],
           'given': [u'*', u'Ya know how'],
           'name': [u'Australian'],
           'native': [u'Australian'],
           'scenario': [u'Mate'],
           'scenario_outline': [u'Blokes'],
           'then': [u'*', u'Ya gotta'],
           'when': [u'*', u'When']},
 'en-lol': {'and': [u'*', u'AN'],
            'background': [u'B4'],
            'but': [u'*', u'BUT'],
            'examples': [u'EXAMPLZ'],
            'feature': [u'OH HAI'],
            'given': [u'*', u'I CAN HAZ'],
            'name': [u'LOLCAT'],
            'native': [u'LOLCAT'],
            'scenario': [u'MISHUN'],
            'scenario_outline': [u'MISHUN SRSLY'],
            'then': [u'*', u'DEN'],
            'when': [u'*', u'WEN']},
 'en-pirate': {'and': [u'*', u'Aye'],
               'background': [u'Yo-ho-ho'],
               'but': [u'*', u'Avast!'],
               'examples': [u'Dead men tell no tales'],
               'feature': [u'Ahoy matey!'],
               'given': [u'*', u'Gangway!'],
               'name': [u'Pirate'],
               'native': [u'Pirate'],
               'scenario': [u'Heave to'],
               'scenario_outline': [u'Shiver me timbers'],
               'then': [u'*', u'Let go and haul'],
               'when': [u'*', u'Blimey!']},
 'en-tx': {'and': [u'*', u"And y'all"],
           'background': [u'Background'],
           'but': [u'*', u"But y'all"],
           'examples': [u'Examples'],
           'feature': [u'Feature'],
           'given': [u'*', u"Given y'all"],
           'name': [u'Texan'],
           'native': [u'Texan'],
           'scenario': [u'Scenario'],
           'scenario_outline': [u"All y'all"],
           'then': [u'*', u"Then y'all"],
           'when': [u'*', u"When y'all"]},
 'eo': {'and': [u'*', u'Kaj'],
        'background': [u'Fono'],
        'but': [u'*', u'Sed'],
        'examples': [u'Ekzemploj'],
        'feature': [u'Trajto'],
        'given': [u'*', u'Donita\u0135o'],
        'name': [u'Esperanto'],
        'native': [u'Esperanto'],
        'scenario': [u'Scenaro'],
        'scenario_outline': [u'Konturo de la scenaro'],
        'then': [u'*', u'Do'],
        'when': [u'*', u'Se']},
 'es': {'and': [u'*', u'Y'],
        'background': [u'Antecedentes'],
        'but': [u'*', u'Pero'],
        'examples': [u'Ejemplos'],
        'feature': [u'Caracter\xedstica'],
        'given': [u'*', u'Dado', u'Dada', u'Dados', u'Dadas'],
        'name': [u'Spanish'],
        'native': [u'espa\xf1ol'],
        'scenario': [u'Escenario'],
        'scenario_outline': [u'Esquema del escenario'],
        'then': [u'*', u'Entonces'],
        'when': [u'*', u'Cuando']},
 'et': {'and': [u'*', u'Ja'],
        'background': [u'Taust'],
        'but': [u'*', u'Kuid'],
        'examples': [u'Juhtumid'],
        'feature': [u'Omadus'],
        'given': [u'*', u'Eeldades'],
        'name': [u'Estonian'],
        'native': [u'eesti keel'],
        'scenario': [u'Stsenaarium'],
        'scenario_outline': [u'Raamstsenaarium'],
        'then': [u'*', u'Siis'],
        'when': [u'*', u'Kui']},
 'fi': {'and': [u'*', u'Ja'],
        'background': [u'Tausta'],
        'but': [u'*', u'Mutta'],
        'examples': [u'Tapaukset'],
        'feature': [u'Ominaisuus'],
        'given': [u'*', u'Oletetaan'],
        'name': [u'Finnish'],
        'native': [u'suomi'],
        'scenario': [u'Tapaus'],
        'scenario_outline': [u'Tapausaihio'],
        'then': [u'*', u'Niin'],
        'when': [u'*', u'Kun']},
 'fr': {'and': [u'*', u'Et'],
        'background': [u'Contexte'],
        'but': [u'*', u'Mais'],
        'examples': [u'Exemples'],
        'feature': [u'Fonctionnalit\xe9'],
        'given': [u'*',
                  u'Soit',
                  u'Etant donn\xe9',
                  u'Etant donn\xe9e',
                  u'Etant donn\xe9s',
                  u'Etant donn\xe9es',
                  u'\xc9tant donn\xe9',
                  u'\xc9tant donn\xe9e',
                  u'\xc9tant donn\xe9s',
                  u'\xc9tant donn\xe9es'],
        'name': [u'French'],
        'native': [u'fran\xe7ais'],
        'scenario': [u'Sc\xe9nario'],
        'scenario_outline': [u'Plan du sc\xe9nario', u'Plan du Sc\xe9nario'],
        'then': [u'*', u'Alors'],
        'when': [u'*', u'Quand', u'Lorsque', u"Lorsqu'<"]},
 'gl': {'and': [u'*', u'E'],
        'background': [u'Contexto'],
        'but': [u'*', u'Mais', u'Pero'],
        'examples': [u'Exemplos'],
        'feature': [u'Caracter\xedstica'],
        'given': [u'*', u'Dado', u'Dada', u'Dados', u'Dadas'],
        'name': [u'Galician'],
        'native': [u'galego'],
        'scenario': [u'Escenario'],
        'scenario_outline': [u'Esbozo do escenario'],
        'then': [u'*', u'Ent\xf3n', u'Logo'],
        'when': [u'*', u'Cando']},
 'he': {'and': [u'*', u'\u05d5\u05d2\u05dd'],
        'background': [u'\u05e8\u05e7\u05e2'],
        'but': [u'*', u'\u05d0\u05d1\u05dc'],
        'examples': [u'\u05d3\u05d5\u05d2\u05de\u05d0\u05d5\u05ea'],
        'feature': [u'\u05ea\u05db\u05d5\u05e0\u05d4'],
        'given': [u'*', u'\u05d1\u05d4\u05d9\u05e0\u05ea\u05df'],
        'name': [u'Hebrew'],
        'native': [u'\u05e2\u05d1\u05e8\u05d9\u05ea'],
        'scenario': [u'\u05ea\u05e8\u05d7\u05d9\u05e9'],
        'scenario_outline': [u'\u05ea\u05d1\u05e0\u05d9\u05ea \u05ea\u05e8\u05d7\u05d9\u05e9'],
        'then': [u'*', u'\u05d0\u05d6', u'\u05d0\u05d6\u05d9'],
        'when': [u'*', u'\u05db\u05d0\u05e9\u05e8']},
 'hr': {'and': [u'*', u'I'],
        'background': [u'Pozadina'],
        'but': [u'*', u'Ali'],
        'examples': [u'Primjeri', u'Scenariji'],
        'feature': [u'Osobina', u'Mogu\u0107nost', u'Mogucnost'],
        'given': [u'*', u'Zadan', u'Zadani', u'Zadano'],
        'name': [u'Croatian'],
        'native': [u'hrvatski'],
        'scenario': [u'Scenarij'],
        'scenario_outline': [u'Skica', u'Koncept'],
        'then': [u'*', u'Onda'],
        'when': [u'*', u'Kada', u'Kad']},
 'hu': {'and': [u'*', u'\xc9s'],
        'background': [u'H\xe1tt\xe9r'],
        'but': [u'*', u'De'],
        'examples': [u'P\xe9ld\xe1k'],
        'feature': [u'Jellemz\u0151'],
        'given': [u'*', u'Amennyiben', u'Adott'],
        'name': [u'Hungarian'],
        'native': [u'magyar'],
        'scenario': [u'Forgat\xf3k\xf6nyv'],
        'scenario_outline': [u'Forgat\xf3k\xf6nyv v\xe1zlat'],
        'then': [u'*', u'Akkor'],
        'when': [u'*', u'Majd', u'Ha', u'Amikor']},
 'id': {'and': [u'*', u'Dan'],
        'background': [u'Dasar'],
        'but': [u'*', u'Tapi'],
        'examples': [u'Contoh'],
        'feature': [u'Fitur'],
        'given': [u'*', u'Dengan'],
        'name': [u'Indonesian'],
        'native': [u'Bahasa Indonesia'],
        'scenario': [u'Skenario'],
        'scenario_outline': [u'Skenario konsep'],
        'then': [u'*', u'Maka'],
        'when': [u'*', u'Ketika']},
 'is': {'and': [u'*', u'Og'],
        'background': [u'Bakgrunnur'],
        'but': [u'*', u'En'],
        'examples': [u'D\xe6mi', u'Atbur\xf0ar\xe1sir'],
        'feature': [u'Eiginleiki'],
        'given': [u'*', u'Ef'],
        'name': [u'Icelandic'],
        'native': [u'\xcdslenska'],
        'scenario': [u'Atbur\xf0ar\xe1s'],
        'scenario_outline': [u'L\xfdsing Atbur\xf0ar\xe1sar',
                             u'L\xfdsing D\xe6ma'],
        'then': [u'*', u'\xde\xe1'],
        'when': [u'*', u'\xdeegar']},
 'it': {'and': [u'*', u'E'],
        'background': [u'Contesto'],
        'but': [u'*', u'Ma'],
        'examples': [u'Esempi'],
        'feature': [u'Funzionalit\xe0'],
        'given': [u'*', u'Dato', u'Data', u'Dati', u'Date'],
        'name': [u'Italian'],
        'native': [u'italiano'],
        'scenario': [u'Scenario'],
        'scenario_outline': [u'Schema dello scenario'],
        'then': [u'*', u'Allora'],
        'when': [u'*', u'Quando']},
 'ja': {'and': [u'*', u'\u304b\u3064<'],
        'background': [u'\u80cc\u666f'],
        'but': [u'*',
                u'\u3057\u304b\u3057<',
                u'\u4f46\u3057<',
                u'\u305f\u3060\u3057<'],
        'examples': [u'\u4f8b', u'\u30b5\u30f3\u30d7\u30eb'],
        'feature': [u'\u30d5\u30a3\u30fc\u30c1\u30e3', u'\u6a5f\u80fd'],
        'given': [u'*', u'\u524d\u63d0<'],
        'name': [u'Japanese'],
        'native': [u'\u65e5\u672c\u8a9e'],
        'scenario': [u'\u30b7\u30ca\u30ea\u30aa'],
        'scenario_outline': [u'\u30b7\u30ca\u30ea\u30aa\u30a2\u30a6\u30c8\u30e9\u30a4\u30f3',
                             u'\u30b7\u30ca\u30ea\u30aa\u30c6\u30f3\u30d7\u30ec\u30fc\u30c8',
                             u'\u30c6\u30f3\u30d7\u30ec',
                             u'\u30b7\u30ca\u30ea\u30aa\u30c6\u30f3\u30d7\u30ec'],
        'then': [u'*', u'\u306a\u3089\u3070<'],
        'when': [u'*', u'\u3082\u3057<']},
 'ko': {'and': [u'*', u'\uadf8\ub9ac\uace0<'],
        'background': [u'\ubc30\uacbd'],
        'but': [u'*', u'\ud558\uc9c0\ub9cc<', u'\ub2e8<'],
        'examples': [u'\uc608'],
        'feature': [u'\uae30\ub2a5'],
        'given': [u'*', u'\uc870\uac74<', u'\uba3c\uc800<'],
        'name': [u'Korean'],
        'native': [u'\ud55c\uad6d\uc5b4'],
        'scenario': [u'\uc2dc\ub098\ub9ac\uc624'],
        'scenario_outline': [u'\uc2dc\ub098\ub9ac\uc624 \uac1c\uc694'],
        'then': [u'*', u'\uadf8\ub7ec\uba74<'],
        'when': [u'*', u'\ub9cc\uc77c<', u'\ub9cc\uc57d<']},
 'lt': {'and': [u'*', u'Ir'],
        'background': [u'Kontekstas'],
        'but': [u'*', u'Bet'],
        'examples': [u'Pavyzd\u017eiai', u'Scenarijai', u'Variantai'],
        'feature': [u'Savyb\u0117'],
        'given': [u'*', u'Duota'],
        'name': [u'Lithuanian'],
        'native': [u'lietuvi\u0173 kalba'],
        'scenario': [u'Scenarijus'],
        'scenario_outline': [u'Scenarijaus \u0161ablonas'],
        'then': [u'*', u'Tada'],
        'when': [u'*', u'Kai']},
 'lu': {'and': [u'*', u'an', u'a'],
        'background': [u'Hannergrond'],
        'but': [u'*', u'awer', u'm\xe4'],
        'examples': [u'Beispiller'],
        'feature': [u'Funktionalit\xe9it'],
        'given': [u'*', u'ugeholl'],
        'name': [u'Luxemburgish'],
        'native': [u'L\xebtzebuergesch'],
        'scenario': [u'Szenario'],
        'scenario_outline': [u'Plang vum Szenario'],
        'then': [u'*', u'dann'],
        'when': [u'*', u'wann']},
 'lv': {'and': [u'*', u'Un'],
        'background': [u'Konteksts', u'Situ\u0101cija'],
        'but': [u'*', u'Bet'],
        'examples': [u'Piem\u0113ri', u'Paraugs'],
        'feature': [u'Funkcionalit\u0101te', u'F\u012b\u010da'],
        'given': [u'*', u'Kad'],
        'name': [u'Latvian'],
        'native': [u'latvie\u0161u'],
        'scenario': [u'Scen\u0101rijs'],
        'scenario_outline': [u'Scen\u0101rijs p\u0113c parauga'],
        'then': [u'*', u'Tad'],
        'when': [u'*', u'Ja']},
 'nl': {'and': [u'*', u'En'],
        'background': [u'Achtergrond'],
        'but': [u'*', u'Maar'],
        'examples': [u'Voorbeelden'],
        'feature': [u'Functionaliteit'],
        'given': [u'*', u'Gegeven', u'Stel'],
        'name': [u'Dutch'],
        'native': [u'Nederlands'],
        'scenario': [u'Scenario'],
        'scenario_outline': [u'Abstract Scenario'],
        'then': [u'*', u'Dan'],
        'when': [u'*', u'Als']},
 'no': {'and': [u'*', u'Og'],
        'background': [u'Bakgrunn'],
        'but': [u'*', u'Men'],
        'examples': [u'Eksempler'],
        'feature': [u'Egenskap'],
        'given': [u'*', u'Gitt'],
        'name': [u'Norwegian'],
        'native': [u'norsk'],
        'scenario': [u'Scenario'],
        'scenario_outline': [u'Scenariomal', u'Abstrakt Scenario'],
        'then': [u'*', u'S\xe5'],
        'when': [u'*', u'N\xe5r']},
 'pl': {'and': [u'*', u'Oraz', u'I'],
        'background': [u'Za\u0142o\u017cenia'],
        'but': [u'*', u'Ale'],
        'examples': [u'Przyk\u0142ady'],
        'feature': [u'W\u0142a\u015bciwo\u015b\u0107'],
        'given': [u'*', u'Zak\u0142adaj\u0105c', u'Maj\u0105c'],
        'name': [u'Polish'],
        'native': [u'polski'],
        'scenario': [u'Scenariusz'],
        'scenario_outline': [u'Szablon scenariusza'],
        'then': [u'*', u'Wtedy'],
        'when': [u'*', u'Je\u017celi', u'Je\u015bli']},
 'pt': {'and': [u'*', u'E'],
        'background': [u'Contexto'],
        'but': [u'*', u'Mas'],
        'examples': [u'Exemplos'],
        'feature': [u'Funcionalidade'],
        'given': [u'*', u'Dado', u'Dada', u'Dados', u'Dadas'],
        'name': [u'Portuguese'],
        'native': [u'portugu\xeas'],
        'scenario': [u'Cen\xe1rio', u'Cenario'],
        'scenario_outline': [u'Esquema do Cen\xe1rio', u'Esquema do Cenario'],
        'then': [u'*', u'Ent\xe3o', u'Entao'],
        'when': [u'*', u'Quando']},
 'ro': {'and': [u'*', u'Si', u'\u0218i', u'\u015ei'],
        'background': [u'Context'],
        'but': [u'*', u'Dar'],
        'examples': [u'Exemple'],
        'feature': [u'Functionalitate',
                    u'Func\u021bionalitate',
                    u'Func\u0163ionalitate'],
        'given': [u'*',
                  u'Date fiind',
                  u'Dat fiind',
                  u'Dati fiind',
                  u'Da\u021bi fiind',
                  u'Da\u0163i fiind'],
        'name': [u'Romanian'],
        'native': [u'rom\xe2n\u0103'],
        'scenario': [u'Scenariu'],
        'scenario_outline': [u'Structura scenariu',
                             u'Structur\u0103 scenariu'],
        'then': [u'*', u'Atunci'],
        'when': [u'*', u'Cand', u'C\xe2nd']},
 'ru': {'and': [u'*',
                u'\u0418',
                u'\u041a \u0442\u043e\u043c\u0443 \u0436\u0435'],
        'background': [u'\u041f\u0440\u0435\u0434\u044b\u0441\u0442\u043e\u0440\u0438\u044f',
                       u'\u041a\u043e\u043d\u0442\u0435\u043a\u0441\u0442'],
        'but': [u'*', u'\u041d\u043e', u'\u0410'],
        'examples': [u'\u041f\u0440\u0438\u043c\u0435\u0440\u044b'],
        'feature': [u'\u0424\u0443\u043d\u043a\u0446\u0438\u044f',
                    u'\u0424\u0443\u043d\u043a\u0446\u0438\u043e\u043d\u0430\u043b',
                    u'\u0421\u0432\u043e\u0439\u0441\u0442\u0432\u043e'],
        'given': [u'*',
                  u'\u0414\u043e\u043f\u0443\u0441\u0442\u0438\u043c',
                  u'\u0414\u0430\u043d\u043e',
                  u'\u041f\u0443\u0441\u0442\u044c'],
        'name': [u'Russian'],
        'native': [u'\u0440\u0443\u0441\u0441\u043a\u0438\u0439'],
        'scenario': [u'\u0421\u0446\u0435\u043d\u0430\u0440\u0438\u0439'],
        'scenario_outline': [u'\u0421\u0442\u0440\u0443\u043a\u0442\u0443\u0440\u0430 \u0441\u0446\u0435\u043d\u0430\u0440\u0438\u044f'],
        'then': [u'*', u'\u0422\u043e', u'\u0422\u043e\u0433\u0434\u0430'],
        'when': [u'*',
                 u'\u0415\u0441\u043b\u0438',
                 u'\u041a\u043e\u0433\u0434\u0430']},
 'sk': {'and': [u'*', u'A'],
        'background': [u'Pozadie'],
        'but': [u'*', u'Ale'],
        'examples': [u'Pr\xedklady'],
        'feature': [u'Po\u017eiadavka'],
        'given': [u'*', u'Pokia\u013e'],
        'name': [u'Slovak'],
        'native': [u'Slovensky'],
        'scenario': [u'Scen\xe1r'],
        'scenario_outline': [u'N\xe1\u010drt Scen\xe1ru'],
        'then': [u'*', u'Tak'],
        'when': [u'*', u'Ke\u010f']},
 'sr-Cyrl': {'and': [u'*', u'\u0418'],
             'background': [u'\u041a\u043e\u043d\u0442\u0435\u043a\u0441\u0442',
                            u'\u041e\u0441\u043d\u043e\u0432\u0430',
                            u'\u041f\u043e\u0437\u0430\u0434\u0438\u043d\u0430'],
             'but': [u'*', u'\u0410\u043b\u0438'],
             'examples': [u'\u041f\u0440\u0438\u043c\u0435\u0440\u0438',
                          u'\u0421\u0446\u0435\u043d\u0430\u0440\u0438\u0458\u0438'],
             'feature': [u'\u0424\u0443\u043d\u043a\u0446\u0438\u043e\u043d\u0430\u043b\u043d\u043e\u0441\u0442',
                         u'\u041c\u043e\u0433\u0443\u045b\u043d\u043e\u0441\u0442',
                         u'\u041e\u0441\u043e\u0431\u0438\u043d\u0430'],
             'given': [u'*',
                       u'\u0417\u0430\u0434\u0430\u0442\u043e',
                       u'\u0417\u0430\u0434\u0430\u0442\u0435',
                       u'\u0417\u0430\u0434\u0430\u0442\u0438'],
             'name': [u'Serbian'],
             'native': [u'\u0421\u0440\u043f\u0441\u043a\u0438'],
             'scenario': [u'\u0421\u0446\u0435\u043d\u0430\u0440\u0438\u043e',
                          u'\u041f\u0440\u0438\u043c\u0435\u0440'],
             'scenario_outline': [u'\u0421\u0442\u0440\u0443\u043a\u0442\u0443\u0440\u0430 \u0441\u0446\u0435\u043d\u0430\u0440\u0438\u0458\u0430',
                                  u'\u0421\u043a\u0438\u0446\u0430',
                                  u'\u041a\u043e\u043d\u0446\u0435\u043f\u0442'],
             'then': [u'*', u'\u041e\u043d\u0434\u0430'],
             'when': [u'*',
                      u'\u041a\u0430\u0434\u0430',
                      u'\u041a\u0430\u0434']},
 'sr-Latn': {'and': [u'*', u'I'],
             'background': [u'Kontekst', u'Osnova', u'Pozadina'],
             'but': [u'*', u'Ali'],
             'examples': [u'Primeri', u'Scenariji'],
             'feature': [u'Funkcionalnost',
                         u'Mogu\u0107nost',
                         u'Mogucnost',
                         u'Osobina'],
             'given': [u'*', u'Zadato', u'Zadate', u'Zatati'],
             'name': [u'Serbian (Latin)'],
             'native': [u'Srpski (Latinica)'],
             'scenario': [u'Scenario', u'Primer'],
             'scenario_outline': [u'Struktura scenarija',
                                  u'Skica',
                                  u'Koncept'],
             'then': [u'*', u'Onda'],
             'when': [u'*', u'Kada', u'Kad']},
 'sv': {'and': [u'*', u'Och'],
        'background': [u'Bakgrund'],
        'but': [u'*', u'Men'],
        'examples': [u'Exempel'],
        'feature': [u'Egenskap'],
        'given': [u'*', u'Givet'],
        'name': [u'Swedish'],
        'native': [u'Svenska'],
        'scenario': [u'Scenario'],
        'scenario_outline': [u'Abstrakt Scenario', u'Scenariomall'],
        'then': [u'*', u'S\xe5'],
        'when': [u'*', u'N\xe4r']},
 'tr': {'and': [u'*', u'Ve'],
        'background': [u'Ge\xe7mi\u015f'],
        'but': [u'*', u'Fakat', u'Ama'],
        'examples': [u'\xd6rnekler'],
        'feature': [u'\xd6zellik'],
        'given': [u'*', u'Diyelim ki'],
        'name': [u'Turkish'],
        'native': [u'T\xfcrk\xe7e'],
        'scenario': [u'Senaryo'],
        'scenario_outline': [u'Senaryo tasla\u011f\u0131'],
        'then': [u'*', u'O zaman'],
        'when': [u'*', u'E\u011fer ki']},
 'uk': {'and': [u'*',
                u'\u0406',
                u'\u0410 \u0442\u0430\u043a\u043e\u0436',
                u'\u0422\u0430'],
        'background': [u'\u041f\u0435\u0440\u0435\u0434\u0443\u043c\u043e\u0432\u0430'],
        'but': [u'*', u'\u0410\u043b\u0435'],
        'examples': [u'\u041f\u0440\u0438\u043a\u043b\u0430\u0434\u0438'],
        'feature': [u'\u0424\u0443\u043d\u043a\u0446\u0456\u043e\u043d\u0430\u043b'],
        'given': [u'*',
                  u'\u041f\u0440\u0438\u043f\u0443\u0441\u0442\u0438\u043c\u043e',
                  u'\u041f\u0440\u0438\u043f\u0443\u0441\u0442\u0438\u043c\u043e, \u0449\u043e',
                  u'\u041d\u0435\u0445\u0430\u0439',
                  u'\u0414\u0430\u043d\u043e'],
        'name': [u'Ukrainian'],
        'native': [u'\u0423\u043a\u0440\u0430\u0457\u043d\u0441\u044c\u043a\u0430'],
        'scenario': [u'\u0421\u0446\u0435\u043d\u0430\u0440\u0456\u0439'],
        'scenario_outline': [u'\u0421\u0442\u0440\u0443\u043a\u0442\u0443\u0440\u0430 \u0441\u0446\u0435\u043d\u0430\u0440\u0456\u044e'],
        'then': [u'*', u'\u0422\u043e', u'\u0422\u043e\u0434\u0456'],
        'when': [u'*',
                 u'\u042f\u043a\u0449\u043e',
                 u'\u041a\u043e\u043b\u0438']},
 'uz': {'and': [u'*', u'\u0412\u0430'],
        'background': [u'\u0422\u0430\u0440\u0438\u0445'],
        'but': [u'*',
                u'\u041b\u0435\u043a\u0438\u043d',
                u'\u0411\u0438\u0440\u043e\u043a',
                u'\u0410\u043c\u043c\u043e'],
        'examples': [u'\u041c\u0438\u0441\u043e\u043b\u043b\u0430\u0440'],
        'feature': [u'\u0424\u0443\u043d\u043a\u0446\u0438\u043e\u043d\u0430\u043b'],
        'given': [u'*', u'\u0410\u0433\u0430\u0440'],
        'name': [u'Uzbek'],
        'native': [u'\u0423\u0437\u0431\u0435\u043a\u0447\u0430'],
        'scenario': [u'\u0421\u0446\u0435\u043d\u0430\u0440\u0438\u0439'],
        'scenario_outline': [u'\u0421\u0446\u0435\u043d\u0430\u0440\u0438\u0439 \u0441\u0442\u0440\u0443\u043a\u0442\u0443\u0440\u0430\u0441\u0438'],
        'then': [u'*', u'\u0423\u043d\u0434\u0430'],
        'when': [u'*', u'\u0410\u0433\u0430\u0440']},
 'vi': {'and': [u'*', u'V\xe0'],
        'background': [u'B\u1ed1i c\u1ea3nh'],
        'but': [u'*', u'Nh\u01b0ng'],
        'examples': [u'D\u1eef li\u1ec7u'],
        'feature': [u'T\xednh n\u0103ng'],
        'given': [u'*', u'Bi\u1ebft', u'Cho'],
        'name': [u'Vietnamese'],
        'native': [u'Ti\u1ebfng Vi\u1ec7t'],
        'scenario': [u'T\xecnh hu\u1ed1ng', u'K\u1ecbch b\u1ea3n'],
        'scenario_outline': [u'Khung t\xecnh hu\u1ed1ng',
                             u'Khung k\u1ecbch b\u1ea3n'],
        'then': [u'*', u'Th\xec'],
        'when': [u'*', u'Khi']},
 'zh-CN': {'and': [u'*', u'\u800c\u4e14<'],
           'background': [u'\u80cc\u666f'],
           'but': [u'*', u'\u4f46\u662f<'],
           'examples': [u'\u4f8b\u5b50'],
           'feature': [u'\u529f\u80fd'],
           'given': [u'*', u'\u5047\u5982<'],
           'name': [u'Chinese simplified'],
           'native': [u'\u7b80\u4f53\u4e2d\u6587'],
           'scenario': [u'\u573a\u666f'],
           'scenario_outline': [u'\u573a\u666f\u5927\u7eb2'],
           'then': [u'*', u'\u90a3\u4e48<'],
           'when': [u'*', u'\u5f53<']},
 'zh-TW': {'and': [u'*', u'\u800c\u4e14<', u'\u4e26\u4e14<'],
           'background': [u'\u80cc\u666f'],
           'but': [u'*', u'\u4f46\u662f<'],
           'examples': [u'\u4f8b\u5b50'],
           'feature': [u'\u529f\u80fd'],
           'given': [u'*', u'\u5047\u8a2d<'],
           'name': [u'Chinese traditional'],
           'native': [u'\u7e41\u9ad4\u4e2d\u6587'],
           'scenario': [u'\u5834\u666f', u'\u5287\u672c'],
           'scenario_outline': [u'\u5834\u666f\u5927\u7db1',
                                u'\u5287\u672c\u5927\u7db1'],
           'then': [u'*', u'\u90a3\u9ebc<'],
           'when': [u'*', u'\u7576<']}}
