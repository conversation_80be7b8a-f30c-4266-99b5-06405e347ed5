# Mappings of Browserforge fingerprints to Camoufox config properties.

navigator:
  # Note: Browserforge tends to have outdated UAs.
  # The version will be replaced in Camoufox.
  userAgent: navigator.userAgent
  # userAgentData not in Firefox
  doNotTrack: navigator.doNotTrack
  appCodeName: navigator.appCodeName
  appName: navigator.appName
  appVersion: navigator.appVersion
  oscpu: navigator.oscpu
  # webdriver is always True
  # Locale is now implemented separately:
  #   language: navigator.language
  #   languages: navigator.languages
  platform: navigator.platform
  # deviceMemory not in Firefox
  hardwareConcurrency: navigator.hardwareConcurrency
  product: navigator.product
  # Never override productSub #105
  # productSub: navigator.productSub
  # vendor is not necessary
  # vendorSub is not necessary
  maxTouchPoints: navigator.maxTouchPoints
  extraProperties:
    # Note: Changing pdfViewerEnabled is not recommended. This will be kept to True.
    globalPrivacyControl: navigator.globalPrivacyControl

screen:
  # hasHDR is not implemented in Camoufox
  availLeft: screen.availLeft
  availTop: screen.availTop
  availWidth: screen.availWidth
  availHeight: screen.availHeight
  height: screen.height
  width: screen.width
  colorDepth: screen.colorDepth
  pixelDepth: screen.pixelDepth
  # devicePixelRatio is not recommended. Any value other than 1.0 is suspicious.
  pageXOffset: screen.pageXOffset
  pageYOffset: screen.pageYOffset
  outerHeight: window.outerHeight
  outerWidth: window.outerWidth
  innerHeight: window.innerHeight
  innerWidth: window.innerWidth
  screenX: window.screenX
  screenY: window.screenY
  # Tends to generate out of bounds (network inconsistencies):
  #   clientWidth: document.body.clientWidth
  #   clientHeight: document.body.clientHeight

# videoCard:
#  renderer: webgl:renderer
#  vendor: webgl:vendor

headers:
  # headers.User-Agent is redundant with navigator.userAgent
  # headers.Accept-Language is redundant with locale:*
  Accept-Encoding: headers.Accept-Encoding

battery:
  charging: battery:charging
  chargingTime: battery:chargingTime
  dischargingTime: battery:dischargingTime

# Unsupported: videoCodecs, audioCodecs, pluginsData, multimediaDevices
# Fonts are listed through the launcher.
