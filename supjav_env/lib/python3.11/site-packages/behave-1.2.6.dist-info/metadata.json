{"classifiers": ["Development Status :: 4 - Beta", "Environment :: Console", "Intended Audience :: <PERSON><PERSON><PERSON>", "Operating System :: OS Independent", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.6", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: 3.5", "Programming Language :: Python :: 3.6", "Programming Language :: Python :: Implementation :: CPython", "Programming Language :: Python :: Implementation :: Jython", "Programming Language :: Python :: Implementation :: PyPy", "Topic :: Software Development :: Testing", "License :: OSI Approved :: BSD License"], "description_content_type": "UNKNOWN", "extensions": {"python.commands": {"wrap_console": {"behave": "behave.__main__:main"}}, "python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "http://github.com/behave/behave"}}, "python.exports": {"console_scripts": {"behave": "behave.__main__:main"}, "distutils.commands": {"behave_test": "setuptools_behave:behave_test"}}}, "extras": ["develop", "docs"], "generator": "bdist_wheel (0.30.0)", "license": "BSD", "metadata_version": "2.0", "name": "behave", "provides": "behave", "requires_python": ">=2.6, !=3.0.*, !=3.1.*, !=3.2.*", "run_requires": [{"extra": "develop", "requires": ["coverage", "invoke (>=0.21.0)", "modernize (>=0.5)", "path.py (>=8.1.2)", "pathlib", "pycmd", "pylint", "pytest (>=3.0)", "pytest-cov", "tox"]}, {"requires": ["parse (>=1.8.2)", "parse-type (>=0.4.2)", "six (>=1.11)"]}, {"extra": "docs", "requires": ["sphinx (>=1.6)", "sphinx-bootstrap-theme (>=0.6)"]}, {"environment": "python_version < \"2.7\"", "requires": ["<PERSON><PERSON><PERSON><PERSON>", "importlib", "ordereddict"]}, {"environment": "python_version < \"3.0\"", "requires": ["traceback2"]}, {"environment": "python_version < \"3.4\"", "requires": ["enum34"]}], "summary": "behave is behaviour-driven development, Python style", "test_requires": [{"requires": ["PyHamcrest (>=1.8)", "mock (>=1.1)", "nose (>=1.3)", "path.py (>=10.1)", "pytest (>=3.0)"]}], "version": "1.2.6"}