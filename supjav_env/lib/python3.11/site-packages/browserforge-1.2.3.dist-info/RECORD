browserforge-1.2.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
browserforge-1.2.3.dist-info/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
browserforge-1.2.3.dist-info/METADATA,sha256=hjOc3Tc0kVLnl5FF7iNRXGfuS14FubS34zkrE4bJ_44,28964
browserforge-1.2.3.dist-info/RECORD,,
browserforge-1.2.3.dist-info/WHEEL,sha256=IYZQI976HJqqOpQU6PHkJ8fb3tMNBFjg-Cn-pwAbaFM,88
browserforge/__main__.py,sha256=INeXya8RI5aSET4mhpwxymdvJ1VJbFGJRCA7KZi2Nxw,879
browserforge/__pycache__/__main__.cpython-311.pyc,,
browserforge/__pycache__/bayesian_network.cpython-311.pyc,,
browserforge/__pycache__/download.cpython-311.pyc,,
browserforge/bayesian_network.py,sha256=wGE3Tpdpoaswq6ObNft21toOKmD_R-cas-OJuDyiZkw,10676
browserforge/download.py,sha256=-QxBU8XHHL2EQ-DDNzYKlokhDoJQjSdxinlt6jVajeg,4683
browserforge/fingerprints/__init__.py,sha256=7oMFpQKzJv49giKjUfmvxVAtVKALoV9qJXzjrG-eWdk,458
browserforge/fingerprints/__pycache__/__init__.cpython-311.pyc,,
browserforge/fingerprints/__pycache__/generator.cpython-311.pyc,,
browserforge/fingerprints/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
browserforge/fingerprints/data/__pycache__/__init__.cpython-311.pyc,,
browserforge/fingerprints/generator.py,sha256=eYEk-OcCy1Y19sKKRcA_vV3v3t9Cu5SfgUhtJGcCrKo,12848
browserforge/headers/__init__.py,sha256=sr5xEP_3ELQ62KkP3AG2ShK-D6z1kQ2lMtEf21COo54,191
browserforge/headers/__pycache__/__init__.cpython-311.pyc,,
browserforge/headers/__pycache__/generator.cpython-311.pyc,,
browserforge/headers/__pycache__/utils.cpython-311.pyc,,
browserforge/headers/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
browserforge/headers/data/__pycache__/__init__.cpython-311.pyc,,
browserforge/headers/generator.py,sha256=5-aP-b4m4JWib2qZwyOWR1wYNre9nj1PTAxiOedlMrQ,20954
browserforge/headers/utils.py,sha256=VCzKnHwExvH2J9aMGQuQLhPpVRW_1kG_mKmW_xTUqfs,1293
browserforge/injectors/__init__.py,sha256=7Rm61zLv75HByr03XlwSkoP0Dhwr-ne1z39qFE1f7r8,20
browserforge/injectors/__pycache__/__init__.cpython-311.pyc,,
browserforge/injectors/__pycache__/utils.cpython-311.pyc,,
browserforge/injectors/data/__init__.py,sha256=Iti51gNM2tCJpm77bdtvfb4Z8Qj0mO6eSkH7emcYDKg,315
browserforge/injectors/data/__pycache__/__init__.cpython-311.pyc,,
browserforge/injectors/data/utils.js.xz,sha256=B4d-ri6I85ASXTHSHt8jT8ak7R0zP8xjCSSmE0dy7Ao,6512
browserforge/injectors/playwright/__init__.py,sha256=EhcXj5gGa2kE05l1Yw_hadTaK-rv4jBdg8-VTpl51xY,141
browserforge/injectors/playwright/__pycache__/__init__.cpython-311.pyc,,
browserforge/injectors/playwright/__pycache__/injector.cpython-311.pyc,,
browserforge/injectors/playwright/injector.py,sha256=saImoK1N3mr3cNZPXCKUsj2AuV8mvT_cdvqspmVbvJE,3493
browserforge/injectors/pyppeteer/__init__.py,sha256=0d2AmQ5zNLxUaWaUWm4n1vtlJsyzEJ1BtNde9ehXV8s,120
browserforge/injectors/pyppeteer/__pycache__/__init__.cpython-311.pyc,,
browserforge/injectors/pyppeteer/__pycache__/injector.cpython-311.pyc,,
browserforge/injectors/pyppeteer/injector.py,sha256=RU1AKQ574oJFsM1tSh0yfex5bnm2DVfol34tYiIPnK0,2391
browserforge/injectors/undetected_playwright/__init__.py,sha256=TNCSiSWQwTP-2F6P9dIz-owxdfDrq-zsDgBVZjQCrcc,305
browserforge/injectors/undetected_playwright/__pycache__/__init__.cpython-311.pyc,,
browserforge/injectors/undetected_playwright/__pycache__/injector.cpython-311.pyc,,
browserforge/injectors/undetected_playwright/injector.py,sha256=ZOgK8ZG35nNz5t3BshpDEz1txZ-Vpd9MttvCvn3-tD8,3480
browserforge/injectors/utils.py,sha256=Vcn2mNht0g6nd5XUKXgW3073jyamEh9VSrr8FBABOo4,4066
browserforge/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
