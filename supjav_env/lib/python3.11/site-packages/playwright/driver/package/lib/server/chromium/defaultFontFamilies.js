"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var defaultFontFamilies_exports = {};
__export(defaultFontFamilies_exports, {
  platformToFontFamilies: () => platformToFontFamilies
});
module.exports = __toCommonJS(defaultFontFamilies_exports);
const platformToFontFamilies = {
  "linux": {
    "fontFamilies": {
      "standard": "Times New Roman",
      "fixed": "Monospace",
      "serif": "Times New Roman",
      "sansSerif": "Arial",
      "cursive": "Comic Sans MS",
      "fantasy": "Impact"
    }
  },
  "mac": {
    "fontFamilies": {
      "standard": "Times",
      "fixed": "Courier",
      "serif": "Times",
      "sansSerif": "Helvetica",
      "cursive": "Apple Chancery",
      "fantasy": "Papyrus"
    },
    "forScripts": [
      {
        "script": "jpan",
        "fontFamilies": {
          "standard": "Hiragino Kaku Gothic ProN",
          "fixed": "Osaka-Mono",
          "serif": "Hiragino Mincho ProN",
          "sansSerif": "Hiragino Kaku Gothic ProN"
        }
      },
      {
        "script": "hang",
        "fontFamilies": {
          "standard": "Apple SD Gothic Neo",
          "serif": "AppleMyungjo",
          "sansSerif": "Apple SD Gothic Neo"
        }
      },
      {
        "script": "hans",
        "fontFamilies": {
          "standard": ",PingFang SC,STHeiti",
          "serif": "Songti SC",
          "sansSerif": ",PingFang SC,STHeiti",
          "cursive": "Kaiti SC"
        }
      },
      {
        "script": "hant",
        "fontFamilies": {
          "standard": ",PingFang TC,Heiti TC",
          "serif": "Songti TC",
          "sansSerif": ",PingFang TC,Heiti TC",
          "cursive": "Kaiti TC"
        }
      }
    ]
  },
  "win": {
    "fontFamilies": {
      "standard": "Times New Roman",
      "fixed": "Consolas",
      "serif": "Times New Roman",
      "sansSerif": "Arial",
      "cursive": "Comic Sans MS",
      "fantasy": "Impact"
    },
    "forScripts": [
      {
        "script": "cyrl",
        "fontFamilies": {
          "standard": "Times New Roman",
          "fixed": "Courier New",
          "serif": "Times New Roman",
          "sansSerif": "Arial"
        }
      },
      {
        "script": "arab",
        "fontFamilies": {
          "fixed": "Courier New",
          "sansSerif": "Segoe UI"
        }
      },
      {
        "script": "grek",
        "fontFamilies": {
          "standard": "Times New Roman",
          "fixed": "Courier New",
          "serif": "Times New Roman",
          "sansSerif": "Arial"
        }
      },
      {
        "script": "jpan",
        "fontFamilies": {
          "standard": ",Meiryo,Yu Gothic",
          "fixed": "MS Gothic",
          "serif": ",Yu Mincho,MS PMincho",
          "sansSerif": ",Meiryo,Yu Gothic"
        }
      },
      {
        "script": "hang",
        "fontFamilies": {
          "standard": "Malgun Gothic",
          "fixed": "Gulimche",
          "serif": "Batang",
          "sansSerif": "Malgun Gothic",
          "cursive": "Gungsuh"
        }
      },
      {
        "script": "hans",
        "fontFamilies": {
          "standard": "Microsoft YaHei",
          "fixed": "NSimsun",
          "serif": "Simsun",
          "sansSerif": "Microsoft YaHei",
          "cursive": "KaiTi"
        }
      },
      {
        "script": "hant",
        "fontFamilies": {
          "standard": "Microsoft JhengHei",
          "fixed": "MingLiU",
          "serif": "PMingLiU",
          "sansSerif": "Microsoft JhengHei",
          "cursive": "DFKai-SB"
        }
      }
    ]
  }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  platformToFontFamilies
});
